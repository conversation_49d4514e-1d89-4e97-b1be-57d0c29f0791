# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
frontend/.next/
frontend/out/

# Production
/build

# Misc
.DS_Store
*.pem
.env*
!.env.example

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ruby/Rails
backend/*.rbc
backend/capybara-*.html
backend/.rspec
backend/db/*.sqlite3
backend/db/*.sqlite3-journal
backend/db/*.sqlite3-[0-9]*
backend/public/system
backend/coverage/
backend/spec/tmp
backend/*.orig
backend/rerun.txt
backend/pickle-email-*.html

# Ignore all logfiles and tempfiles
backend/log/*
backend/tmp/*
!/backend/log/.keep
!/backend/tmp/.keep

# Ignore pidfiles
backend/tmp/pids/*
!/backend/tmp/pids/
!/backend/tmp/pids/.keep

# Ignore uploaded files in development
backend/storage/*
!/backend/storage/.keep
backend/tmp/storage/*
!/backend/tmp/storage/
!/backend/tmp/storage/.keep

# Ignore master key for decrypting credentials
backend/config/master.key

# Environment variables
backend/.env*
!backend/.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo

# Added by Claude Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 