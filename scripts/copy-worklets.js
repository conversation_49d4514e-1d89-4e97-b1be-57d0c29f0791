/**
 * Copy Worklets Script
 * 
 * This script copies the compiled worklet files to the public directory
 * so they can be loaded by the browser.
 */

const fs = require('fs');
const path = require('path');

// Paths
const WORKLET_SRC_DIR = path.join(__dirname, '../frontend/src/audio/worklets');
const WORKER_SRC_DIR = path.join(__dirname, '../frontend/src/audio/workers');
const PUBLIC_DIR = path.join(__dirname, '../frontend/public');

// Create directories if they don't exist
if (!fs.existsSync(PUBLIC_DIR)) {
  fs.mkdirSync(PUBLIC_DIR, { recursive: true });
}

// Copy worklet files
function copyWorklets() {
  console.log('Copying worklet files to public directory...');
  
  // Get all worklet files
  const workletFiles = fs.readdirSync(WORKLET_SRC_DIR).filter(file => file.endsWith('.worklet.ts'));
  
  // Process each worklet file
  workletFiles.forEach(file => {
    const srcPath = path.join(WORKLET_SRC_DIR, file);
    const destPath = path.join(PUBLIC_DIR, file.replace('.ts', '.js'));
    
    // Read the file
    const content = fs.readFileSync(srcPath, 'utf8');
    
    // Simple TypeScript to JavaScript conversion
    // This is a very basic conversion and won't handle complex TypeScript features
    let jsContent = content
      // Remove type annotations
      .replace(/: \w+(\[\])?([ ]*\|[ ]*\w+(\[\])?)*( \| null)?/g, '')
      // Remove interface declarations
      .replace(/interface [^{]+{[^}]+}/g, '')
      // Remove type imports
      .replace(/import type[^;]+;/g, '')
      // Remove type parameters
      .replace(/<[^>]+>/g, '')
      // Remove private field declarations
      .replace(/private [^:]+: [^;]+;/g, '')
      // Convert private fields to regular fields
      .replace(/private ([^:]+):/g, '$1:');
    
    // Write the file
    fs.writeFileSync(destPath, jsContent);
    console.log(`Copied ${file} to ${destPath}`);
  });
}

// Copy worker files
function copyWorkers() {
  console.log('Copying worker files to public directory...');
  
  // Get all worker files
  const workerFiles = fs.readdirSync(WORKER_SRC_DIR).filter(file => file.endsWith('.worker.ts'));
  
  // Process each worker file
  workerFiles.forEach(file => {
    const srcPath = path.join(WORKER_SRC_DIR, file);
    const destPath = path.join(PUBLIC_DIR, file.replace('.ts', '.js'));
    
    // Read the file
    const content = fs.readFileSync(srcPath, 'utf8');
    
    // Simple TypeScript to JavaScript conversion
    // This is a very basic conversion and won't handle complex TypeScript features
    let jsContent = content
      // Remove type annotations
      .replace(/: \w+(\[\])?([ ]*\|[ ]*\w+(\[\])?)*( \| null)?/g, '')
      // Remove interface declarations
      .replace(/interface [^{]+{[^}]+}/g, '')
      // Remove type imports
      .replace(/import type[^;]+;/g, '')
      // Remove type parameters
      .replace(/<[^>]+>/g, '')
      // Remove private field declarations
      .replace(/private [^:]+: [^;]+;/g, '')
      // Convert private fields to regular fields
      .replace(/private ([^:]+):/g, '$1:');
    
    // Write the file
    fs.writeFileSync(destPath, jsContent);
    console.log(`Copied ${file} to ${destPath}`);
  });
}

// Run the script
try {
  copyWorklets();
  copyWorkers();
  console.log('Done!');
} catch (error) {
  console.error('Error copying worklet files:', error);
  process.exit(1);
}
