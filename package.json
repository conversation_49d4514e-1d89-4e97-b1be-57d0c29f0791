{"name": "freestyle-app", "version": "1.0.0", "description": "Freestyle AI - A web application for beat production and freestyle rap with AI assistance", "scripts": {"kill-ports": "kill -9 $(lsof -ti:3000) 2>/dev/null || true && kill -9 $(lsof -ti:3001) 2>/dev/null || true && kill -9 $(lsof -ti:4000) 2>/dev/null || true", "frontend": "cd frontend && npm run dev", "backend": "cd backend/rails && rm -f tmp/pids/server.pid && bin/rails server -p 3001", "websocket": "cd backend/websocket && npx ts-node server.ts", "dev": "npm run kill-ports && npm run copy-worklets && concurrently \"npm run frontend\" \"npm run backend\"", "dev:all": "npm run kill-ports && npm run copy-worklets && concurrently -k -n FRONTEND,BACKEND,WEBSOCKET \"npm run frontend\" \"npm run backend\" \"npm run websocket\"", "dev:frontend": "npm run kill-ports && npm run copy-worklets && npm run frontend", "dev:backend": "npm run kill-ports && npm run backend", "dev:websocket": "npm run kill-ports && npm run websocket", "e2e": "wait-on http://localhost:3000/healthz http://localhost:3001/up http://localhost:4000/healthz && cd frontend && npx cypress run --browser chrome", "install:all": "cd frontend && npm install && cd ../backend/rails && bundle install && cd ../websocket && npm install", "test": "concurrently \"cd frontend && npm test\" \"cd backend/rails && rails test\"", "copy-worklets": "node scripts/copy-worklets.js"}, "keywords": ["freestyle", "rap", "ai", "music", "beats"], "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.26.0", "@tailwindcss/postcss": "^4.1.6", "@testing-library/cypress": "^10.0.3", "@types/chai": "^5.2.2", "@types/cypress": "^1.1.6", "@types/dom-speech-recognition": "^0.0.6", "@types/jest": "^29.5.14", "@types/jquery": "^3.5.32", "@types/mocha": "^10.0.10", "@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "concurrently": "^9.1.2", "cypress": "^14.3.3", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.1.0", "jest-canvas-mock": "^2.5.2", "msw": "^2.8.2", "postcss": "^8.5.3", "sinon": "^20.0.0", "tailwindcss": "^4.1.6", "ts-jest": "^29.3.2", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "wait-on": "^8.0.3", "whatwg-fetch": "^3.6.20"}, "dependencies": {"@mswjs/interceptors": "^0.38.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@wavesurfer/react": "^1.0.11", "analytics": "^0.8.16", "audio-buffer-utils": "^5.1.2", "axios": "^1.9.0", "browser-fs-access": "^0.37.0", "comlink": "^4.4.2", "file-saver": "^2.0.5", "jest": "^29.7.0", "prettier": "^3.5.3", "recorder-js": "^1.0.7", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "task-master-ai": "^0.13.2", "wavesurfer.js": "^7.9.5", "web-audio-engine": "^0.13.4", "worklet-loader": "^2.0.0", "zustand": "^5.0.4"}}