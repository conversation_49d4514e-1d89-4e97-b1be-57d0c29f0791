# Audio Components

This document provides context for all audio-related components in the Freestyle App.

## Component Hierarchy

```
RecordingSystem
├── FreestyleRecorder (for recording)
└── RecordingPreviewSystem (for preview)
    └── PreviewPlayer
        └── WaveformVisualizer
```

## Core Components

### RecordingSystem

**Path**: `frontend/src/audio/components/RecordingSystem.tsx`

**Purpose**: Main controller component for the recording experience. Manages the state transitions between recording and preview modes.

**Responsibilities**:
- Initializes audio recording
- Handles microphone input selection
- Manages recording state (idle, recording, preview)
- Coordinates between recording and preview components

**Key Props**:
- `onRecordingComplete`: Callback when recording is finished
- `onPreviewComplete`: Callback when preview is finished
- `initialBeat`: Optional beat to start with

**State Management**:
- Uses `audioStore` for recording state
- Uses `beatStore` for beat selection

**Child Components**:
- `FreestyleRecorder`: For the actual recording functionality
- `RecordingPreviewSystem`: For preview functionality

---

### FreestyleRecorder

**Path**: `frontend/src/audio/components/FreestyleRecorder.tsx`

**Purpose**: Handles the actual audio recording functionality.

**Responsibilities**:
- Captures microphone input
- Synchronizes recording with beat playback
- Processes audio data for preview
- Visualizes audio input

**Key Props**:
- `onRecordingComplete`: Callback when recording is finished
- `beatUrl`: URL of the selected beat

**Hooks Used**:
- `useAudioRecording`: For recording functionality
- `useWaveform`: For waveform visualization

---

### PreviewPlayer

**Path**: `frontend/src/audio/components/PreviewPlayer.tsx`

**Purpose**: Plays back the recorded freestyle with the beat.

**Responsibilities**:
- Plays the mixed audio (vocals + beat)
- Displays waveform visualization
- Provides playback controls
- Enables downloading the recording

**Key Props**:
- `onRecordAgain`: Callback to restart recording
- `autoPlay`: Whether to automatically play the recording

**State Management**:
- Uses `audioStore` for mixed audio data
- Uses `beatStore` for beat information

**Hooks Used**:
- `useAudioPlayback`: For audio playback functionality

**Child Components**:
- `WaveformVisualizer`: For waveform visualization

---

### WaveformVisualizer

**Path**: `frontend/src/ui/visualizers/WaveformVisualizer.tsx`

**Purpose**: Visualizes audio data as a waveform.

**Responsibilities**:
- Renders waveform from audio data
- Displays playhead position
- Handles seek interactions
- Applies visual styling with gradients

**Key Props**:
- `audioBuffer`: Audio data to visualize
- `audioUrl`: Alternative URL source for audio
- `currentTime`: Current playback position
- `duration`: Total duration of audio
- `onSeek`: Callback for seeking
- `gradientColors`: Colors for waveform gradient

**Hooks Used**:
- `useWaveform`: For waveform generation and drawing

---

### TransportControls

**Path**: `frontend/src/audio/components/TransportControls.tsx`

**Purpose**: Provides playback controls for audio.

**Responsibilities**:
- Play/pause functionality
- Time display
- Seek controls

**Key Props**:
- `isPlaying`: Whether audio is currently playing
- `currentTime`: Current playback position
- `duration`: Total duration of audio
- `onPlayPause`: Callback for play/pause
- `onSeek`: Callback for seeking

---

## UI Components

### MicVinylIntegrated

**Path**: `frontend/src/ui/visualizers/MicVinylIntegrated.tsx`

**Purpose**: Main visual component for the recording interface, combining a microphone and vinyl record visualization.

**Responsibilities**:
- Displays a vinyl record with a microphone in the center
- Animates based on recording state
- Handles click interactions for recording

**Key Props**:
- `isRecording`: Whether recording is active
- `onClick`: Callback for microphone click
- `onVinylClick`: Callback for vinyl (outer part) click

**Visual States**:
- Idle: Static vinyl with microphone
- Recording: Animated vinyl rotation with pulsing effects
- Preview: Different animation state for playback

---

### TonearmComponent

**Path**: `frontend/src/ui/visualizers/TonearmComponent.tsx`

**Purpose**: Visual component that displays a tonearm for the vinyl record.

**Responsibilities**:
- Renders a realistic tonearm visualization
- Animates based on playback state

**Key Props**:
- `isPlaying`: Whether audio is playing
- `position`: Position of the tonearm (0-1)

---

## Component Interactions

1. **Recording Flow**:
   - `RecordingSystem` initializes the recording environment
   - `FreestyleRecorder` handles the actual recording
   - `MicVinylIntegrated` provides the visual interface
   - When recording completes, audio is processed and sent to preview

2. **Preview Flow**:
   - `RecordingPreviewSystem` manages the preview state
   - `PreviewPlayer` handles playback of the mixed audio
   - `WaveformVisualizer` displays the audio waveform
   - User can download or record again

## Implementation Notes

- All waveform generation is centralized in `waveformGenerator.ts`
- Audio processing uses Web Audio API
- Components use React's functional component pattern with hooks
- State is managed through Zustand stores for global state
