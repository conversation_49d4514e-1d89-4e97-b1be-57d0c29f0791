# Waveform Visualization

This document provides detailed context for the waveform visualization system in the Freestyle App.

## Overview

The waveform visualization system is responsible for rendering audio data as visual waveforms. It's used in both the recording and preview modes to provide visual feedback to the user.

## Key Components

### WaveformVisualizer

**Path**: `frontend/src/ui/visualizers/WaveformVisualizer.tsx`

**Purpose**: Main component for visualizing audio waveforms.

**Responsibilities**:
- Renders waveform from audio data
- Displays playhead position
- Handles seek interactions
- Applies visual styling with gradients

**Key Props**:
```typescript
interface WaveformVisualizerProps {
  audioBuffer?: AudioBuffer | null;
  audioUrl?: string | null;
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onSeek?: (time: number) => void;
  height?: number;
  gradientColors?: string[];
  waveformKey?: string;
  className?: string;
  style?: React.CSSProperties;
}
```

**Implementation Details**:
- Uses the `useWaveform` hook for waveform generation and drawing
- Uses two canvas elements: one for the waveform and one for the playhead
- Implements memoization to prevent unnecessary re-renders
- Handles loading and error states

**Usage Example**:
```jsx
<WaveformVisualizer
  audioBuffer={mixedBuffer}
  currentTime={currentTime}
  duration={duration}
  isPlaying={isPlaying}
  onSeek={handleSeek}
  height={128}
  gradientColors={["#5f6fff", "#7a6fff", "#b367ff", "#d45fff", "#ff5f7e", "#ff7a5f"]}
/>
```

## Key Hooks

### useWaveform

**Path**: `frontend/src/audio/hooks/useWaveform.ts`

**Purpose**: Provides waveform visualization functionality.

**Responsibilities**:
- Processes audio data to generate waveform peaks
- Handles responsive sizing
- Manages canvas drawing
- Handles user interactions (seeking)

**Parameters**:
```typescript
interface UseWaveformOptions {
  audioUrl?: string | Blob | null;
  audioBuffer?: AudioBuffer | null;
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onSeek?: (time: number) => void;
  height?: number;
  gradientColors?: string[];
  waveformKey?: string;
}
```

**Returns**:
```typescript
{
  waveformCanvasRef: React.RefObject<HTMLCanvasElement>;
  playheadCanvasRef: React.RefObject<HTMLCanvasElement>;
  containerRef: React.RefObject<HTMLDivElement>;
  loading: boolean;
  error: string | null;
  handleSeekClick: (e: React.MouseEvent<HTMLDivElement>) => void;
}
```

**Implementation Details**:
- Uses `waveformGenerator.ts` utilities for peak generation
- Implements caching for performance optimization
- Prevents duplicate waveform generation and drawing
- Handles responsive sizing with ResizeObserver
- Draws waveform and playhead on separate canvases
- Supports high-DPI displays
- Optimizes canvas resizing to prevent unnecessary redraws
- Tracks rendering state to avoid duplicate processing
- Consolidated from duplicate implementations into a single source of truth
- Replaced the legacy Waveform component throughout the codebase

## Utility Functions

### waveformGenerator

**Path**: `frontend/src/audio/utils/waveformGenerator.ts`

**Purpose**: Generates waveform data for visualization.

**Key Functions**:

#### `generatePeaksFromBuffer(buffer: AudioBuffer, numPoints: number, options?: object)`
Generates peaks from an AudioBuffer.

```typescript
function generatePeaksFromBuffer(
  buffer: AudioBuffer,
  numPoints: number,
  options?: {
    enhanceFactor?: number;
    prioritizeVocals?: boolean;
  }
): Float32Array
```

#### `generatePeaksFromUrl(audioUrl: string | Blob | null, numPoints: number, options?: object)`
Generates peaks from an audio URL or Blob.

```typescript
async function generatePeaksFromUrl(
  audioUrl: string | Blob | null,
  numPoints: number,
  options?: {
    enhanceFactor?: number;
    prioritizeVocals?: boolean;
    cacheKey?: string;
  }
): Promise<Float32Array>
```

#### `generateDummyPeaks(numPoints: number, options?: object)`
Generates dummy waveform data for fallback.

```typescript
function generateDummyPeaks(
  numPoints: number,
  options?: {
    enhanceFactor?: number;
    addNoise?: boolean;
  }
): Float32Array
```

**Implementation Details**:
- Central source of truth for all waveform generation
- Includes normalization and enhancement for better visualization
- Provides caching for performance optimization
- Handles error cases with fallbacks

## Waveform Drawing Process

The waveform drawing process follows these steps:

1. **Peak Generation**:
   - Audio data is processed to extract peak values
   - Peaks are normalized to ensure consistent visualization
   - Enhancement is applied to make smaller values more visible

2. **Canvas Setup**:
   - Canvas dimensions are set based on container size
   - High-DPI scaling is applied for sharp rendering
   - Gradient is created for visual styling

3. **Waveform Drawing**:
   - Peaks are drawn as a filled path
   - Top half represents positive values
   - Bottom half mirrors the top half
   - Gradient fill is applied

4. **Playhead Drawing**:
   - Playhead position is calculated based on currentTime/duration
   - Playhead is drawn as a vertical line
   - Throttling is applied to reduce unnecessary redraws

5. **Interaction Handling**:
   - Click events are captured on the container
   - Click position is converted to a time value
   - onSeek callback is called with the new time

## Visual Styling

The waveform visualization supports the following visual styling options:

- **Height**: Controls the height of the waveform container
- **Gradient Colors**: Array of colors for the waveform gradient
- **Custom Styling**: Additional CSS styles can be applied via className and style props

Default gradient colors:
```javascript
["#5f6fff", "#7a6fff", "#b367ff", "#d45fff", "#ff5f7e", "#ff7a5f"]
```

## Performance Optimizations

The waveform visualization includes several performance optimizations:

1. **Caching**:
   - Generated peaks are cached using a unique key
   - Cached peaks are reused when the same audio is visualized again
   - A global cache is maintained in the window object to prevent duplicate generation

2. **Duplicate Prevention**:
   - Tracks if waveform generation has already run for a specific audio source
   - Skips generation if the same audio source is processed multiple times
   - Uses a unique key based on audio source properties

3. **Memoization**:
   - The WaveformVisualizer component is memoized to prevent unnecessary re-renders
   - Custom equality function allows small changes in currentTime without re-rendering

4. **Throttling**:
   - Playhead updates are throttled to reduce CPU usage
   - Only updates every ~50ms (about 20fps) which is smooth enough for playhead
   - Skips updates if the position hasn't changed significantly

5. **Responsive Sizing**:
   - ResizeObserver is used for accurate container size tracking
   - Canvas dimensions are updated only when necessary

6. **Conditional Rendering**:
   - Skips waveform generation if the container isn't ready yet
   - Tracks if a waveform has already been drawn to prevent duplicate drawing

7. **High-DPI Support**:
   - Canvas dimensions are scaled based on device pixel ratio
   - Drawing operations are scaled to match
   - Canvas is only resized if dimensions have changed

## Implementation Notes

### Canvas Usage

The waveform visualization uses two canvas elements:

1. **Waveform Canvas**:
   - Renders the waveform itself
   - Only redrawn when peaks or gradient colors change

2. **Playhead Canvas**:
   - Renders the playhead position
   - Redrawn when currentTime changes

This separation allows for efficient updates, as the playhead can be redrawn without redrawing the entire waveform.

### Error Handling

The waveform visualization includes robust error handling:

- If audio data cannot be processed, dummy peaks are generated
- If the canvas cannot be drawn, an error state is displayed
- If the audio URL cannot be fetched, a fallback is provided

### Accessibility

The waveform visualization includes accessibility considerations:

- Keyboard navigation for seeking
- ARIA attributes for screen readers
- Visual feedback for interaction states

## Best Practices

When using the waveform visualization system:

1. **Provide Both Sources**: When possible, provide both audioBuffer and audioUrl for optimal performance
2. **Use Unique Keys**: Provide a unique waveformKey when the audio source changes
3. **Handle Errors**: Display appropriate messages when errors occur
4. **Responsive Design**: Ensure the container has a defined width and height
5. **Performance**: Be mindful of the number of waveform visualizations on a page

## Troubleshooting

Common issues with waveform visualization:

1. **Empty Waveform / Dummy Peaks**:
   - If the waveform appears as a generic pattern rather than representing the actual audio, the system is using fallback dummy peaks
   - This happens when the AudioBuffer contains all zeros or very small values
   - Check that the audio mixing process is generating valid data
   - Ensure the recording process is capturing audio correctly
   - Look for the warning "Generated all-zero peaks, falling back to dummy peaks" in the console
   - The system now validates audio data and provides detailed logging to help diagnose these issues

2. **Playhead Sync Issues**:
   - If the playhead doesn't sync with the audio, ensure the currentTime is being updated correctly
   - Check that the audio element's timeupdate event is firing
   - Verify that the currentTime is being passed to the WaveformVisualizer component

3. **Performance Issues**:
   - For large audio files, consider downsampling the waveform data to improve performance
   - The system uses caching to prevent regenerating waveforms unnecessarily
   - Use a stable waveformKey to enable proper caching

4. **Responsive Issues**:
   - If the waveform doesn't resize correctly, check that the ResizeObserver is working properly
   - Ensure the container has a defined width and height
