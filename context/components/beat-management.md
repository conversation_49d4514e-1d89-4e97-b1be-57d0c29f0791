# Beat Management

This document provides detailed context for the beat management system in the Freestyle App.

## Overview

The beat management system is responsible for loading, selecting, and playing beats (instrumentals) for freestyle rap sessions. It's a core part of the freestyle experience.

## Beat Store

### beatStore

**Path**: `frontend/src/stores/beatStore.ts`

**Purpose**: Manages the state related to beats.

**State Structure**:
```typescript
interface BeatState {
  beats: Beat[];
  currentBeat: Beat | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchBeats: () => Promise<void>;
  setCurrentBeat: (beat: Beat) => void;
  clearCurrentBeat: () => void;
}
```

**Key Actions**:
- `fetchBeats`: Loads the available beats from the backend or local storage
- `setCurrentBeat`: Sets the currently selected beat
- `clearCurrentBeat`: Clears the current beat selection

**Beat Model**:
```typescript
interface Beat {
  id: string;
  title: string;
  artist: string;
  bpm: number;
  key: string;
  duration: number;
  audio_url: string;
  image_url?: string;
  tags?: string[];
  attributes?: {
    [key: string]: any;
  };
}
```

**Persistence**:
- Beat selection is persisted in localStorage to survive page reloads
- Beat data is cached for performance

**Usage Example**:
```jsx
const { currentBeat, setCurrentBeat, beats } = useBeatStore();
```

## Beat Components

### BeatLibrary

**Path**: `frontend/src/components/BeatLibrary.tsx`

**Purpose**: Displays a collection of beats for selection.

**Responsibilities**:
- Displays available beats in a grid or list
- Provides filtering and sorting options
- Handles beat selection
- Allows beat preview

**Key Props**:
```typescript
interface BeatLibraryProps {
  onBeatSelect: (beat: Beat) => void;
  selectedBeat: Beat | null;
  className?: string;
}
```

**Visual Elements**:
- Beat Cards: Visual representation of beats
- Filters: Controls for filtering beats
- Sorting: Controls for sorting beats
- Search: Search input for finding beats

**Usage Example**:
```jsx
<BeatLibrary
  onBeatSelect={handleBeatSelect}
  selectedBeat={currentBeat}
/>
```

### BeatCard

**Path**: `frontend/src/components/BeatCard.tsx`

**Purpose**: Displays a single beat with preview functionality.

**Responsibilities**:
- Displays beat information
- Provides play/pause functionality for preview
- Indicates selection state
- Handles click events for selection

**Key Props**:
```typescript
interface BeatCardProps {
  beat: Beat;
  isSelected: boolean;
  onSelect: (beat: Beat) => void;
  onPreview: (beat: Beat) => void;
  isPlaying: boolean;
  className?: string;
}
```

**Visual Elements**:
- Title: Beat title
- Artist: Beat artist
- BPM: Beats per minute
- Key: Musical key
- Play Button: Button for previewing the beat
- Selection Indicator: Visual indicator for selected state

**Usage Example**:
```jsx
<BeatCard
  beat={beat}
  isSelected={selectedBeatId === beat.id}
  onSelect={handleBeatSelect}
  onPreview={handleBeatPreview}
  isPlaying={playingBeatId === beat.id}
/>
```

### BeatPlayer

**Path**: `frontend/src/audio/components/BeatPlayer.tsx`

**Purpose**: Plays beats with basic controls.

**Responsibilities**:
- Loads and plays beat audio
- Provides play/pause functionality
- Displays current time and duration
- Handles seeking

**Key Props**:
```typescript
interface BeatPlayerProps {
  beatUrl: string;
  autoPlay?: boolean;
  loop?: boolean;
  onEnded?: () => void;
  onError?: (error: string) => void;
  className?: string;
}
```

**Key Methods**:
- `play()`: Starts beat playback
- `pause()`: Pauses beat playback
- `stop()`: Stops beat playback and resets position
- `seek(time: number)`: Seeks to a specific position in the beat

**Usage Example**:
```jsx
<BeatPlayer
  beatUrl={currentBeat?.audio_url}
  autoPlay={true}
  loop={true}
  onEnded={handleBeatEnded}
/>
```

## Beat Selection Flow

The beat selection flow follows these steps:

1. **Beat Loading**:
   - Beats are loaded from the backend or local storage
   - Beat data is cached for performance

2. **Beat Browsing**:
   - User browses available beats in the BeatLibrary
   - Beats can be filtered, sorted, and searched

3. **Beat Preview**:
   - User can preview beats before selection
   - Preview plays a short segment of the beat

4. **Beat Selection**:
   - User selects a beat for recording
   - Selected beat is stored in the beatStore

5. **Beat Persistence**:
   - Selected beat is persisted in localStorage
   - Beat selection survives page reloads

## Beat Audio Processing

Beat audio is processed as follows:

1. **Loading**:
   - Beat audio is loaded from the provided URL
   - Audio is decoded into an AudioBuffer for precise timing

2. **Playback**:
   - Beat is played using Web Audio API
   - Playback can be controlled (play, pause, seek)

3. **Looping**:
   - Beat can be looped for continuous playback
   - Loop points can be adjusted for specific sections

4. **Mixing**:
   - Beat is mixed with vocals during recording
   - Beat volume can be adjusted relative to vocals

## Beat File Structure

Beat files are stored in the following location:

```
frontend/public/beats/
```

Each beat file follows this naming convention:

```
[artist]-[title]-[bpm]-[key].mp3
```

Example:
```
producer-supreme-88-cmin.mp3
```

## Beat Metadata

Beat metadata is stored in a JSON file:

```
frontend/public/beats/beats.json
```

Example metadata:
```json
[
  {
    "id": "beat-1",
    "title": "Supreme",
    "artist": "Producer",
    "bpm": 88,
    "key": "Cmin",
    "duration": 180,
    "audio_url": "/beats/producer-supreme-88-cmin.mp3",
    "image_url": "/images/beats/supreme.jpg",
    "tags": ["boom bap", "chill"],
    "attributes": {
      "mood": "relaxed",
      "energy": "medium"
    }
  }
]
```

## Beat Categorization

Beats are categorized by the following attributes:

- **BPM Range**:
  - Slow: < 85 BPM
  - Medium: 85-95 BPM
  - Fast: > 95 BPM

- **Musical Key**:
  - Major keys (C, G, D, etc.)
  - Minor keys (Am, Em, Bm, etc.)

- **Style**:
  - Boom Bap
  - Trap
  - Lo-Fi
  - Jazz
  - etc.

- **Mood**:
  - Chill
  - Energetic
  - Dark
  - Uplifting
  - etc.

## Implementation Notes

### Beat Loading

Beats are loaded using the Web Audio API:

```javascript
async function loadBeat(url) {
  const response = await fetch(url);
  const arrayBuffer = await response.arrayBuffer();
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
  return audioBuffer;
}
```

### Beat Playback

Beat playback is handled using the Web Audio API:

```javascript
function playBeat(audioBuffer) {
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  const source = audioContext.createBufferSource();
  source.buffer = audioBuffer;
  source.connect(audioContext.destination);
  source.start();
  return source;
}
```

### Beat Selection Persistence

Beat selection is persisted using localStorage:

```javascript
// Save selected beat
localStorage.setItem('selectedBeat', JSON.stringify(beat));

// Load selected beat
const savedBeat = JSON.parse(localStorage.getItem('selectedBeat'));
```

## Best Practices

When working with the beat management system:

1. **Preload Beats**: Preload beats before they are needed for smooth playback
2. **Cache Beat Data**: Cache beat data to reduce loading times
3. **Handle Errors**: Implement robust error handling for beat loading and playback
4. **Optimize File Size**: Optimize beat files for web delivery
5. **Provide Fallbacks**: Have fallback beats in case the selected beat fails to load
