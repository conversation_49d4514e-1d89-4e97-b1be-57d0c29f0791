# UI Components

This document provides detailed context for the UI components in the Freestyle App.

## Visual Theme

The Freestyle App uses a premium Hip-Hop Golden Era themed interface with the following color palette:

- **Deep Black**: `#050505` - Used for vinyl record
- **Champagne Gold**: `#E6C770` - Used for accents and highlights
- **Navy Blue**: `#0A1A2F` - Used for labels and secondary elements
- **Deep Blue Gradient**: `#0D1725` to `#050A14` - Used for backgrounds
- **Platinum/Silver**: `#A9ADB2` - Used for microphone and metallic elements

## Core UI Components

### MicVinylIntegrated

**Path**: `frontend/src/ui/visualizers/MicVinylIntegrated.tsx`

**Purpose**: Main visual component for the recording interface, combining a microphone and vinyl record visualization.

**Responsibilities**:
- Displays a vinyl record with a microphone in the center
- Animates based on recording state
- Handles click interactions for recording

**Key Props**:
```typescript
interface MicVinylIntegratedProps {
  isRecording: boolean;
  onClick: () => void;
  onVinylClick?: () => void;
  size?: number;
  className?: string;
}
```

**Visual Elements**:
- Vinyl Record: Deep black with champagne gold rim
- Label: Navy blue circular label
- Microphone: Platinum/silver vintage studio microphone
- Stand: Y-shaped microphone stand with metallic finish

**Animation States**:
- Idle: Static vinyl with subtle hover effects
- Recording: Animated vinyl rotation with pulsing effects
- Preview: Different animation state for playback

**Usage Example**:
```jsx
<MicVinylIntegrated
  isRecording={isRecording}
  onClick={handleMicClick}
  onVinylClick={handleVinylClick}
  size={300}
/>
```

### TonearmComponent

**Path**: `frontend/src/ui/visualizers/TonearmComponent.tsx`

**Purpose**: Visual component that displays a tonearm for the vinyl record.

**Responsibilities**:
- Renders a realistic tonearm visualization
- Animates based on playback state

**Key Props**:
```typescript
interface TonearmComponentProps {
  isPlaying: boolean;
  position?: number; // 0-1 range
  size?: number;
  className?: string;
}
```

**Visual Elements**:
- Tonearm: Metallic arm with realistic pivot
- Cartridge: Small element at the end of the arm
- Base: Circular base for the tonearm

**Animation States**:
- Idle: Tonearm in resting position
- Playing: Tonearm lowered onto vinyl
- Seeking: Tonearm moves to specific position

### CollapsiblePanel

**Path**: `frontend/src/ui/layout/CollapsiblePanel.tsx`

**Purpose**: Expandable panel for technical controls on the session page.

**Responsibilities**:
- Provides a collapsible container for audio controls and beat library
- Manages expanded/collapsed state
- Handles smooth transitions
- Manages tab navigation between audio controls and beat library

**Key Props**:
```typescript
interface CollapsiblePanelProps {
  // Panel state
  isCollapsed: boolean;
  toggleCollapse: () => void;

  // Audio controls props
  currentBeat: any;
  beatDuration: number;
  currentTime: number;
  beatVolume: number;
  setBeatVolume: (volume: number) => void;
  micGain: number;
  setMicGain: (gain: number) => void;
  availableMics: MediaDeviceInfo[];
  selectedMic: MediaDeviceInfo | null;
  setSelectedMic: (mic: MediaDeviceInfo) => void;
  beatAudioBuffer: AudioBuffer | null;

  // Beat library props
  beats: any[];
  onBeatSelect: (beat: any) => void;
  onBeatPreview: (beat: any) => void;
  isPreviewPlaying: boolean;
  stopPreview: () => void;
}
```

**Visual Elements**:
- Toggle Button: Button to expand/collapse the panel
- Tab Navigation: Tabs for switching between audio controls and beat library
- Content Area: Container for the active tab content
- Transition: Smooth animation for expand/collapse

**Usage Example**:
```jsx
<CollapsiblePanel
  isCollapsed={isPanelCollapsed}
  toggleCollapse={togglePanelCollapsed}
  currentBeat={currentBeat}
  beatDuration={beatDuration}
  currentTime={currentTime}
  beatVolume={beatVolume}
  setBeatVolume={setBeatVolume}
  micGain={micGain}
  setMicGain={setMicGain}
  availableMics={availableMics}
  selectedMic={selectedMic}
  setSelectedMic={setSelectedMic}
  beatAudioBuffer={beatAudioBuffer}
  beats={beats}
  onBeatSelect={handleBeatSelect}
  onBeatPreview={handleBeatPreview}
  isPreviewPlaying={isPreviewPlaying}
  stopPreview={stopBeatPreview}
/>
```

### AudioControlsTab

**Path**: `frontend/src/ui/layout/AudioControlsTab.tsx`

**Purpose**: Technical audio controls for the recording interface in the CollapsiblePanel.

**Responsibilities**:
- Provides microphone input selection
- Controls microphone gain
- Controls beat volume
- Displays beat waveform visualization
- Organizes controls in a clean, intuitive layout

**Key Props**:
```typescript
interface AudioControlsTabProps {
  currentBeat: any;
  beatDuration: number;
  currentTime: number;
  beatVolume: number;
  setBeatVolume: (volume: number) => void;
  micGain: number;
  setMicGain: (gain: number) => void;
  availableMics: MediaDeviceInfo[];
  selectedMic: MediaDeviceInfo | null;
  setSelectedMic: (mic: MediaDeviceInfo) => void;
  beatAudioBuffer: AudioBuffer | null;
}
```

**Visual Elements**:
- Dropdown: Microphone input selection
- Sliders: Microphone gain (using GainControl) and beat volume (using VolumeSlider) controls
- Waveform: Beat audio visualization using WaveformVisualizer
- Labels: Clear text labels for controls
- Sections: Organized sections for different control groups

**Integration**:
- Uses GainControl for microphone gain
- Uses VolumeSlider for beat volume
- Uses WaveformVisualizer for beat waveform display

### BeatLibraryTab

**Path**: `frontend/src/ui/layout/BeatLibraryTab.tsx`

**Purpose**: Beat selection interface in the CollapsiblePanel.

**Responsibilities**:
- Displays available beats
- Allows beat preview
- Handles beat selection
- Shows beat details (title, genre, BPM)

**Key Props**:
```typescript
interface BeatLibraryTabProps {
  beats: any[];
  currentBeat: any;
  onBeatSelect: (beat: any) => void;
  onBeatPreview: (beat: any) => void;
  isPreviewPlaying: boolean;
  stopPreview: () => void;
}
```

**Visual Elements**:
- Beat List: Scrollable list of available beats
- Beat Items: Individual beat entries with details
- Preview Button: Button to preview each beat
- Select Button: Button to select a beat for recording
- Visual Indicator: Highlight for the currently selected beat

**Note**: The BeatLibraryTab component has been enhanced with search, filtering, and favorites functionality from the previously duplicate version.

### TabNavigation

**Path**: `frontend/src/ui/controls/TabNavigation.tsx`

**Purpose**: Tab navigation component for switching between different views in the CollapsiblePanel.

**Responsibilities**:
- Displays tab buttons for different sections
- Handles tab selection
- Provides visual feedback for the active tab

**Key Props**:
```typescript
interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tabId: string) => void;
  tabs: Tab[];
}

interface Tab {
  id: string;
  label: string;
  icon?: string;
}
```

**Visual Elements**:
- Tab Buttons: Buttons for each tab
- Active Indicator: Visual indicator for the active tab
- Icons: Optional icons for each tab
- Labels: Text labels for each tab

**Note**: The TabNavigation component has been consolidated from two versions into a single component with improved accessibility features.

## UI Utilities

### Button

**Path**: `frontend/src/ui/elements/Button.tsx`

**Purpose**: Reusable button component.

**Variants**:
- Primary: Gold background with navy text
- Secondary: Transparent with gold border
- Tertiary: Transparent with white border

**Sizes**:
- Small: Compact size for tight spaces
- Medium: Default size for most uses
- Large: Prominent size for main actions

**States**:
- Default: Normal state
- Hover: Subtle highlight effect
- Active: Pressed state
- Disabled: Grayed out appearance

### GainControl

**Path**: `frontend/src/ui/controls/GainControl.tsx`

**Purpose**: Microphone gain control slider.

**Responsibilities**:
- Provides a styled slider for controlling microphone gain
- Displays the current gain value as a multiplier (e.g., 1.5x)

**Key Props**:
```typescript
interface GainControlProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step: number;
}
```

**Visual Elements**:
- Slider: Horizontal slider with custom styling
- Value Display: Shows the current gain value as a multiplier

**Usage Example**:
```jsx
<GainControl
  value={micGain}
  onChange={setMicGain}
  min={0}
  max={2}
  step={0.01}
/>
```

**Integration**:
- Used in the AudioControlsTab for microphone gain control
- Provides a consistent UI for gain controls throughout the application

### VolumeSlider

**Path**: `frontend/src/ui/controls/VolumeSlider.tsx`

**Purpose**: Beat volume control slider.

**Responsibilities**:
- Provides a styled slider for controlling beat volume
- Displays the current volume value as a percentage

**Key Props**:
```typescript
interface VolumeSliderProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step: number;
}
```

**Visual Elements**:
- Slider: Horizontal slider with custom styling
- Value Display: Shows the current volume value as a percentage

**Usage Example**:
```jsx
<VolumeSlider
  value={beatVolume}
  onChange={setBeatVolume}
  min={0}
  max={1}
  step={0.01}
/>
```

**Integration**:
- Used in the AudioControlsTab for beat volume control
- Provides a consistent UI for volume controls throughout the application

### Slider

**Path**: `frontend/src/ui/elements/Slider.tsx`

**Purpose**: Reusable slider component.

**Features**:
- Custom styling matching the app theme
- Label and value display
- Smooth interaction

**Usage Example**:
```jsx
<Slider
  label="Volume"
  min={0}
  max={1}
  step={0.01}
  value={volume}
  onChange={handleVolumeChange}
/>
```

### Dropdown

**Path**: `frontend/src/ui/elements/Dropdown.tsx`

**Purpose**: Reusable dropdown component.

**Features**:
- Custom styling matching the app theme
- Search functionality for long lists
- Keyboard navigation

**Usage Example**:
```jsx
<Dropdown
  label="Microphone"
  options={audioInputDevices}
  value={selectedInputDevice}
  onChange={handleInputDeviceChange}
/>
```

## Layout Components

### Layout

**Path**: `frontend/src/components/Layout.tsx`

**Purpose**: Main layout wrapper for all pages.

**Responsibilities**:
- Provides consistent page structure
- Includes header and footer
- Manages responsive behavior

**Structure**:
```jsx
<Layout>
  <Header />
  <main>{children}</main>
  <Footer />
</Layout>
```

### Header

**Path**: `frontend/src/components/Header.tsx`

**Purpose**: Top navigation bar.

**Elements**:
- Logo: App logo and name
- Navigation: Main navigation links
- User Menu: Account-related actions

### Footer

**Path**: `frontend/src/components/Footer.tsx`

**Purpose**: Bottom footer section.

**Elements**:
- Copyright: Copyright information
- Links: Additional links (About, Terms, etc.)
- Social: Social media links

## Animation System

The UI components use a consistent animation system:

### Transition Types

- **Fade**: Opacity transitions for appearing/disappearing elements
- **Slide**: Position transitions for moving elements
- **Scale**: Size transitions for growing/shrinking elements
- **Rotate**: Rotation transitions for spinning elements

### Animation Timing

- **Fast**: 150ms - For small, frequent interactions
- **Medium**: 300ms - For standard transitions
- **Slow**: 500ms - For emphasis and attention

### Animation Curves

- **Ease**: Smooth acceleration and deceleration
- **Ease-In**: Slow start, fast end
- **Ease-Out**: Fast start, slow end
- **Bounce**: Slight overshoot for playful effects

## Responsive Design

The UI components are designed to be responsive with the following breakpoints:

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

Key responsive considerations:
- Mobile: Simplified controls, stacked layout
- Tablet: Adjusted spacing, optimized for touch
- Desktop: Full feature set, optimized for mouse/keyboard

## Accessibility

The UI components include accessibility considerations:

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Readers**: ARIA attributes for screen reader support
- **Color Contrast**: Sufficient contrast for readability
- **Focus Indicators**: Clear visual indicators for keyboard focus

## Implementation Notes

### Styling Approach

The UI components use Tailwind CSS for styling:

```jsx
<div className="flex items-center justify-center p-4 bg-deep-blue text-gold">
  {/* Component content */}
</div>
```

### Component Structure

Components follow a consistent structure:

```jsx
// Imports
import React from 'react';

// Types
interface ComponentProps {
  // Props definition
}

// Component
export const Component: React.FC<ComponentProps> = ({
  // Destructured props
}) => {
  // Component logic

  return (
    // JSX markup
  );
};
```

### Best Practices

When working with UI components:

1. **Consistency**: Use the established design system
2. **Composition**: Compose complex UIs from simple components
3. **Responsiveness**: Test on multiple screen sizes
4. **Accessibility**: Ensure all components are accessible
5. **Performance**: Optimize for rendering performance
