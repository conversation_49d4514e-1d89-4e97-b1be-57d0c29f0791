# Audio Recording and Playback

This document provides detailed context for the audio recording and playback system in the Freestyle App.

## Overview

The audio recording and playback system is responsible for capturing user vocals, mixing them with beats, and playing back the mixed audio. It's a core part of the freestyle rap experience.

## Recording System

### RecordingSystem Component

**Path**: `frontend/src/audio/components/RecordingSystem.tsx`

**Purpose**: Main controller component for the recording experience.

**Responsibilities**:
- Initializes audio recording
- Handles microphone input selection
- Manages recording state (idle, recording, preview)
- Coordinates between recording and preview components

**Key States**:
- `recordingState`: Current state of the recording process (idle, recording, preview)
- `selectedInputDevice`: Currently selected microphone input device
- `inputVolume`: Volume level for microphone input

**Key Methods**:
- `handleRecordingComplete`: Processes the recording and transitions to preview mode
- `handlePreviewComplete`: Resets the recording state and returns to idle mode
- `handleInputDeviceChange`: Updates the selected microphone input device
- `handleVolumeChange`: Updates the microphone input volume

### FreestyleRecorder Component

**Path**: `frontend/src/audio/components/FreestyleRecorder.tsx`

**Purpose**: Handles the actual audio recording functionality.

**Responsibilities**:
- Captures microphone input
- Synchronizes recording with beat playback
- Processes audio data for preview
- Visualizes audio input

**Key Hooks**:
- `useAudioRecording`: For recording functionality
- `useAudioContext`: For audio processing nodes

**Key Methods**:
- `startRecording`: Initializes the recording process
- `stopRecording`: Ends the recording and processes the audio
- `handleBeatLoaded`: Synchronizes beat playback with recording

### AudioRecorder Service

**Path**: `frontend/src/audio/services/audioRecorder.ts`

**Purpose**: Low-level service for audio recording.

**Responsibilities**:
- Initializes MediaRecorder
- Captures audio data
- Manages recording state
- Provides recording blob

**Key Methods**:
- `start(deviceId?: string)`: Starts recording from the specified device
- `stop()`: Stops recording and triggers the onStop callback
- `pause()`: Pauses recording
- `resume()`: Resumes recording after pausing
- `getRecordingTime()`: Gets the current recording time in seconds

## Playback System

### PreviewPlayer Component

**Path**: `frontend/src/audio/components/PreviewPlayer.tsx`

**Purpose**: Plays back the recorded freestyle with the beat.

**Responsibilities**:
- Plays the mixed audio (vocals + beat)
- Displays waveform visualization
- Provides playback controls
- Enables downloading the recording

**Key Hooks**:
- `useAudioPlayback`: For audio playback functionality
- `useAudioStore`: For accessing mixed audio data
- `useBeatStore`: For accessing beat information

**Key Methods**:
- `togglePlayPause`: Toggles between play and pause states
- `handleSeek`: Seeks to a specific position in the audio
- `handleDownload`: Downloads the mixed recording

### useAudioPlayback Hook

**Path**: `frontend/src/audio/hooks/useAudioPlayback.ts`

**Purpose**: Provides audio playback functionality.

**Responsibilities**:
- Initializes audio playback
- Handles play/pause controls
- Manages playback state
- Handles seeking
- Manages audio sources from multiple locations
- Handles errors and recovery
- Provides consistent API for audio playback

**Note**: This hook has been consolidated from duplicate implementations to provide a single source of truth for audio playback functionality.

**Key Methods**:
- `play()`: Starts audio playback
- `pause()`: Pauses audio playback
- `stop()`: Stops audio playback and resets position
- `handleSeek(time: number)`: Seeks to a specific position in the audio
- `togglePlayPause()`: Toggles between play and pause states

## Audio Processing

### audioProcessor Service

**Path**: `frontend/src/audio/services/audioProcessor.ts`

**Purpose**: Provides audio processing functionality.

**Responsibilities**:
- Decodes audio files
- Mixes audio buffers
- Converts between formats
- Processes recordings for preview

**Key Functions**:
- `decodeAudioFile(arrayBuffer: ArrayBuffer)`: Decodes an audio file into an AudioBuffer
- `mixAudioBuffers(vocals: AudioBuffer, beat: AudioBuffer)`: Mixes two audio buffers together
- `audioBufferToWav(buffer: AudioBuffer)`: Converts an AudioBuffer to a WAV file blob
- `processRecordingForPreview(recordingBlob: Blob, beatUrl: string)`: Processes a recording with a beat for preview

## Audio Mixing Process

The audio mixing process follows these steps:

1. **Recording Capture**:
   - User vocals are captured using MediaRecorder
   - Recording is stored as a Blob

2. **Audio Decoding**:
   - Recording Blob is decoded into an AudioBuffer
   - Beat audio is fetched and decoded into an AudioBuffer

3. **Mixing**:
   - Vocals and beat are mixed together with user-selected gain levels
   - Microphone gain (0-200%) is applied to the vocal track
   - Beat volume (0-200%) is applied to the beat track
   - If beat is longer than vocals, vocals are positioned at the beginning
   - If vocals are longer than beat, beat is looped to match
   - The mixed audio is normalized to prevent clipping while maintaining the relative balance

4. **Output Generation**:
   - Mixed AudioBuffer is converted to a WAV file Blob
   - Mixed AudioBuffer is stored for visualization and playback

## Synchronization

Synchronization between recording and beat playback is handled as follows:

1. **Beat Preloading**:
   - Beat is preloaded before recording starts
   - Beat is decoded into an AudioBuffer for precise timing
   - Beat audio element is prepared with the correct volume

2. **Synchronized Start with Delay**:
   - UI is updated to show initializing state
   - A short delay ensures UI updates and audio elements are ready
   - Recording starts with the MediaRecorder
   - Another short delay ensures recording has started
   - Beat playback starts after recording is confirmed
   - This sequence prevents the beginning of audio from being cut off

3. **Synchronized Stop**:
   - Recording stops first
   - Beat playback is stopped with a fade-out to prevent clicks
   - Recording is processed and mixed with the beat using correct gain values

4. **Preview Playback**:
   - Mixed audio is played back as a single file
   - Waveform visualization shows the mixed audio
   - Volume controls affect their respective audio sources only

## Audio Quality Settings

The audio recording and playback system supports the following quality settings:

- **Sample Rate**: 44.1 kHz (CD quality)
- **Bit Depth**: 16-bit
- **Channels**: Stereo (2 channels)
- **Format**: WAV (uncompressed)

These settings ensure high-quality recordings while maintaining compatibility with web browsers.

## Microphone Input Handling

Microphone input is handled as follows:

1. **Device Enumeration**:
   - Available audio input devices are enumerated using MediaDevices API
   - Devices are displayed in a dropdown for user selection

2. **Input Selection**:
   - User can select a specific microphone input device
   - Selection is stored for future sessions

3. **Volume Control**:
   - Microphone gain is controlled using a percentage slider (0-200%)
   - Beat volume is controlled using a separate percentage slider (0-200%)
   - Both sliders default to 100% (source volume)
   - Volume settings are persisted in localStorage between sessions
   - Microphone gain affects only the recording input
   - Beat volume affects only the beat playback
   - Both values are used during mixing to maintain the user's preferred balance

4. **Permission Handling**:
   - Microphone permissions are requested when needed
   - Permission state is tracked and displayed to the user

## Error Handling

The audio recording and playback system includes robust error handling:

- **Permission Errors**: Handled with clear user messages
- **Device Errors**: Fallback to default device if selected device is unavailable
- **Processing Errors**: Graceful degradation with error messages
- **Playback Errors**: Retry mechanisms and fallbacks

## Implementation Notes

### Web Audio API Usage

The system makes extensive use of the Web Audio API:

```javascript
// Create an audio context
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

// Create nodes
const gainNode = audioContext.createGain();
const analyserNode = audioContext.createAnalyser();

// Connect nodes
sourceNode.connect(gainNode);
gainNode.connect(analyserNode);
analyserNode.connect(audioContext.destination);

// Process audio
gainNode.gain.value = 0.8; // Set volume to 80%
```

### MediaRecorder Usage

Audio recording uses the MediaRecorder API:

```javascript
// Get user media
const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

// Create recorder
const recorder = new MediaRecorder(stream);

// Set up data handling
recorder.ondataavailable = (e) => {
  chunks.push(e.data);
};

// Set up completion handling
recorder.onstop = () => {
  const blob = new Blob(chunks, { type: 'audio/webm' });
  onComplete(blob);
};

// Start recording
recorder.start();
```

### Performance Considerations

The system includes several performance optimizations:

1. **Web Workers**: Heavy processing is offloaded to Web Workers
2. **Caching**: Audio buffers are cached for reuse
3. **Lazy Loading**: Components are loaded only when needed
4. **Memory Management**: Audio resources are properly cleaned up

## Best Practices

When working with the audio recording and playback system:

1. **Close AudioContext**: Always close the AudioContext when not in use
2. **Handle Permissions**: Request microphone permissions at appropriate times
3. **Error Handling**: Implement robust error handling for audio operations
4. **Memory Management**: Clean up audio resources when components unmount
5. **User Feedback**: Provide clear visual feedback for audio operations
