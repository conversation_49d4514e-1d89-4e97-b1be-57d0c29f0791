# Freestyle App - Context Documentation Index

This index provides a comprehensive overview of all context documentation for the Freestyle App.

## Overview

- [README](./README.md) - Main overview of the application architecture and context

## Components

- [Audio Components](./components/audio-components.md) - Documentation for all audio-related components
- [Audio Recording and Playback](./components/audio-recording-playback.md) - Detailed documentation for the recording and playback system
- [Waveform Visualization](./components/waveform-visualization.md) - Detailed documentation for the waveform visualization system
- [UI Components](./components/ui-components.md) - Documentation for UI components and visual design
- [Beat Management](./components/beat-management.md) - Documentation for the beat management system

## Hooks

- [Audio Hooks](./hooks/audio-hooks.md) - Documentation for all audio-related hooks

## Stores

- [State Management](./stores/state-management.md) - Documentation for state management approach and stores

## Services

- [Audio Services](./services/audio-services.md) - Documentation for audio processing services

## Pages

- [Application Pages](./pages/application-pages.md) - Documentation for application pages and routing

## Diagrams

- [Architecture Diagrams](./diagrams/architecture.md) - Visual representations of the application architecture

## Workflow

- [Development Workflow](./development-workflow.md) - Documentation for development process and workflow

## Key Concepts

### Audio Processing

The Freestyle App uses the Web Audio API for audio processing, with the following key components:

- **AudioRecorder**: Captures microphone input
- **AudioProcessor**: Processes and mixes audio
- **WaveformGenerator**: Generates waveform visualizations

### User Interface

The UI follows a premium Hip-Hop Golden Era theme with:

- Deep black vinyl record with champagne gold rim
- Navy-blue label
- Platinum/silver microphone with gold accents
- Deep blue gradient background

### Recording Flow

1. User selects a beat
2. User records vocals over the beat
3. Recording is processed and mixed with the beat
4. User is taken to preview mode for playback and download

### Component Hierarchy

```
RecordingSystem
├── FreestyleRecorder (for recording)
└── RecordingPreviewSystem (for preview)
    └── PreviewPlayer
        └── WaveformVisualizer
```

### State Management

The application uses Zustand for state management with three main stores:

- **beatStore**: Manages beat selection and metadata
- **audioStore**: Manages recording and playback state
- **uiStore**: Manages UI-related state

## File Structure

```
frontend/
├── public/            # Static assets including beats
├── src/
│   ├── audio/         # Audio-related components, hooks, and utilities
│   │   ├── components/  # Audio-specific UI components
│   │   ├── hooks/       # Audio-related React hooks
│   │   ├── services/    # Audio processing services
│   │   └── utils/       # Audio utility functions
│   ├── components/    # Shared UI components
│   ├── hooks/         # Shared React hooks
│   ├── pages/         # Next.js pages
│   ├── stores/        # State management stores
│   ├── styles/        # Global styles
│   └── ui/            # UI-specific components
│       └── visualizers/  # Visual components like waveforms
├── backend/
│   ├── controllers/   # Request handlers
│   ├── models/        # Data models
│   ├── routes/        # API routes
│   └── services/      # Backend services
```

## Development Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Start development servers:
   ```
   npm run dev
   ```

3. Frontend runs on port 3000, backend on port 3001

## Key Technologies

- **Next.js**: React framework for server-rendered applications
- **React**: UI library for building component-based interfaces
- **TypeScript**: Typed superset of JavaScript
- **Zustand**: State management library
- **Web Audio API**: Browser API for audio processing
- **Tailwind CSS**: Utility-first CSS framework

## Best Practices

1. **Component Organization**: Keep components focused and reusable
2. **State Management**: Use Zustand for global state, React hooks for local state
3. **Audio Processing**: Handle audio processing in Web Workers when possible
4. **Error Handling**: Implement robust error handling for audio operations
5. **Performance**: Optimize for performance, especially for audio processing
6. **Accessibility**: Ensure all components are accessible
7. **Testing**: Write tests for all components and functionality

## Common Issues and Solutions

1. **Audio Permission Issues**: Handle microphone permissions gracefully
2. **Browser Compatibility**: Test across different browsers
3. **Mobile Support**: Ensure responsive design works on mobile devices
4. **Performance**: Monitor and optimize performance, especially for audio processing
5. **State Management**: Avoid state management anti-patterns

## Future Development

1. **User Accounts**: Add user authentication and accounts
2. **Saved Recordings**: Allow users to save and manage recordings
3. **Social Sharing**: Enable sharing recordings on social media
4. **More Beats**: Expand the beat library
5. **Advanced Audio Effects**: Add effects like reverb, EQ, etc.

## Contributing

1. **Pull Latest Changes**: Always pull latest changes before starting work
2. **Create Feature Branch**: Work in a feature branch, not directly on main
3. **Small Commits**: Make small, focused commits
4. **Write Tests**: Write tests for new features and bug fixes
5. **Document Changes**: Update documentation for significant changes
