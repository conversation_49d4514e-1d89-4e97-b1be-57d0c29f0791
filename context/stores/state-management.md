# State Management

This document provides context for the state management approach in the Freestyle App.

## Overview

The Freestyle App uses Zustand for state management. Zustand is a small, fast, and scalable state management solution that uses hooks. It's simpler than Redux but provides similar capabilities.

## Core Stores

### beatStore

**Path**: `frontend/src/stores/beatStore.ts`

**Purpose**: Manages the state related to beats (instrumentals).

**State Structure**:
```typescript
interface BeatState {
  beats: Beat[];
  currentBeat: Beat | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchBeats: () => Promise<void>;
  setCurrentBeat: (beat: Beat) => void;
  clearCurrentBeat: () => void;
}
```

**Key Actions**:
- `fetchBeats`: Loads the available beats from the backend or local storage
- `setCurrentBeat`: Sets the currently selected beat
- `clearCurrentBeat`: Clears the current beat selection

**Usage Example**:
```jsx
const { currentBeat, setCurrentBeat, beats } = useBeatStore();
```

**Persistence**:
- Beat selection is persisted in localStorage to survive page reloads

---

### audioStore

**Path**: `frontend/src/stores/audioStore.ts`

**Purpose**: Manages the state related to audio recording and playback.

**State Structure**:
```typescript
interface AudioState {
  recordingBlob: Blob | null;
  recordingBuffer: AudioBuffer | null;
  mixedBlob: Blob | null;
  mixedBuffer: AudioBuffer | null;
  isRecording: boolean;
  isPlaying: boolean;
  recordingTime: number;
  
  // Actions
  setRecordingBlob: (blob: Blob | null) => void;
  setRecordingBuffer: (buffer: AudioBuffer | null) => void;
  setMixedBlob: (blob: Blob | null) => void;
  setMixedBuffer: (buffer: AudioBuffer | null) => void;
  setIsRecording: (isRecording: boolean) => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setRecordingTime: (time: number) => void;
  resetRecording: () => void;
}
```

**Key Actions**:
- `setRecordingBlob`: Sets the raw recording blob
- `setMixedBlob`: Sets the mixed recording (vocals + beat) blob
- `setIsRecording`: Updates the recording state
- `resetRecording`: Clears all recording data

**Usage Example**:
```jsx
const { 
  isRecording, 
  recordingBlob, 
  mixedBlob, 
  setIsRecording 
} = useAudioStore();
```

---

### uiStore

**Path**: `frontend/src/stores/uiStore.ts`

**Purpose**: Manages UI-related state.

**State Structure**:
```typescript
interface UIState {
  activeTab: string;
  isSidebarOpen: boolean;
  theme: 'light' | 'dark';
  
  // Actions
  setActiveTab: (tab: string) => void;
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
}
```

**Key Actions**:
- `setActiveTab`: Sets the active tab in tabbed interfaces
- `toggleSidebar`: Toggles the sidebar open/closed state
- `setTheme`: Changes the application theme

**Usage Example**:
```jsx
const { activeTab, setActiveTab } = useUIStore();
```

## Store Interactions

The stores interact in the following ways:

1. **Recording Flow**:
   - `beatStore` provides the current beat for recording
   - `audioStore` stores the recording state and data
   - Components read from both stores to coordinate recording

2. **Playback Flow**:
   - `audioStore` provides the mixed audio for playback
   - `beatStore` provides beat metadata for display
   - Components read from both stores to coordinate playback

## Implementation Notes

### Store Creation Pattern

All stores follow a similar pattern:

```typescript
import create from 'zustand';
import { persist } from 'zustand/middleware';

interface StoreState {
  // State properties
  
  // Actions
}

export const useStore = create<StoreState>()(
  persist(
    (set) => ({
      // Initial state
      
      // Actions that use set() to update state
    }),
    {
      name: 'store-name', // For localStorage persistence
      partialize: (state) => ({ /* subset of state to persist */ }),
    }
  )
);
```

### State Updates

State updates follow the immutability principle:

```typescript
set((state) => ({ 
  ...state, 
  someProperty: newValue 
}));
```

### Selectors

For performance optimization, components should use selectors to access only the parts of state they need:

```jsx
const currentBeat = useBeatStore((state) => state.currentBeat);
```

## Best Practices

1. **Keep stores focused**: Each store should manage a specific domain of state
2. **Minimize state**: Only store what's necessary in global state
3. **Use selectors**: Select only the parts of state needed by components
4. **Persist carefully**: Only persist state that needs to survive page reloads
5. **Document actions**: Each action should have a clear purpose and documentation
