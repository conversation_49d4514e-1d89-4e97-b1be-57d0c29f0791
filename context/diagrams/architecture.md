# Application Architecture Diagrams

This document provides visual representations of the Freestyle App's architecture.

## Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        Next.js Pages                         │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │  Landing    │    │  Session    │    │  Preview    │      │
│  │   Page      │    │   Page      │    │   Page      │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                     Core Components                          │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ Recording   │    │ Preview     │    │ Beat        │      │
│  │ System      │◄───┤ Player      │    │ Library     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                  │                  │             │
│         ▼                  ▼                  ▼             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ Freestyle   │    │ Waveform    │    │ Beat        │      │
│  │ Recorder    │    │ Visualizer  │    │ Card        │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                      UI Components                           │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ MicVinyl    │    │ Transport   │    │ Collapsible │      │
│  │ Integrated  │    │ Controls    │    │ Panel       │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                        Hooks                                 │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ useAudio    │    │ useAudio    │    │ useWaveform │      │
│  │ Recording   │    │ Playback    │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                       Services                               │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ Audio       │    │ Audio       │    │ Waveform    │      │
│  │ Recorder    │    │ Processor   │    │ Generator   │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────┐
│                        Stores                                │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ Beat        │    │ Audio       │    │ UI          │      │
│  │ Store       │    │ Store       │    │ Store       │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
```

## Data Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ User        │     │ Component   │     │ Hook        │
│ Interaction │────►│ State       │────►│ Logic       │
└─────────────┘     └─────────────┘     └─────────────┘
                                              │
                                              ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ UI          │     │ Store       │     │ Service     │
│ Update      │◄────│ Update      │◄────│ Processing  │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Recording Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ User clicks │     │ Recording   │     │ Audio       │
│ microphone  │────►│ System      │────►│ Recorder    │
└─────────────┘     │ starts      │     │ initializes │
                    └─────────────┘     └─────────────┘
                                              │
                                              ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Beat plays  │     │ Beat Store  │     │ Audio       │
│ synchronized│◄────│ provides    │◄────│ Context     │
│ with mic    │     │ beat data   │     │ created     │
└─────────────┘     └─────────────┘     └─────────────┘
                                              │
                                              ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Recording   │     │ Audio Store │     │ Audio       │
│ completes   │────►│ updated with│────►│ Processor   │
│             │     │ recording   │     │ mixes audio │
└─────────────┘     └─────────────┘     └─────────────┘
                                              │
                                              ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ User        │     │ Preview     │     │ Waveform    │
│ redirected  │◄────│ Player      │◄────│ Visualizer  │
│ to preview  │     │ loads       │     │ renders     │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Audio Processing Pipeline

```
┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ Microphone│    │ Audio     │    │ Gain      │    │ Recorder  │
│ Input     │───►│ Context   │───►│ Node      │───►│ Node      │
└───────────┘    └───────────┘    └───────────┘    └───────────┘
                                                         │
                                                         ▼
┌───────────┐    ┌───────────┐    ┌───────────┐    ┌───────────┐
│ Mixed     │    │ Audio     │    │ Beat      │    │ Recording │
│ Output    │◄───│ Processor │◄───│ Audio     │◄───│ Blob      │
└───────────┘    └───────────┘    └───────────┘    └───────────┘
```

## State Management

```
┌───────────────────────────────────────────────────────┐
│                     Zustand Stores                     │
│                                                       │
│  ┌─────────────┐    ┌─────────────┐    ┌──────────┐   │
│  │ Beat Store  │    │ Audio Store │    │ UI Store │   │
│  │             │    │             │    │          │   │
│  │ - beats     │    │ - recording │    │ - tabs   │   │
│  │ - currentBeat    │ - mixed     │    │ - theme  │   │
│  └─────────────┘    └─────────────┘    └──────────┘   │
└───────────────────────────────────────────────────────┘
                           │
                           ▼
┌───────────────────────────────────────────────────────┐
│                     React Components                   │
│                                                       │
│  ┌─────────────┐    ┌─────────────┐    ┌──────────┐   │
│  │ Beat        │    │ Recording   │    │ UI       │   │
│  │ Selection   │    │ Components  │    │ Controls │   │
│  └─────────────┘    └─────────────┘    └──────────┘   │
└───────────────────────────────────────────────────────┘
                           │
                           ▼
┌───────────────────────────────────────────────────────┐
│                     Local Storage                      │
│                                                       │
│  ┌─────────────┐    ┌─────────────┐    ┌──────────┐   │
│  │ Selected    │    │ User        │    │ UI       │   │
│  │ Beat        │    │ Preferences │    │ Settings │   │
│  └─────────────┘    └─────────────┘    └──────────┘   │
└───────────────────────────────────────────────────────┘
```

## Frontend-Backend Communication

```
┌───────────────────────────────────────────────────────┐
│                     Frontend (Port 3000)               │
│                                                       │
│  ┌─────────────┐    ┌─────────────┐    ┌──────────┐   │
│  │ React       │    │ Zustand     │    │ Web      │   │
│  │ Components  │    │ Stores      │    │ Audio API│   │
│  └─────────────┘    └─────────────┘    └──────────┘   │
└───────────────────────────────────────────────────────┘
                           │
                           ▼
┌───────────────────────────────────────────────────────┐
│                     Backend (Port 3001)                │
│                                                       │
│  ┌─────────────┐    ┌─────────────┐    ┌──────────┐   │
│  │ API         │    │ Controllers │    │ Services │   │
│  │ Routes      │    │             │    │          │   │
│  └─────────────┘    └─────────────┘    └──────────┘   │
│                                                       │
│  ┌─────────────┐    ┌─────────────┐                   │
│  │ Models      │    │ Database    │                   │
│  │             │    │             │                   │
│  └─────────────┘    └─────────────┘                   │
└───────────────────────────────────────────────────────┘
```

## User Flow Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Landing     │     │ Beat        │     │ Session     │
│ Page        │────►│ Selection   │────►│ Page        │
└─────────────┘     └─────────────┘     └─────────────┘
                                              │
                                              ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Download    │     │ Preview     │     │ Recording   │
│ Recording   │◄────│ Page        │◄────│ Process     │
└─────────────┘     └─────────────┘     └─────────────┘
      │                    │
      │                    ▼
      │              ┌─────────────┐
      │              │ Record      │
      └─────────────►│ Again       │
                     └─────────────┘
```

## Notes on Diagrams

These diagrams are simplified representations of the application architecture. The actual implementation may have additional components and connections.

Key points to remember:

1. **Component Hierarchy**: Components are organized in a hierarchical structure, with pages at the top level, followed by core components, UI components, and so on.

2. **Data Flow**: Data flows from user interactions through components, hooks, and services, with state updates propagating back to the UI.

3. **Recording Flow**: The recording process involves multiple components and services working together to capture, process, and mix audio.

4. **Audio Processing**: The audio processing pipeline uses the Web Audio API to handle microphone input, gain control, and recording.

5. **State Management**: Zustand stores manage global state, with components accessing only the state they need through selectors.

6. **Frontend-Backend**: The frontend and backend communicate through API calls, with the backend handling data persistence and processing.

7. **User Flow**: The user journey through the application follows a logical progression from landing page to recording to preview.
