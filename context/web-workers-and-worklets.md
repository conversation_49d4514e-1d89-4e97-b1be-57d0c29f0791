# Web Workers and Audio Worklets

This document explains how Web Workers and Audio Worklets are structured in the freestyle app.

## Overview

Web Workers and Audio Worklets are used to offload CPU-intensive tasks from the main thread to improve performance. In our app:

- **Web Workers** handle audio processing tasks like decoding, mixing, and effects.
- **Audio Worklets** handle real-time audio recording with precise timing.

## File Structure

### Source Files (TypeScript)

- `frontend/src/audio/workers/` - TypeScript source for Web Workers
  - `audioProcessor.worker.ts` - Main audio processing worker

- `frontend/src/audio/worklets/` - TypeScript source for Audio Worklets
  - `syncRecorder.worklet.ts` - Synchronized recording worklet

### Compiled Files (JavaScript)

- `frontend/public/` - Compiled JavaScript files
  - `audioProcessor.worker.js` - Compiled Web Worker
  - `syncRecorder.worklet.js` - Compiled Audio Worklet
  - `comlink.js` - Comlink library for communication with workers

### Service Files

- `frontend/src/audio/services/` - Services for audio processing
  - `audioWorkerService.ts` - Service for interacting with the Web Worker
  - `syncAudioRecorder.ts` - Service for synchronized audio recording

### Hook Files

- `frontend/src/audio/hooks/` - React hooks for audio functionality
  - `useAudioWorker.ts` - Hook for accessing the Web Worker
  - `useSyncAudioRecording.ts` - Hook for recording with synchronization

### Deprecated Files (To Be Removed)

- `frontend/src/audio/services/audioProcessor.ts` - Deprecated, functionality moved to Web Worker
- `frontend/src/audio/services/worklet/` - Deprecated, not used in the current architecture

## Compilation Process

The TypeScript source files need to be compiled to JavaScript and placed in the `frontend/public` directory. This is because Web Workers and Audio Worklets must be loaded from separate JavaScript files.

### Important Notes

1. **No TypeScript Syntax in Compiled Files**
   - The compiled JavaScript files should not contain TypeScript-specific syntax like `private` keywords.
   - Use standard JavaScript constructs instead.

2. **Module Loading**
   - Web Workers can use `importScripts()` to load dependencies.
   - Audio Worklets are more restricted and should be self-contained.

3. **Communication**
   - Web Workers use `postMessage()` and event listeners for communication.
   - Audio Worklets use `port.postMessage()` and `port.onmessage` for communication.
   - We use Comlink to simplify communication with Web Workers.

## Loading Workers and Worklets

### Web Workers

```javascript
// Create a new worker
const worker = new Worker('/audioProcessor.worker.js');

// With Comlink
import * as Comlink from 'comlink';
const workerApi = Comlink.wrap(worker);
```

### Audio Worklets

```javascript
// Load the worklet
await audioContext.audioWorklet.addModule('/syncRecorder.worklet.js');

// Create a worklet node
const workletNode = new AudioWorkletNode(audioContext, 'sync-recorder-processor');
```

## Best Practices

1. **Keep Workers Simple**
   - Focus on a single responsibility per worker.
   - Minimize dependencies.

2. **Avoid Transferring Large Data**
   - Use `transferable` objects when possible.
   - Consider chunking large data transfers.

3. **Error Handling**
   - Implement proper error handling in workers.
   - Provide fallback mechanisms for when workers fail.

4. **Testing**
   - Test workers in isolation.
   - Verify that workers function correctly across different browsers.

## Troubleshooting

### Common Issues

1. **Syntax Errors**
   - JavaScript syntax errors in workers will cause them to fail silently.
   - Check the console for error messages.

2. **Cross-Origin Issues**
   - Workers must be served from the same origin as the main page.
   - Use proper CORS headers if needed.

3. **Module Loading Issues**
   - Ensure dependencies are properly loaded.
   - Check paths to worker files.

### Debugging

1. **Console Logging**
   - Use `console.log()` in workers for debugging.
   - Messages will appear in the main console.

2. **Chrome DevTools**
   - Use the "Sources" panel to debug workers.
   - Set breakpoints and inspect variables.

## Example: Audio Processing Worker

```javascript
// audioProcessor.worker.js
importScripts('/comlink.js');

const audioProcessorWorker = {
  // Methods for audio processing
  async decodeAudio(arrayBuffer) {
    // Implementation
  },

  async mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain) {
    // Implementation
  }
};

// Export with Comlink
self.Comlink.expose(audioProcessorWorker);
```

## Example: Audio Worklet

```javascript
// syncRecorder.worklet.js
class SyncRecorderProcessor extends AudioWorkletProcessor {
  constructor() {
    super();

    // Initialize properties
    this._buffer = [];
    this._isRecording = false;

    // Set up message handling
    this.port.onmessage = this.handleMessage.bind(this);
  }

  process(inputs, outputs, parameters) {
    // Process audio
    return true;
  }
}

// Register the processor
registerProcessor('sync-recorder-processor', SyncRecorderProcessor);
```
