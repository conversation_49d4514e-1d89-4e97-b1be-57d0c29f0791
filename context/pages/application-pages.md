# Application Pages

This document provides context for the pages in the Freestyle App.

## Page Structure

The Freestyle App uses Next.js for routing, with the following main pages:

```
pages/
├── _app.tsx           # App wrapper with global providers
├── _document.tsx      # Custom document for HTML structure
├── index.tsx          # Landing page
├── session/           # Recording session pages
│   ├── index.tsx      # Main recording session page
│   └── preview.tsx    # Recording preview page
├── beats/             # Beat library pages
│   └── index.tsx      # Beat library listing
└── account/           # User account pages (future)
```

## Core Pages

### Landing Page

**Path**: `frontend/src/pages/index.tsx`

**Purpose**: Serves as the entry point to the application.

**Key Features**:
- Introduction to the app
- Call-to-action to start a freestyle session
- Featured beats
- Quick access to recent recordings (future)

**Components Used**:
- `Layout`: Main layout wrapper
- `Hero`: Hero section with CTA
- `FeaturedBeats`: Showcase of featured beats

**Navigation**:
- "Start Freestyle" button → `/session`
- "Browse Beats" button → `/beats`

---

### Session Page

**Path**: `frontend/src/pages/session/index.tsx`

**Purpose**: Main page for recording freestyle sessions.

**Key Features**:
- Beat selection
- Recording interface
- Audio controls
- Microphone input selection
- Volume controls

**Components Used**:
- `RecordingSystem`: Main controller for recording
- `MicVinylIntegrated`: Visual interface for recording
- `CollapsiblePanel`: Expandable panel for technical controls
- `AudioControlsTab`: Technical audio controls
- `BeatLibraryTab`: Beat selection interface

**State Management**:
- Uses `beatStore` for beat selection
- Uses `audioStore` for recording state

**User Flow**:
1. User selects a beat
2. User adjusts microphone input and volume
3. User clicks the microphone to start recording
4. After recording, user is taken to preview page

**Navigation**:
- After recording → `/session/preview`
- "Change Beat" → Opens beat selection panel

---

### Preview Page

**Path**: `frontend/src/pages/session/preview.tsx`

**Purpose**: Allows users to preview and download their recordings.

**Key Features**:
- Playback of mixed recording (vocals + beat)
- Waveform visualization
- Download functionality
- Option to record again

**Components Used**:
- `PreviewPlayer`: Main component for playback
- `WaveformVisualizer`: Visualization of the mixed audio
- `TransportControls`: Playback controls

**State Management**:
- Uses `audioStore` for mixed audio data
- Uses `beatStore` for beat information

**User Flow**:
1. User arrives after recording
2. User can play back the recording
3. User can download the recording
4. User can choose to record again

**Navigation**:
- "Record Again" button → `/session`
- "Back to Home" → `/`

---

### Beat Library Page

**Path**: `frontend/src/pages/beats/index.tsx`

**Purpose**: Allows users to browse and select beats.

**Key Features**:
- Beat listing with filtering
- Beat preview
- Beat selection for recording

**Components Used**:
- `BeatLibrary`: Main component for beat listing
- `BeatCard`: Individual beat display
- `AudioPlayer`: Simple player for beat preview

**State Management**:
- Uses `beatStore` for beat data and selection

**User Flow**:
1. User browses available beats
2. User can preview beats
3. User selects a beat for recording

**Navigation**:
- "Use This Beat" button → `/session` with selected beat

## Page Layout Structure

All pages use a common layout structure:

```jsx
<Layout>
  <Header />
  <main>
    {/* Page-specific content */}
  </main>
  <Footer />
</Layout>
```

## Responsive Design

The pages are designed to be responsive with the following breakpoints:

- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

Key responsive considerations:
- Mobile: Simplified controls, stacked layout
- Tablet: Adjusted spacing, optimized for touch
- Desktop: Full feature set, optimized for mouse/keyboard

## Page Transitions

Page transitions are handled by Next.js with the following enhancements:

- Smooth fade transitions between pages
- State persistence for audio-related data
- Loading states for data-dependent pages

## SEO Considerations

Each page includes proper SEO metadata:

```jsx
<Head>
  <title>{pageTitle} | Freestyle App</title>
  <meta name="description" content={pageDescription} />
  <meta property="og:title" content={pageTitle} />
  <meta property="og:description" content={pageDescription} />
  <meta property="og:image" content="/images/og-image.jpg" />
</Head>
```

## Error Handling

Pages include error boundaries to handle unexpected errors:

```jsx
<ErrorBoundary fallback={<ErrorPage />}>
  {/* Page content */}
</ErrorBoundary>
```

## Loading States

Data-dependent pages show loading states:

```jsx
{isLoading ? (
  <LoadingSpinner />
) : error ? (
  <ErrorMessage message={error} />
) : (
  /* Actual page content */
)}
```

## Future Pages

Planned future pages include:

1. **User Account Pages**:
   - Login/Registration
   - User profile
   - Saved recordings

2. **Community Pages**:
   - Shared recordings
   - Community challenges
   - Leaderboards

3. **Tutorial Pages**:
   - Getting started guides
   - Tips and techniques
   - FAQ
