# Audio Hooks

This document provides context for all audio-related hooks in the Freestyle App.

## Core Audio Hooks

### useWaveform

**Path**: `frontend/src/audio/hooks/useWaveform.ts`

**Purpose**: Provides waveform visualization functionality.

**Responsibilities**:
- Processes audio data to generate waveform peaks
- Handles responsive sizing
- Manages canvas drawing
- Handles user interactions (seeking)

**Parameters**:
```typescript
interface UseWaveformOptions {
  audioUrl?: string | Blob | null;
  audioBuffer?: AudioBuffer | null;
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onSeek?: (time: number) => void;
  height?: number;
  gradientColors?: string[];
  waveformKey?: string;
}
```

**Returns**:
```typescript
{
  waveformCanvasRef: React.RefObject<HTMLCanvasElement>;
  playheadCanvasRef: React.RefObject<HTMLCanvasElement>;
  containerRef: React.RefObject<HTMLDivElement>;
  loading: boolean;
  error: string | null;
  handleSeekClick: (e: React.MouseEvent<HTMLDivElement>) => void;
}
```

**Usage Example**:
```jsx
const {
  waveformCanvasRef,
  playheadCanvasRef,
  containerRef,
  loading,
  error,
  handleSeekClick
} = useWaveform({
  audioBuffer,
  currentTime,
  duration,
  isPlaying,
  onSeek
});
```

**Dependencies**:
- Uses `waveformGenerator.ts` utilities for peak generation
- Uses `useBeatStore` for fallback beat audio

---

### useAudioRecording

**Path**: `frontend/src/audio/hooks/useAudioRecording.ts`

**Purpose**: Provides audio recording functionality.

**Responsibilities**:
- Initializes audio recording
- Handles microphone input
- Manages recording state
- Processes recorded audio

**Parameters**:
```typescript
interface UseAudioRecordingOptions {
  onRecordingComplete?: (recordingBlob: Blob) => void;
  beatUrl?: string | null;
  autoStart?: boolean;
  gainNode?: GainNode | null;
}
```

**Returns**:
```typescript
{
  isRecording: boolean;
  isPaused: boolean;
  recordingTime: number;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  pauseRecording: () => void;
  resumeRecording: () => void;
  recordingBlob: Blob | null;
  error: string | null;
  audioInputDevices: MediaDeviceInfo[];
  selectedInputDevice: string;
  setSelectedInputDevice: (deviceId: string) => void;
  inputVolume: number;
  setInputVolume: (volume: number) => void;
}
```

**Usage Example**:
```jsx
const {
  isRecording,
  startRecording,
  stopRecording,
  recordingTime,
  recordingBlob,
  error
} = useAudioRecording({
  onRecordingComplete: handleRecordingComplete,
  beatUrl: currentBeat?.audio_url
});
```

**Dependencies**:
- Uses `AudioRecorder` service for recording
- Uses `audioProcessor` service for processing
- Updates `audioStore` with recording data

---

### useAudioPlayback

**Path**: `frontend/src/audio/hooks/useAudioPlayback.ts`

**Purpose**: Provides audio playback functionality.

**Responsibilities**:
- Initializes audio playback
- Handles play/pause controls
- Manages playback state
- Handles seeking

**Parameters**:
```typescript
interface UseAudioPlaybackOptions {
  audioUrl?: string | null;
  audioBlob?: Blob | null;
  autoPlay?: boolean;
  loop?: boolean;
  onEnded?: () => void;
  onError?: (error: string) => void;
}
```

**Returns**:
```typescript
{
  isPlaying: boolean;
  isLoading: boolean;
  currentTime: number;
  duration: number;
  error: string | null;
  togglePlayPause: () => void;
  play: () => Promise<void>;
  pause: () => void;
  stop: () => void;
  handleSeek: (time: number) => void;
}
```

**Usage Example**:
```jsx
const {
  isPlaying,
  isLoading,
  currentTime,
  duration,
  error,
  togglePlayPause,
  handleSeek
} = useAudioPlayback({ 
  audioBlob: mixedBlob,
  autoPlay: true,
  onEnded: handlePlaybackEnded
});
```

**Dependencies**:
- Uses Web Audio API for playback
- Uses `audioStore` for audio data

## Utility Hooks

### useAudioContext

**Path**: `frontend/src/audio/hooks/useAudioContext.ts`

**Purpose**: Provides access to a shared AudioContext.

**Responsibilities**:
- Creates and manages a singleton AudioContext
- Handles browser audio context limitations
- Provides audio processing nodes

**Returns**:
```typescript
{
  audioContext: AudioContext | null;
  createGainNode: () => GainNode | null;
  createAnalyserNode: () => AnalyserNode | null;
}
```

**Usage Example**:
```jsx
const { audioContext, createGainNode } = useAudioContext();
const gainNode = createGainNode();
```

---

### useMediaDevices

**Path**: `frontend/src/hooks/useMediaDevices.ts`

**Purpose**: Provides access to media input devices.

**Responsibilities**:
- Enumerates available audio input devices
- Handles device selection
- Manages permissions

**Returns**:
```typescript
{
  audioInputDevices: MediaDeviceInfo[];
  selectedInputDevice: string;
  setSelectedInputDevice: (deviceId: string) => void;
  requestPermission: () => Promise<boolean>;
  hasPermission: boolean;
}
```

**Usage Example**:
```jsx
const {
  audioInputDevices,
  selectedInputDevice,
  setSelectedInputDevice,
  hasPermission
} = useMediaDevices();
```

## Implementation Notes

- All hooks follow the React hooks pattern
- Hooks are designed to be reusable across components
- Audio processing is handled through Web Audio API
- State is managed through React's useState and useEffect
- Complex state is managed through Zustand stores
