# Freestyle App - Architecture & Context Documentation

This documentation provides comprehensive context for the Freestyle App's architecture, components, and functionality. It serves as a reference for understanding how different parts of the application work together.

## Application Overview

The Freestyle App is a web application focused on freestyle rap creation with the following core features:

- Recording freestyle raps over beats
- Beat selection and playback
- Audio visualization with waveforms
- Recording preview and playback
- Audio mixing (vocals + beats)
- Audio export

The application is built with:
- **Frontend**: Next.js 15, React 18.2.0, TypeScript
- **Backend**: Node.js server (port 3001)
- **State Management**: Custom stores using Zustand
- **Audio Processing**: Web Audio API

## Directory Structure

```
frontend/
├── public/            # Static assets including beats
├── src/
│   ├── audio/         # Audio-related components, hooks, and utilities
│   │   ├── components/  # Audio-specific UI components
│   │   ├── hooks/       # Audio-related React hooks
│   │   ├── services/    # Audio processing services
│   │   └── utils/       # Audio utility functions
│   ├── components/    # Shared UI components
│   ├── hooks/         # Shared React hooks
│   ├── pages/         # Next.js pages
│   ├── stores/        # State management stores
│   ├── styles/        # Global styles
│   └── ui/            # UI-specific components
│       └── visualizers/  # Visual components like waveforms
├── backend/
│   ├── controllers/   # Request handlers
│   ├── models/        # Data models
│   ├── routes/        # API routes
│   └── services/      # Backend services
```

## Key User Flows

1. **Recording Flow**:
   - User selects a beat
   - User records vocals over the beat
   - Recording is processed and mixed with the beat
   - User is taken to preview mode

2. **Preview Flow**:
   - User can play back the mixed recording
   - User can download the mixed recording
   - User can choose to record again

## Documentation Structure

This context folder is organized as follows:

- **[components/](./components/)**: Documentation for all UI components
- **[pages/](./pages/)**: Documentation for application pages and routing
- **[hooks/](./hooks/)**: Documentation for custom React hooks
- **[stores/](./stores/)**: Documentation for state management
- **[services/](./services/)**: Documentation for service layers
- **[utils/](./utils/)**: Documentation for utility functions
- **[diagrams/](./diagrams/)**: Visual diagrams of application architecture

## Development Workflow

1. Run both frontend and backend:
   ```
   npm run dev
   ```

2. Frontend runs on port 3000, backend on port 3001

## Key Architectural Decisions

1. **Audio Processing**: 
   - Audio recording and processing is handled client-side using Web Audio API
   - Complex audio processing is moved to Web Workers to prevent UI jank

2. **Component Organization**:
   - Audio-specific components are in `audio/components/`
   - Visual components like waveforms are in `ui/visualizers/`
   - Shared UI components are in `components/`

3. **State Management**:
   - Beat selection state is in `beatStore`
   - Recording state is in `audioStore`
   - UI state is managed locally in components

4. **Audio Visualization**:
   - Waveform generation is centralized in `waveformGenerator.ts`
   - Visualization is handled by `WaveformVisualizer` component

## Next Development Steps

1. Complete the audio mixing functionality
2. Enhance the preview playback experience
3. Implement user accounts and saved recordings
4. Add more beats and categorization
