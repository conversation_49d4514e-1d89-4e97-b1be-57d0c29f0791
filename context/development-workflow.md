# Development Workflow

This document provides context for the development workflow of the Freestyle App.

## Project Setup

### Repository Structure

The Freestyle App is organized as follows:

```
freestyle-app/
├── frontend/             # Frontend code
│   ├── public/           # Static assets
│   │   ├── beats/        # Beat audio files
│   │   └── images/       # Image assets
│   └── src/              # Source code
│       ├── audio/        # Audio-related code
│       ├── components/   # Shared components
│       ├── hooks/        # Custom React hooks
│       ├── pages/        # Next.js pages
│       ├── stores/       # State management
│       ├── styles/       # Global styles
│       └── ui/           # UI components
├── backend/              # Backend code
│   ├── controllers/      # Request handlers
│   ├── models/           # Data models
│   ├── routes/           # API routes
│   └── services/         # Backend services
├── context/              # Documentation and context
├── docs/                 # User documentation
└── scripts/              # Utility scripts
```

### Environment Setup

The Freestyle App requires the following environment:

- **Node.js**: v18.x or later
- **npm**: v9.x or later
- **Next.js**: v15.x
- **React**: v18.2.0

### Configuration Files

Key configuration files:

- **package.json**: Dependencies and scripts
- **next.config.js**: Next.js configuration
- **tsconfig.json**: TypeScript configuration
- **.env.local**: Environment variables (not in repo)

## Development Process

### Starting Development

To start development:

```bash
# Install dependencies
npm install

# Start development servers (frontend + backend)
npm run dev
```

This will start:
- Frontend server on port 3000
- Backend server on port 3001

### Code Organization

The codebase follows these organization principles:

1. **Feature-based Organization**: Code is organized by feature rather than type
2. **Component Hierarchy**: Components are organized in a hierarchical structure
3. **Separation of Concerns**: UI, logic, and state are separated
4. **Reusability**: Common code is extracted into reusable components and hooks

### Coding Standards

The codebase follows these coding standards:

1. **TypeScript**: All code is written in TypeScript
2. **Functional Components**: React components use functional style with hooks
3. **ESLint**: Code is linted using ESLint
4. **Prettier**: Code is formatted using Prettier
5. **Testing**: Code is tested using Jest and React Testing Library

### State Management

State management follows these principles:

1. **Zustand**: Global state is managed using Zustand
2. **Local State**: Component-specific state uses React's useState
3. **Persistence**: Persistent state is stored in localStorage
4. **Selectors**: Components use selectors to access only needed state

## Testing

### Testing Approach

The testing approach includes:

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test complete user flows

### Running Tests

To run tests:

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run specific tests
npm test -- -t "component name"
```

### Test Organization

Tests are organized as follows:

```
__tests__/
├── components/   # Component tests
├── hooks/        # Hook tests
├── pages/        # Page tests
└── utils/        # Utility function tests
```

## Deployment

### Build Process

To build the application:

```bash
# Build for production
npm run build

# Start production server
npm start
```

### Deployment Environments

The application has the following environments:

1. **Development**: Local development environment
2. **Staging**: Pre-production environment for testing
3. **Production**: Live environment for end users

### Deployment Process

The deployment process follows these steps:

1. **Build**: Build the application for production
2. **Test**: Run tests to ensure quality
3. **Deploy**: Deploy to the target environment
4. **Verify**: Verify the deployment was successful

## Version Control

### Git Workflow

The Git workflow follows these principles:

1. **Feature Branches**: New features are developed in feature branches
2. **Pull Requests**: Changes are reviewed through pull requests
3. **Code Review**: All code is reviewed before merging
4. **Continuous Integration**: Tests run automatically on pull requests

### Branch Naming

Branch names follow this convention:

- **feature/feature-name**: For new features
- **fix/issue-description**: For bug fixes
- **refactor/component-name**: For refactoring
- **docs/documentation-description**: For documentation

### Commit Messages

Commit messages follow this convention:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation changes
- **style**: Changes that don't affect code behavior
- **refactor**: Code changes that neither fix bugs nor add features
- **test**: Adding or updating tests
- **chore**: Changes to the build process or auxiliary tools

## Documentation

### Code Documentation

Code is documented using:

1. **JSDoc Comments**: Functions and components have JSDoc comments
2. **README Files**: Each directory has a README explaining its purpose
3. **Type Definitions**: TypeScript interfaces and types are well-documented

### User Documentation

User documentation is stored in the `docs/` directory and includes:

1. **User Guide**: Instructions for using the application
2. **FAQ**: Frequently asked questions
3. **Troubleshooting**: Solutions to common problems

### Context Documentation

Context documentation is stored in the `context/` directory and includes:

1. **Architecture**: Overall application architecture
2. **Components**: Detailed component documentation
3. **Workflows**: User and development workflows
4. **Diagrams**: Visual representations of the application

## Performance Optimization

### Performance Considerations

Performance optimization includes:

1. **Code Splitting**: Split code into smaller chunks
2. **Lazy Loading**: Load components only when needed
3. **Memoization**: Memoize expensive calculations
4. **Web Workers**: Offload heavy processing to Web Workers
5. **Caching**: Cache data to reduce processing and network requests

### Performance Monitoring

Performance is monitored using:

1. **Lighthouse**: Measure page performance
2. **React Profiler**: Identify component rendering issues
3. **Chrome DevTools**: Analyze runtime performance
4. **Analytics**: Track real-user performance metrics

## Accessibility

### Accessibility Standards

The application follows these accessibility standards:

1. **WCAG 2.1**: Web Content Accessibility Guidelines
2. **ARIA**: Accessible Rich Internet Applications
3. **Keyboard Navigation**: All functionality is accessible via keyboard
4. **Screen Readers**: Content is accessible to screen readers

### Accessibility Testing

Accessibility is tested using:

1. **Automated Tools**: Lighthouse, axe, etc.
2. **Manual Testing**: Keyboard navigation, screen reader testing
3. **User Testing**: Testing with users with disabilities

## Best Practices

### Development Best Practices

1. **Pull Latest Changes**: Always pull latest changes before starting work
2. **Create Feature Branch**: Work in a feature branch, not directly on main
3. **Small Commits**: Make small, focused commits
4. **Write Tests**: Write tests for new features and bug fixes
5. **Document Changes**: Update documentation for significant changes

### Code Review Best Practices

1. **Be Respectful**: Provide constructive feedback
2. **Be Specific**: Point to specific lines of code
3. **Explain Why**: Explain why changes are needed
4. **Suggest Solutions**: Offer alternative solutions
5. **Check Tests**: Ensure tests cover the changes
