# Audio Services

This document provides context for the audio processing services in the Freestyle App.

## Core Services

### audioProcessor

**Path**: `frontend/src/audio/services/audioProcessor.ts`

**Purpose**: Provides audio processing functionality.

**Key Functions**:

#### `getAudioContext()`
Creates or returns a singleton AudioContext.

```typescript
function getAudioContext(): AudioContext
```

#### `closeAudioContext()`
Closes the AudioContext to free up resources.

```typescript
function closeAudioContext(): void
```

#### `decodeAudioFile(arrayBuffer: ArrayBuffer)`
Decodes an audio file into an AudioBuffer.

```typescript
async function decodeAudioFile(arrayBuffer: ArrayBuffer): Promise<AudioBuffer>
```

#### `fetchAndDecodeAudio(url: string)`
Fetches audio from a URL and decodes it.

```typescript
async function fetchAndDecodeAudio(url: string): Promise<AudioBuffer>
```

#### `mixAudioBuffers(vocals: AudioBuffer, beat: AudioBuffer, vocalGain: number = 1.0, beatGain: number = 0.8)`
Mixes two audio buffers together with specified gain levels.

```typescript
async function mixAudioBuffers(
  vocals: AudioBuffer, 
  beat: AudioBuffer, 
  vocalGain: number = 1.0, 
  beatGain: number = 0.8
): Promise<AudioBuffer>
```

#### `audioBufferToWav(buffer: AudioBuffer)`
Converts an AudioBuffer to a WAV file blob.

```typescript
function audioBufferToWav(buffer: AudioBuffer): Blob
```

#### `processRecordingForPreview(recordingBlob: Blob, beatUrl: string)`
Processes a recording with a beat for preview.

```typescript
async function processRecordingForPreview(
  recordingBlob: Blob, 
  beatUrl: string
): Promise<{ mixedBuffer: AudioBuffer; mixedBlob: Blob }>
```

**Usage Example**:
```javascript
// Mix recording with beat
const recordingBuffer = await decodeAudioFile(await recordingBlob.arrayBuffer());
const beatBuffer = await fetchAndDecodeAudio(beatUrl);
const mixedBuffer = await mixAudioBuffers(recordingBuffer, beatBuffer);
const mixedBlob = audioBufferToWav(mixedBuffer);
```

**Implementation Notes**:
- Uses Web Audio API for processing
- Heavy processing is moved to Web Workers when possible
- Handles cross-browser compatibility issues

---

### AudioRecorder

**Path**: `frontend/src/audio/services/audioRecorder.ts`

**Purpose**: Provides audio recording functionality.

**Class Methods**:

#### `constructor(options?: AudioRecorderOptions)`
Creates a new AudioRecorder instance.

```typescript
constructor(options?: {
  onDataAvailable?: (data: Blob) => void;
  onStop?: (recording: Blob) => void;
  mimeType?: string;
  audioBitsPerSecond?: number;
})
```

#### `async start(deviceId?: string)`
Starts recording from the specified device.

```typescript
async start(deviceId?: string): Promise<void>
```

#### `stop()`
Stops recording and triggers the onStop callback.

```typescript
stop(): void
```

#### `pause()`
Pauses recording.

```typescript
pause(): void
```

#### `resume()`
Resumes recording after pausing.

```typescript
resume(): void
```

#### `getRecordingTime()`
Gets the current recording time in seconds.

```typescript
getRecordingTime(): number
```

**Usage Example**:
```javascript
const recorder = new AudioRecorder({
  onStop: (recordingBlob) => {
    // Handle the completed recording
    processRecording(recordingBlob);
  }
});

// Start recording
await recorder.start(selectedDeviceId);

// Later, stop recording
recorder.stop();
```

**Implementation Notes**:
- Uses MediaRecorder API for recording
- Handles browser compatibility issues
- Manages recording state internally

## Utility Services

### waveformGenerator

**Path**: `frontend/src/audio/utils/waveformGenerator.ts`

**Purpose**: Generates waveform data for visualization.

**Key Functions**:

#### `generatePeaksFromBuffer(buffer: AudioBuffer, numPoints: number, options?: object)`
Generates peaks from an AudioBuffer.

```typescript
function generatePeaksFromBuffer(
  buffer: AudioBuffer, 
  numPoints: number,
  options?: {
    enhanceFactor?: number;
    prioritizeVocals?: boolean;
  }
): Float32Array
```

#### `generatePeaksFromUrl(audioUrl: string | Blob | null, numPoints: number, options?: object)`
Generates peaks from an audio URL or Blob.

```typescript
async function generatePeaksFromUrl(
  audioUrl: string | Blob | null, 
  numPoints: number,
  options?: {
    enhanceFactor?: number;
    prioritizeVocals?: boolean;
    cacheKey?: string;
  }
): Promise<Float32Array>
```

#### `generateDummyPeaks(numPoints: number, options?: object)`
Generates dummy waveform data for fallback.

```typescript
function generateDummyPeaks(
  numPoints: number,
  options?: {
    enhanceFactor?: number;
    addNoise?: boolean;
  }
): Float32Array
```

**Usage Example**:
```javascript
// Generate waveform data from an audio buffer
const peaks = generatePeaksFromBuffer(audioBuffer, 1000, {
  enhanceFactor: 0.8,
  prioritizeVocals: true
});

// Draw the waveform using the peaks data
drawWaveform(peaks);
```

**Implementation Notes**:
- Central source of truth for all waveform generation
- Includes normalization and enhancement for better visualization
- Provides caching for performance optimization

## Implementation Details

### Web Audio API Usage

The audio services make extensive use of the Web Audio API:

```javascript
// Create an audio context
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

// Create nodes
const gainNode = audioContext.createGain();
const analyserNode = audioContext.createAnalyser();

// Connect nodes
sourceNode.connect(gainNode);
gainNode.connect(analyserNode);
analyserNode.connect(audioContext.destination);

// Process audio
gainNode.gain.value = 0.8; // Set volume to 80%
```

### Audio Mixing Process

The mixing process follows these steps:

1. Decode both the vocals and beat into AudioBuffers
2. Create a new AudioBuffer for the mixed output
3. Mix the samples from both inputs with appropriate gain
4. Convert the mixed AudioBuffer to a WAV file

### Web Workers

Heavy processing is offloaded to Web Workers:

```javascript
// Create a worker
const worker = new Worker('/workers/audioProcessor.js');

// Send data to the worker
worker.postMessage({
  command: 'mix',
  vocalsBuffer: vocalsBuffer,
  beatBuffer: beatBuffer,
  vocalGain: 1.0,
  beatGain: 0.8
});

// Receive results from the worker
worker.onmessage = (e) => {
  const { mixedBuffer } = e.data;
  // Use the mixed buffer
};
```

## Best Practices

1. **Close AudioContext**: Always close the AudioContext when not in use to free up resources
2. **Error Handling**: Implement robust error handling for audio operations
3. **Fallbacks**: Provide fallbacks for unsupported features
4. **Performance**: Use Web Workers for heavy processing
5. **Memory Management**: Clean up audio resources when components unmount
