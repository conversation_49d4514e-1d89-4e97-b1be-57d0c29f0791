const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  output: "standalone",
  poweredByHeader: false,

  // Configure webpack to support Web Workers and Audio Worklets
  webpack: (config, { isServer, dev }) => {
    // Support for Web Workers
    config.module.rules.push({
      test: /\.worker\.ts$/,
      use: { loader: 'worker-loader' },
    });

    // Support for Audio Worklets
    config.module.rules.push({
      test: /\.worklet\.ts$/,
      use: { loader: 'worklet-loader' },
    });

    // Fix for AudioWorklet and Worker issues with Next.js
    if (!isServer) {
      config.output.globalObject = 'self';
    }

    return config;
  },

  // Environment variables that will be shared with the client-side
  publicRuntimeConfig: {
    apiUrl: process.env.NEXT_PUBLIC_API_URL,
  },

  // Headers
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "X-DNS-Prefetch-Control",
            value: "on",
          },
          {
            key: "Strict-Transport-Security",
            value: "max-age=63072000; includeSubDomains; preload",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
    ];
  },

  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "http://localhost:3001/api/:path*",
      },
      {
        source: "/rails/active_storage/:path*",
        destination: "http://localhost:3001/rails/active_storage/:path*",
      },
      {
        source: "/healthz",
        destination: "/api/healthz",
      },
    ];
  },
};

module.exports = withBundleAnalyzer(nextConfig);
