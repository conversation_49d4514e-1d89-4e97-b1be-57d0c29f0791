{"name": "freestyle-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:audio": "jest --testPathPattern=audio", "test:ui": "jest --testPathPattern=ui", "test:integration": "jest --testPathPattern=integration", "analyze": "ANALYZE=true next build", "dev:all": "concurrently \"npm run dev\" \"cd ../backend && rm -f tmp/pids/server.pid && rails s -p 3001\""}, "dependencies": {"@next/bundle-analyzer": "^15.3.3", "@types/file-saver": "^2.0.7", "comlink": "^4.4.2", "file-saver": "^2.0.5", "next": "^15.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "wavesurfer.js": "^7.9.5", "zustand": "^5.0.4"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.28.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.17.12", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "eslint-plugin-react": "^7.37.5", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8.5.4", "prettier": "^3.5.3", "react-icons": "^5.5.0", "tailwindcss": "^4.1.8", "typescript": "~5.8.3"}, "description": "Next.js frontend for the Freestyle App", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}