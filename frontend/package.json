{"name": "freestyle-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true next build", "dev:all": "concurrently \"npm run dev\" \"cd ../backend && rm -f tmp/pids/server.pid && rails s -p 3001\""}, "dependencies": {"@next/bundle-analyzer": "^13.4.19", "next": "^15.4.19", "react": "18.2.0", "react-dom": "18.2.0", "wavesurfer.js": "^7.9.5"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.26.0", "@types/node": "^20.17.12", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.14", "concurrently": "^9.1.2", "eslint": "^9.26.0", "eslint-config-next": "^15.3.1", "eslint-plugin-react": "^7.37.5", "postcss": "^8.4.21", "prettier": "^3.5.3", "react-icons": "^5.5.0", "tailwindcss": "^3.3.1", "typescript": "~5.4.3"}, "description": "Next.js frontend for the Freestyle App", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}