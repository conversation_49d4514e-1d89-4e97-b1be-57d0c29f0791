/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to login a user
       * @example cy.login('<EMAIL>', 'password123')
       */
      login(email: string, password: string): Chainable<void>;

      /**
       * Custom command to intercept network requests
       * @example cy.intercept('GET', '/api/beats', { fixture: 'beats.json' })
       */
      // intercept(method: string, url: string, response?: any): Chainable<void>;

      /**
       * Custom command to stub network requests
       * @example cy.stub('GET', '/api/beats', { fixture: 'beats.json' })
       */
      stub(method: string, url: string, response?: any): Chainable<void>;
    }
  }
}

// Export empty object to make this a module
export {};
