/// <reference types="cypress" />

describe("Audio Processing E2E", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.intercept("GET", /\/api\/beats.*/, {
      statusCode: 200,
      body: [
        {
          id: 1,
          title: "Test Beat",
          producer_name: "Test Producer",
          bpm: 90,
          key: "C",
          audio_url: "https://example.com/test-beat.mp3",
          genre: "Hip Hop",
          is_free: true,
          status: "active",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          audio_file: { url: "https://example.com/test-beat.mp3" },
        },
      ],
    }).as("getBeats");
    cy.visit("/auth/login");
    cy.get(
      'input[type="email"], input[name="email"], [data-testid="email-input"]',
    ).type("<EMAIL>");
    cy.get(
      'input[type="password"], input[name="password"], [data-testid="password-input"]',
    ).type("testpassword");
    cy.get('button[type="submit"], [data-testid="login-button"]').click();
    cy.visit("/freestyle");
    cy.url().then((url) => cy.log("Current URL:", url));
    cy.getCookies().then((cookies) =>
      cy.log("Cookies:", JSON.stringify(cookies)),
    );
    cy.document().then((doc) => {
      cy.log("Full body HTML:", doc.body.innerHTML.slice(0, 3000));
      cy.log("All visible text:", doc.body.innerText.slice(0, 2000));
    });
    cy.window().then((win) => {
      win.addEventListener("error", (e) => {
        // eslint-disable-next-line no-console
        console.error("BROWSER ERROR:", e.message);
      });
      win.addEventListener("unhandledrejection", (e) => {
        // eslint-disable-next-line no-console
        console.error("BROWSER PROMISE REJECTION:", e.reason);
      });
    });
    cy.intercept({ url: "**", middleware: true }, (req) => {
      req.on("response", (res) => {
        cy.log(`Network: ${req.method} ${req.url} -> ${res.statusCode}`);
      });
    });
  });

  it("should render the beat player or log the DOM if missing", () => {
    cy.wait(15000); // Hard timeout for page/data to load
    cy.url().then((url) => {
      if (!url.includes("/freestyle")) {
        cy.log("Redirected! Current URL:", url);
        throw new Error("Redirected from /freestyle to " + url);
      }
    });
    cy.get("body").then(($body) => {
      if ($body.find('[data-testid="beat-player"]').length === 0) {
        cy.log(
          "No beat-player found. Full body HTML:",
          $body.html().slice(0, 3000),
        );
        cy.log("All visible text:", $body.text().slice(0, 2000));
        cy.url().then((url) => cy.log("Current URL:", url));
        throw new Error("Beat player not found on /freestyle");
      }
    });
    cy.get('[data-testid="beat-player"]', { timeout: 10000 }).should("exist");
  });

  it("should play and record if controls exist", () => {
    cy.get('[data-testid="play-button"]', { timeout: 10000 })
      .should("exist")
      .and("not.be.disabled")
      .click();
    cy.wait(500);
    cy.get('[data-testid="record-button"]', { timeout: 10000 })
      .should("exist")
      .and("not.be.disabled")
      .click();
    cy.wait(1000);
    cy.get('[data-testid="audio-visualizer"]', { timeout: 10000 }).should(
      "exist",
    );
  });

  it("should show error or loading state if present", () => {
    cy.get("body").then(($body) => {
      if ($body.find('[data-testid*="error"], .error').length) {
        cy.log(
          "Error state:",
          $body.find('[data-testid*="error"], .error').text(),
        );
      }
      if ($body.find('[data-testid="loading"], .loading').length) {
        cy.log(
          "Loading state:",
          $body.find('[data-testid="loading"], .loading').text(),
        );
      }
    });
  });

  it("should analyze beat patterns during playback", () => {
    cy.get('[data-testid="play-button"]').click();
    cy.wait(1000);
    cy.get('[data-testid="bpm-display"]').should("exist").and("not.be.empty");
    cy.get('[data-testid="beat-markers"]')
      .should("exist")
      .find(".beat-marker")
      .should("have.length.gt", 0);
    cy.get('[data-testid="analysis-timestamp"]')
      .invoke("text")
      .then((timestamp1) => {
        cy.wait(1000);
        cy.get('[data-testid="analysis-timestamp"]')
          .invoke("text")
          .should("not.eq", timestamp1);
      });
  });

  it("should persist audio processing settings", () => {
    cy.get('[data-testid="settings-button"]').click();
    cy.get('[data-testid="quality-selector"]').select("high");
    cy.get('[data-testid="visualization-type"]').select("frequency");
    cy.get('[data-testid="save-settings"]').click();
    cy.reload();
    cy.get('[data-testid="settings-button"]').click();
    cy.get('[data-testid="quality-selector"]').should("have.value", "high");
    cy.get('[data-testid="visualization-type"]').should(
      "have.value",
      "frequency",
    );
  });
});
