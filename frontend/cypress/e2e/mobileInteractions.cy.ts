/// <reference types="cypress" />
import sinon from "sinon";

describe("Mobile Interactions E2E", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    // Set viewport to mobile size
    cy.viewport("iphone-x", "portrait");

    // Mock audio context and media devices
    cy.window().then((win) => {
      cy.stub(win, "AudioContext")
        .as("AudioContext")
        .returns({
          createAnalyser: () => ({
            connect: cy.stub(),
            disconnect: cy.stub(),
            frequencyBinCount: 1024,
            getByteFrequencyData: cy.stub(),
            fftSize: 2048,
          }),
          createMediaStreamSource: cy.stub(),
          close: cy.stub(),
        });

      cy.stub(win.navigator.mediaDevices, "getUserMedia").resolves({
        getTracks: () => [
          {
            stop: cy.stub(),
          },
        ],
      });
    });

    // Visit the freestyle page
    cy.visit("/freestyle");
  });

  it("should handle touch interactions correctly", () => {
    // Test play/pause button touch
    cy.get('[data-testid="play-button"]')
      .trigger("touchstart")
      .trigger("touchend");

    cy.get('[data-testid="beat-audio"]').should("have.prop", "paused", false);

    // Test record button touch
    cy.get('[data-testid="record-button"]')
      .trigger("touchstart")
      .trigger("touchend");

    cy.get('[data-testid="mobile-audio-visualizer"]').should("exist");

    // Test visualization canvas touch
    cy.get('[data-testid="visualization-canvas"]')
      .trigger("touchstart", { touches: [{ clientX: 100, clientY: 100 }] })
      .trigger("touchmove", { touches: [{ clientX: 150, clientY: 150 }] })
      .trigger("touchend");
  });

  it("should handle mobile orientation changes", () => {
    // Start recording
    cy.get('[data-testid="record-button"]').click();

    // Change to landscape
    cy.viewport("iphone-x", "landscape");

    // Check if visualization adjusts
    cy.get('[data-testid="visualization-canvas"]')
      .should("have.css", "width")
      .and("match", /812px/); // iPhone X landscape width

    // Change back to portrait
    cy.viewport("iphone-x", "portrait");

    // Check if visualization adjusts back
    cy.get('[data-testid="visualization-canvas"]')
      .should("have.css", "width")
      .and("match", /375px/); // iPhone X portrait width
  });

  it("should handle mobile quality settings", () => {
    // Open settings
    cy.get('[data-testid="settings-button"]')
      .trigger("touchstart")
      .trigger("touchend");

    // Change quality to low for better performance
    cy.get('[data-testid="quality-selector"]').select("low");

    // Start recording
    cy.get('[data-testid="record-button"]').click();

    // Check if visualization is working with low quality
    cy.get('[data-testid="visualization-canvas"]').should(
      "have.attr",
      "data-quality",
      "low",
    );
  });

  it("should handle offline mode gracefully", () => {
    // Simulate offline
    cy.window().then((win) => {
      win.dispatchEvent(new Event("offline"));
    });

    // Try to start recording
    cy.get('[data-testid="record-button"]').click();

    // Check for offline message
    cy.get('[data-testid="offline-message"]')
      .should("exist")
      .and("contain.text", "You are currently offline");

    // Simulate coming back online
    cy.window().then((win) => {
      win.dispatchEvent(new Event("online"));
    });

    // Check if offline message is removed
    cy.get('[data-testid="offline-message"]').should("not.exist");
  });

  it("should handle mobile gestures", () => {
    // Test swipe to change visualization type
    cy.get('[data-testid="visualization-canvas"]')
      .trigger("touchstart", { touches: [{ clientX: 300, clientY: 150 }] })
      .trigger("touchmove", { touches: [{ clientX: 100, clientY: 150 }] })
      .trigger("touchend");

    // Check if visualization type changed
    cy.get('[data-testid="visualization-type"]').should(
      "have.value",
      "frequency",
    );

    // Test pinch to zoom
    cy.get('[data-testid="visualization-canvas"]')
      .trigger("touchstart", {
        touches: [
          { clientX: 100, clientY: 150 },
          { clientX: 200, clientY: 150 },
        ],
      })
      .trigger("touchmove", {
        touches: [
          { clientX: 50, clientY: 150 },
          { clientX: 250, clientY: 150 },
        ],
      })
      .trigger("touchend");

    // Check if zoom level changed
    cy.get('[data-testid="visualization-canvas"]').should(
      "have.attr",
      "data-zoom-level",
    );
  });

  it("should persist mobile settings", () => {
    // Change visualization type
    cy.get('[data-testid="settings-button"]').click();
    cy.get('[data-testid="visualization-type"]').select("circular");

    // Change quality
    cy.get('[data-testid="quality-selector"]').select("low");

    // Reload page
    cy.reload();

    // Check if settings persisted
    cy.get('[data-testid="visualization-type"]').should(
      "have.value",
      "circular",
    );
    cy.get('[data-testid="quality-selector"]').should("have.value", "low");
  });
});
