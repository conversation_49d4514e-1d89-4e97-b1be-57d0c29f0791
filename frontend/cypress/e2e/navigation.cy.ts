/// <reference types="cypress" />

describe("Navigation Flow", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.window().then((win) => {
      win.localStorage.clear();
    });
  });

  it("navigates through main pages as a guest user", () => {
    cy.visit("/");

    // Check landing page content
    cy.findByText("Freestyle AI").should("exist");
    cy.findByText("Try It Now").should("exist");
    cy.findByText("Sign Up").should("exist");

    // Try navigation to freestyle
    cy.findByText("Try It Now").click();
    cy.url().should("include", "/freestyle");

    // Try navigation to register
    cy.visit("/");
    cy.findByText("Sign Up").click();
    cy.url().should("include", "/register");
  });

  it("shows correct navigation items for authenticated users", () => {
    // Register and login
    cy.register("nav_user", "<EMAIL>", "password123");
    cy.visit("/");

    // Should see authenticated nav items
    cy.findByText("Dashboard").should("exist");
    cy.findByText("Start Freestyle Session").should("exist");

    // Navigate to dashboard
    cy.findByText("Dashboard").click();
    cy.url().should("include", "/dashboard");

    // Check dashboard navigation
    cy.findByText("New Session").should("exist");
    cy.findByText("nav_user's Dashboard").should("exist");
  });

  it("handles protected routes correctly", () => {
    // Try accessing protected route as guest
    cy.visit("/dashboard");

    // Should be redirected to login
    cy.url().should("include", "/login");

    // Login and try again
    cy.login("<EMAIL>", "password123");
    cy.visit("/dashboard");

    // Should now have access
    cy.url().should("include", "/dashboard");
  });

  it("maintains navigation state during freestyle session", () => {
    cy.register("session_user", "<EMAIL>", "password123");
    cy.visit("/freestyle");

    // Start a session
    cy.findAllByTestId("beat-card").first().click();
    cy.findByTestId("record-button").click();

    // Try navigating away
    cy.findByText("Dashboard").click();

    // Should show confirmation dialog
    cy.findByText(/Are you sure you want to leave/i).should("exist");

    // Cancel navigation
    cy.findByText("Cancel").click();
    cy.url().should("include", "/freestyle");

    // Stop recording and try again
    cy.findByTestId("stop-button").click();
    cy.findByText("Dashboard").click();

    // Should navigate without warning
    cy.url().should("include", "/dashboard");
  });

  it("handles subscription navigation correctly", () => {
    cy.register("free_user", "<EMAIL>", "password123");
    cy.visit("/dashboard");

    // Click upgrade button
    cy.findByText("Upgrade to Premium").click();
    cy.url().should("include", "/subscription");

    // Check subscription page content
    cy.findByText(/Choose your plan/i).should("exist");
    cy.findByText(/Premium/i).should("exist");

    // Navigate back to dashboard
    cy.findByText("Dashboard").click();
    cy.url().should("include", "/dashboard");
  });

  it("navigates to register page when Unlock Premium is clicked on join page", () => {
    cy.visit("/auth/join");
    cy.get('[data-testid="unlock-premium"]').click();
    cy.url().should("include", "/auth/register");
  });
});
