/// <reference types="cypress" />
import {
  mockMediaRecorder,
  cypressMockSpeechRecognition,
} from "../support/mocks";

describe("Recording Flow", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.intercept("GET", "/api/beats*", { fixture: "beats.json" }).as(
      "getBeats",
    );
    cy.intercept("POST", "/api/freestyle_sessions", {
      fixture: "session.json",
    }).as("createSession");
    cy.intercept("GET", "/api/freestyle_sessions/*/ai_suggestions", {
      fixture: "suggestions.json",
    }).as("getSuggestions");
    cy.intercept("POST", "/api/users/sign_in", {
      statusCode: 200,
      body: {
        token: "fake-jwt-token",
        user: {
          id: 1,
          email: "<EMAIL>",
        },
      },
    }).as("login");
    cy.intercept("POST", "/api/users", {
      statusCode: 201,
      body: {
        user: {
          id: 1,
          email: "<EMAIL>",
          username: "testuser",
          is_premium: false,
        },
      },
    }).as("register");
    cy.intercept("GET", "/api/profile", {
      statusCode: 200,
      body: {
        id: 1,
        email: "<EMAIL>",
        username: "testuser",
        is_premium: false,
      },
    }).as("getProfile");

    // Set up mocks
    mockMediaRecorder();
    cypressMockSpeechRecognition();

    // Login and visit freestyle page
    cy.login("<EMAIL>", "password123");
    cy.visit("/freestyle");
    cy.wait("@getBeats");
  });

  it("completes a full recording session with AI suggestions", () => {
    // Select a beat
    cy.get('[data-testid="beat-card"]').first().click();
    cy.wait("@createSession");

    // Verify beat player loaded
    cy.get('[data-testid="beat-player"]').should("be.visible");
    cy.get('[data-testid="play-button"]').should("be.visible");

    // Start recording
    cy.get('[data-testid="record-button"]').click();
    cy.get('[data-testid="recording-indicator"]').should("be.visible");
    cy.get('[data-testid="recording-timer"]').should("be.visible");

    // Play the beat
    cy.get('[data-testid="play-button"]').click();
    cy.get('[data-testid="beat-audio"]').should("have.prop", "paused", false);

    // Wait for AI suggestions
    cy.wait("@getSuggestions");
    cy.get('[data-testid="suggestions-container"]').should("be.visible");
    cy.get('[data-testid="suggestion-item"]').should("have.length.at.least", 1);

    // Use a suggestion
    cy.get('[data-testid="suggestion-item"]')
      .first()
      .within(() => {
        cy.get("button").contains("Use").click();
        cy.get("button").should("have.text", "Used");
      });

    // Stop recording
    cy.get('[data-testid="record-button"]').click();
    cy.get('[data-testid="recording-indicator"]').should("not.exist");

    // Verify recording saved
    cy.get('[data-testid="recording-saved"]').should("be.visible");
  });

  it("handles errors during recording gracefully", () => {
    // Mock recording error
    cy.window().then((win) => {
      win.MediaRecorder.prototype.start = () => {
        throw new Error("Recording failed");
      };
    });

    // Select a beat
    cy.get('[data-testid="beat-card"]').first().click();
    cy.wait("@createSession");

    // Attempt to start recording
    cy.get('[data-testid="record-button"]').click();

    // Verify error handling
    cy.get('[role="alert"]')
      .should("be.visible")
      .and("contain.text", "Recording failed");
  });

  it("maintains recording state during beat transitions", () => {
    // Select first beat
    cy.get('[data-testid="beat-card"]').first().click();
    cy.wait("@createSession");

    // Start recording
    cy.get('[data-testid="record-button"]').click();
    cy.get('[data-testid="recording-indicator"]').should("be.visible");

    // Switch to next beat
    cy.get('[data-testid="next-beat"]').click();

    // Verify recording continues
    cy.get('[data-testid="recording-indicator"]').should("be.visible");
    cy.get('[data-testid="recording-timer"]').should("be.visible");
  });

  it("syncs recording with beat playback", () => {
    // Select a beat
    cy.get('[data-testid="beat-card"]').first().click();
    cy.wait("@createSession");

    // Start recording
    cy.get('[data-testid="record-button"]').click();

    // Play and pause beat
    cy.get('[data-testid="play-button"]').click();
    cy.get('[data-testid="recording-indicator"]').should(
      "have.attr",
      "data-playing",
      "true",
    );

    cy.get('[data-testid="pause-button"]').click();
    cy.get('[data-testid="recording-indicator"]').should(
      "have.attr",
      "data-playing",
      "false",
    );
  });

  it("handles premium features correctly", () => {
    // Login as premium user
    cy.login("<EMAIL>", "password123");
    cy.visit("/freestyle");
    cy.wait("@getBeats");

    // Select a beat
    cy.get('[data-testid="beat-card"]').first().click();
    cy.wait("@createSession");

    // Verify premium features
    cy.get('[aria-label="Toggle settings"]').click();
    cy.get('[data-testid="premium-settings"]').within(() => {
      cy.get("button").contains("Auto Switch to Next Beat").click();
      cy.get('input[type="number"]').type("10");
    });

    // Start recording with premium settings
    cy.get('[data-testid="record-button"]').click();
    cy.get('[data-testid="recording-indicator"]').should("be.visible");

    // Verify auto-switch near end of beat
    cy.clock();
    cy.tick(170000); // 10 seconds before end
    cy.get('[data-testid="next-beat-countdown"]').should("be.visible");
  });
});
