/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

import { FreestyleSessionPage } from "../pages/freestyle-session.page";
import { LoginPage } from "../pages/login.page";
import { PremiumPage } from "../pages/premium.page";

describe("Freestyle Session Flow", () => {
  const freestylePage = new FreestyleSessionPage();
  const loginPage = new LoginPage();
  const premiumPage = new PremiumPage();

  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    // Mock user authentication
    cy.intercept("POST", "/api/users/sign_in", {
      statusCode: 200,
      body: {
        token: "fake-jwt-token",
        user: {
          id: 1,
          email: "<EMAIL>",
        },
      },
    }).as("login");

    // Mock beats API
    cy.intercept("GET", "/api/beats", {
      statusCode: 200,
      body: [
        {
          id: 1,
          title: "Test Beat",
          producer_name: "Test Producer",
          bpm: 90,
          key: "C",
          audio_url: "/test-beat.mp3",
          genre: "Hip Hop",
          is_free: true,
          status: "approved",
        },
      ],
    }).as("getBeats");

    // Mock AI suggestions API
    cy.intercept("POST", "/api/freestyle_sessions/*/ai_suggestions", {
      statusCode: 200,
      body: {
        id: 1,
        content: "Test suggestion",
        timestamp: 0,
      },
    }).as("createSuggestion");

    // Visit the freestyle page
    cy.visit("/freestyle");

    // Reset any previous state
    cy.window().then((win) => {
      win.localStorage.clear();
    });
  });

  it("should allow guest user to try freestyle session", () => {
    freestylePage.verifyGuestAccess();
    freestylePage.selectBeat("default");
    freestylePage.startRecording();
    cy.wait(5000); // Record for 5 seconds
    freestylePage.stopRecording();
    freestylePage.verifyRecordingSaved();
  });

  it("should complete full freestyle session as premium user", () => {
    loginPage.loginAsPremiumUser();
    freestylePage.selectBeat("premium");
    freestylePage.startRecording();
    cy.wait(10000); // Record for 10 seconds
    freestylePage.stopRecording();
    freestylePage.saveRecording("My Premium Freestyle");
    freestylePage.verifyRecordingSaved();
  });

  it("should show AI suggestions during session", () => {
    loginPage.loginAsPremiumUser();
    freestylePage.selectBeat("premium");
    freestylePage.startRecording();
    cy.wait(5000);
    freestylePage.verifyAISuggestions();
    freestylePage.stopRecording();
  });

  it("should enforce limitations for free-tier users", () => {
    loginPage.loginAsFreeUser();
    freestylePage.selectBeat("default");
    freestylePage.startRecording();
    cy.wait(30000); // Try to record for 30 seconds
    freestylePage.verifyRecordingLimit();
    freestylePage.verifyPremiumPrompt();
  });

  it("should handle errors gracefully", () => {
    cy.intercept("POST", "/api/recording", {
      statusCode: 500,
      body: { error: "Server error" },
    }).as("saveRecording");

    loginPage.loginAsPremiumUser();
    freestylePage.selectBeat("premium");
    freestylePage.startRecording();
    cy.wait(5000);
    freestylePage.stopRecording();
    freestylePage.saveRecording("Error Test");
    freestylePage.verifyErrorHandling();
  });

  it("should maintain playback during long sessions", () => {
    loginPage.loginAsPremiumUser();
    freestylePage.selectBeat("premium");
    freestylePage.startRecording();

    // Test for 5 minutes
    for (let i = 0; i < 5; i++) {
      cy.wait(60000); // Wait 1 minute
      freestylePage.verifyPlaybackActive();
    }

    freestylePage.stopRecording();
    freestylePage.saveRecording("Long Session Test");
  });

  it("should integrate AI suggestions effectively", () => {
    loginPage.loginAsPremiumUser();
    freestylePage.selectBeat("premium");
    freestylePage.startRecording();

    // Wait for and verify multiple AI suggestions
    for (let i = 0; i < 3; i++) {
      cy.wait(10000);
      freestylePage.verifyAISuggestions();
    }

    freestylePage.stopRecording();
  });

  it("should handle premium features appropriately", () => {
    loginPage.loginAsPremiumUser();
    freestylePage.verifyPremiumFeatures();
    freestylePage.selectBeat("premium");
    freestylePage.startRecording();
    cy.wait(5000);
    freestylePage.stopRecording();
    freestylePage.saveRecording("Premium Test");
  });

  it("should manage session limits for free users", () => {
    loginPage.loginAsFreeUser();
    freestylePage.verifyFreeLimitations();

    // Test multiple sessions
    for (let i = 0; i < 3; i++) {
      freestylePage.selectBeat("default");
      freestylePage.startRecording();
      cy.wait(10000);
      freestylePage.stopRecording();
      freestylePage.verifySessionCount(i + 1);
    }

    freestylePage.verifySessionLimitReached();
  });

  it("maintains continuous playback during long sessions", () => {
    cy.wait("@getBeats");

    // Select beat and start playback
    cy.get('[data-testid="beat-item"]').first().click();
    cy.get("button").contains("Play").click();

    // Start recording
    cy.get('[data-testid="record-button"]').click();

    // Verify beat continues playing for extended duration
    cy.get('[data-testid="beat-audio"]')
      .invoke("prop", "paused")
      .should("eq", false);

    // Wait for multiple beat loops
    cy.wait(10000);

    // Verify beat is still playing
    cy.get('[data-testid="beat-audio"]')
      .invoke("prop", "paused")
      .should("eq", false);
  });

  it("integrates AI suggestions with recording flow", () => {
    cy.wait("@getBeats");

    // Start session
    cy.get('[data-testid="beat-item"]').first().click();
    cy.get("button").contains("Play").click();
    cy.get('[data-testid="record-button"]').click();

    // Mock multiple AI suggestions
    const suggestions = [
      "First suggestion",
      "Second suggestion",
      "Third suggestion",
    ];

    suggestions.forEach((content, i) => {
      cy.intercept("POST", "/api/freestyle_sessions/*/ai_suggestions", {
        statusCode: 200,
        body: {
          id: i + 1,
          content,
          timestamp: i * 5,
        },
      }).as(`suggestion${i}`);
    });

    // Verify suggestions appear and can be marked as used
    suggestions.forEach((content, i) => {
      cy.wait(`@suggestion${i}`);
      cy.get('[data-testid="suggestions-container"]').should(
        "have.text",
        content,
      );

      cy.get(`[data-testid="suggestion-${i + 1}"]`)
        .find("button")
        .contains("Use")
        .click();

      cy.get(`[data-testid="suggestion-${i + 1}"]`).should(
        "have.class",
        "used",
      );
    });
  });

  it("handles premium feature access correctly", () => {
    // Mock free user
    cy.login("<EMAIL>", "password123");
    cy.wait("@getBeats");

    // Try to access premium beat
    cy.intercept("GET", "/api/beats/premium/*", {
      statusCode: 403,
      body: { error: "Premium subscription required" },
    }).as("getPremiumBeat");

    cy.get('[data-testid="premium-beat"]').first().click();

    // Verify upgrade prompt
    cy.get('[data-testid="upgrade-modal"]')
      .should("be.visible")
      .and("have.text", "Upgrade to Premium");

    // Mock premium user
    cy.login("<EMAIL>", "password123");
    cy.intercept("GET", "/api/beats/premium/*", {
      statusCode: 200,
      body: {
        id: 2,
        title: "Premium Beat",
        is_free: false,
        // ... other beat properties
      },
    }).as("getPremiumBeatSuccess");

    // Verify premium access
    cy.get('[data-testid="premium-beat"]').first().click();
    cy.get('[data-testid="beat-player"]').should("be.visible");
  });

  it("handles session limits for free users", () => {
    cy.login("<EMAIL>", "password123");

    // Mock session count check
    cy.intercept("GET", "/api/freestyle_sessions/count", {
      statusCode: 200,
      body: { count: 5 }, // Maximum free sessions
    }).as("sessionCount");

    // Try to start new session
    cy.get('[data-testid="beat-item"]').first().click();

    // Verify limit reached message
    cy.get('[data-testid="session-limit-message"]')
      .should("be.visible")
      .and("have.text", "Daily session limit reached");
  });

  it("maintains state during network interruptions", () => {
    cy.wait("@getBeats");
    cy.get('[data-testid="beat-item"]').first().click();
    cy.get("button").contains("Play").click();
    cy.get('[data-testid="record-button"]').click();

    // Simulate network failure during recording
    cy.intercept("POST", "/api/freestyle_sessions/*/ai_suggestions", {
      forceNetworkError: true,
    }).as("failedSuggestion");

    // Verify recording continues
    cy.get('[data-testid="recording-indicator"]').should("be.visible");

    cy.get('[data-testid="beat-audio"]')
      .invoke("prop", "paused")
      .should("eq", false);

    // Verify offline indicator
    cy.get('[data-testid="network-status"]').should("have.text", "Offline");

    // Simulate network recovery
    cy.intercept("POST", "/api/freestyle_sessions/*/ai_suggestions", {
      statusCode: 200,
      body: {
        id: 1,
        content: "Back online suggestion",
        timestamp: 0,
      },
    }).as("recoveredSuggestion");

    // Verify recovery
    cy.get('[data-testid="network-status"]').should("have.text", "Online");

    cy.wait("@recoveredSuggestion");
    cy.get('[data-testid="suggestions-container"]').should(
      "have.text",
      "Back online suggestion",
    );
  });

  it("handles audio device changes gracefully", () => {
    cy.wait("@getBeats");
    cy.get('[data-testid="beat-item"]').first().click();

    // Start recording
    cy.get('[data-testid="record-button"]').click();

    // Simulate device disconnection
    cy.window().then((win) => {
      const devicechange = new Event("devicechange");
      win.navigator.mediaDevices.dispatchEvent(devicechange);
    });

    // Verify device selection prompt
    cy.get('[data-testid="device-select-modal"]').should("be.visible");

    // Verify recording state is preserved
    cy.get('[data-testid="recording-indicator"]').should("be.visible");

    cy.get('[data-testid="beat-audio"]')
      .invoke("prop", "paused")
      .should("eq", false);
  });

  it("saves session data when browser closes unexpectedly", () => {
    cy.wait("@getBeats");
    cy.get('[data-testid="beat-item"]').first().click();
    cy.get("button").contains("Play").click();
    cy.get('[data-testid="record-button"]').click();

    // Record for a few seconds
    cy.wait(3000);

    // Verify local storage backup
    cy.window().then((win) => {
      const stored = win.localStorage.getItem("freestyle_session_backup");
      cy.wrap(stored).should("not.be.null");

      if (stored) {
        const backup = JSON.parse(stored);
        cy.wrap(backup).should("have.property", "recording");
        cy.wrap(backup).should("have.property", "timestamp");
      }
    });

    // Simulate page reload
    cy.reload();

    // Verify recovery prompt
    cy.get('[data-testid="recovery-prompt"]')
      .should("be.visible")
      .and("have.text", "Recover previous session?");
  });
});
