/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

import "./commands";

// Import testing-library custom commands
import "@testing-library/cypress/add-commands";

// Handle uncaught exceptions
Cypress.on("uncaught:exception", (err: Error) => {
  // returning false here prevents <PERSON><PERSON> from failing the test
  return false;
});

declare global {
  namespace Cypress {
    interface Chainable {
      // Add custom commands here if needed
    }
  }
}

// Export empty object to make this a module
export {};

// Add any additional Cypress configuration here

Cypress.on("window:before:load", (win) => {
  const origError = win.console.error;
  win.console.error = (...args) => {
    Cypress.log({ name: "console.error", message: args });
    if (origError) origError.apply(win.console, args);
  };
});
