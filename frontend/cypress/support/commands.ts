/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

import "@testing-library/cypress/add-commands";
import "./mocks";
import { mockMediaRecorder } from "./mocks";

declare global {
  namespace Cypress {
    interface Chainable {
      login(email: string, password: string): Chainable<void>;
      register(
        username: string,
        email: string,
        password: string,
      ): Chainable<void>;
      logout(): Chainable<void>;
      intercept(method: string, url: string, response?: any): Chainable<void>;
      stub(method: string, url: string, response?: any): Chainable<void>;
      mockBeats(beats?: any[]): Chainable<void>;
      mockAISuggestions(suggestions?: any[]): Chainable<void>;
      mockMediaRecorder(): Chainable<void>;
    }
  }
}

// Custom command to login
Cypress.Commands.add("login", (email: string, password: string) => {
  cy.visit("/login");
  cy.findByTestId("email-input").type(email);
  cy.findByTestId("password-input").type(password);
  cy.findByTestId("login-button").click();
});

// Custom command to register
Cypress.Commands.add(
  "register",
  (username: string, email: string, password: string) => {
    cy.intercept("POST", "/api/users", {
      statusCode: 201,
      body: {
        user: {
          id: 1,
          email,
          username,
          is_premium: email.includes("premium"),
        },
      },
    }).as("register");

    cy.visit("/register");
    cy.get('[data-testid="username-input"]').type(username);
    cy.get('[data-testid="email-input"]').type(email);
    cy.get('[data-testid="password-input"]').type(password);
    cy.get('[data-testid="register-button"]').click();
    cy.wait("@register");
  },
);

// Custom command to logout
Cypress.Commands.add("logout", () => {
  window.localStorage.removeItem("token");
  cy.visit("/");
});

// Custom command to mock beat data
Cypress.Commands.add("mockBeats" as const, (beats: any[] = []) => {
  cy.intercept("GET", "/api/beats", {
    statusCode: 200,
    body:
      (beats as any[]).length > 0
        ? beats
        : [
            {
              id: 1,
              title: "Test Beat",
              producer_name: "Test Producer",
              bpm: 90,
              key: "C",
              audio_url: "/test-beat.mp3",
              genre: "Hip Hop",
              is_free: true,
              status: "approved",
            },
          ],
  }).as("getBeats");
});

// Custom command to mock AI suggestions
Cypress.Commands.add(
  "mockAISuggestions" as const,
  (suggestions: any[] = []) => {
    (suggestions as any[]).forEach((suggestion: any, index: number) => {
      cy.intercept("POST", "/api/freestyle_sessions/*/ai_suggestions", {
        statusCode: 200,
        body: {
          id: index + 1,
          content: suggestion,
          timestamp: index * 5,
        },
      }).as(`suggestion${index}`);
    });
  },
);

Cypress.Commands.add("mockMediaRecorder", () => {
  mockMediaRecorder();
});

Cypress.Commands.overwrite(
  "intercept",
  (originalFn, method: string, url: string, response?: any) => {
    return originalFn(method, url, response);
  },
);

Cypress.Commands.add("stub", (method: string, url: string, response?: any) => {
  return cy.intercept(method, url, response);
});

// Export empty object to make this a module
export {};
