// Cypress custom command typings (module augmentation)
declare global {
  namespace Cypress {
    interface Chainable<Subject = any> {
      mockAISuggestions(suggestions?: Array<string>): Chainable<void>;
      mockAudioContext(mock: unknown): Chainable<void>;
      mockMediaRecorder(mock: unknown): Chainable<void>;
      mockSpeechRecognition(mock: unknown): Chainable<void>;
      login(email: string, password: string): Chainable<void>;
      register(
        username: string,
        email: string,
        password: string,
      ): Chainable<void>;
      logout(): Chainable<void>;
      mockBeats(beats?: Array<any>): Chainable<void>;
      findByText(text: string | RegExp): Chainable<JQuery<HTMLElement>>;
      findAllByText(text: string | RegExp): Chainable<JQuery<HTMLElement>>;
      findByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
      findAllByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
    }
  }
}
export {};
