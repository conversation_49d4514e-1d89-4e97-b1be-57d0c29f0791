/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

declare namespace Cypress {
  interface Chainable {
    // Custom commands
    register(
      username: string,
      email: string,
      password: string,
    ): Chainable<void>;
    login(email: string, password: string): Chainable<void>;
    mockBeats(beats?: any[]): Chainable<void>;
    mockAISuggestions(suggestions?: string[]): Chainable<void>;

    // Cypress commands
    // intercept(method: string, url: string, options?: any): Chainable<void>;

    // Testing Library commands
    findByText(text: string | RegExp): Chainable<JQuery<HTMLElement>>;
    findAllByText(text: string | RegExp): Chainable<JQuery<HTMLElement>>;
    findByTestId(id: string): Chainable<JQuery<HTMLElement>>;
    findAllByTestId(id: string): Chainable<JQuery<HTMLElement>>;
  }
}

// Import Testing Library commands
import "@testing-library/cypress/add-commands";
