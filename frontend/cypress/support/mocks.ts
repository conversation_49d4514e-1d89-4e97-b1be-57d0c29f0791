/// <reference lib="dom" />

// Use type aliases for browser SpeechRecognition types
// @ts-ignore
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const SpeechRecognition: typeof window.SpeechRecognition =
  window.SpeechRecognition || (window as any).webkitSpeechRecognition;
// @ts-ignore
const SpeechRecognitionEvent: typeof window.SpeechRecognitionEvent =
  window.SpeechRecognitionEvent || (window as any).webkitSpeechRecognitionEvent;

const mockMediaRecorder = () => {
  cy.window().then((win) => {
    class MockMediaRecorder {
      ondataavailable: ((event: BlobEvent) => void) | null = null;
      onstop: (() => void) | null = null;
      state: RecordingState = "inactive";

      constructor() {
        this.state = "inactive";
      }

      start() {
        this.state = "recording";
        if (this.ondataavailable) {
          const blob = new Blob(["test"], { type: "audio/webm" });
          const event = new Event("dataavailable") as BlobEvent;
          Object.defineProperty(event, "data", { value: blob });
          this.ondataavailable(event);
        }
      }

      stop() {
        this.state = "inactive";
        if (this.onstop) this.onstop();
      }

      pause() {
        this.state = "paused";
      }

      resume() {
        this.state = "recording";
      }

      static isTypeSupported(type: string): boolean {
        return true;
      }
    }

    (win as any).MediaRecorder = MockMediaRecorder;
  });
};

const cypressMockSpeechRecognition = () => {
  cy.window().then((win) => {
    class MockSpeechRecognition {
      onresult: ((event: typeof SpeechRecognitionEvent) => void) | null = null;
      onerror: ((event: ErrorEvent) => void) | null = null;
      continuous = false;
      interimResults = false;

      start() {
        if (this.onresult) {
          const event = new Event("result") as typeof SpeechRecognitionEvent;
          Object.defineProperty(event, "results", {
            value: [[{ transcript: "test transcript", confidence: 0.9 }]],
          });
          this.onresult(event);
        }
      }

      stop() {}
      abort() {}
    }

    (win as any).SpeechRecognition = MockSpeechRecognition as any;
    (win as any).webkitSpeechRecognition = MockSpeechRecognition as any;
  });
};

export { mockMediaRecorder, cypressMockSpeechRecognition };
