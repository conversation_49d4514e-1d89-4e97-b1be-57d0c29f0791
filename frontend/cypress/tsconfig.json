{"extends": "../tsconfig.json", "compilerOptions": {"types": ["cypress", "@testing-library/cypress"], "noEmit": true, "target": "es5", "lib": ["es5", "dom", "es2015"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types", "../node_modules/@types"], "paths": {"@/*": ["../src/*"]}}, "include": ["e2e/**/*.ts", "support/**/*.ts", "support/**/*.d.ts", "cypress.d.ts", "pages/**/*.ts"]}