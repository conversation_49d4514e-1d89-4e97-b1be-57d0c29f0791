/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

export class SettingsPage {
  visit() {
    cy.visit("/settings");
  }

  updateProfile(name: string, email: string) {
    cy.get('[data-testid="name-input"]').clear().type(name);
    cy.get('[data-testid="email-input"]').clear().type(email);
    cy.get('[data-testid="save-button"]').click();
  }

  verifyProfileUpdateSuccess() {
    cy.get('[data-testid="success-message"]').should("be.visible");
  }

  verifyProfileUpdateError() {
    cy.get('[data-testid="error-message"]').should("be.visible");
  }
}
