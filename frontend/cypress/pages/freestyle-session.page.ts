export class FreestyleSessionPage {
  visit() {
    cy.visit("/freestyle");
  }

  verifyGuestAccess() {
    cy.visit("/");
    cy.findByText("Try It Now").click();
    cy.url().should("include", "/freestyle");
    cy.findByText("Select a Beat").should("exist");
    cy.findAllByTestId("beat-card").should("have.length.at.least", 1);
  }

  selectBeat(type: "default" | "premium") {
    cy.findAllByTestId("beat-card").first().click();
    if (type === "premium") {
      cy.findByTestId("premium-beat").should("exist");
    }
  }

  startRecording() {
    cy.findByTestId("record-button").click();
    cy.findByText("Recording...").should("exist");
  }

  stopRecording() {
    cy.findByTestId("stop-button").click();
  }

  saveRecording(title: string) {
    cy.findByText("Save Recording").should("exist");
    cy.findByTestId("recording-title").type(title);
    cy.findByTestId("save-button").click();
  }

  verifyRecordingSaved() {
    cy.url().should("include", "/dashboard");
    cy.findByText("Recording saved successfully").should("exist");
  }

  verifyAISuggestions() {
    cy.findByTestId("suggestions-container").within(() => {
      cy.findAllByTestId("suggestion-item").should("have.length.at.least", 1);
    });
  }

  verifyRecordingLimit() {
    cy.findByText("Recording limit reached").should("exist");
  }

  verifyPremiumPrompt() {
    cy.findByText(/Upgrade to Premium/i).should("exist");
  }

  verifyErrorHandling() {
    cy.findByText(/failed to save/i).should("exist");
  }

  verifyPlaybackActive() {
    cy.findByTestId("playback-indicator").should("be.visible");
  }

  verifyPremiumFeatures() {
    cy.findByTestId("premium-features").should("exist");
  }

  verifyFreeLimitations() {
    cy.findByTestId("free-limitations").should("exist");
  }

  verifySessionCount(count: number) {
    cy.findByTestId("session-count").should("have.text", `${count}/3`);
  }

  verifySessionLimitReached() {
    cy.findByText("Session limit reached").should("exist");
  }
}
