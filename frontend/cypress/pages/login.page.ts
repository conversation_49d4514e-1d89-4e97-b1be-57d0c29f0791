/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

export class LoginPage {
  visit() {
    cy.visit("/login");
  }

  login(email: string, password: string) {
    cy.get('[data-testid="email-input"]').type(email);
    cy.get('[data-testid="password-input"]').type(password);
    cy.get('[data-testid="login-button"]').click();
  }

  verifyLoginSuccess() {
    cy.url().should("include", "/dashboard");
  }

  verifyLoginError() {
    cy.get('[data-testid="login-error"]').should("be.visible");
  }

  loginAsPremiumUser() {
    this.login("<EMAIL>", "password123");
  }

  loginAsFreeUser() {
    this.login("<EMAIL>", "password123");
  }
}
