/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

export class PremiumPage {
  visit() {
    cy.visit("/premium");
  }

  verifyPremiumFeatures() {
    cy.get('[data-testid="premium-features"]').should("be.visible");
  }

  verifyUpgradeButton() {
    cy.get('[data-testid="upgrade-button"]').should("be.visible");
  }

  verifyPremiumPageLoaded() {
    cy.findByTestId("premium-header").should("exist");
  }

  selectPlan(plan: "monthly" | "yearly") {
    cy.findByTestId(`${plan}-plan`).click();
  }

  verifyPlanSelected(plan: "monthly" | "yearly") {
    cy.findByTestId(`${plan}-plan`).should("have.class", "selected");
  }

  proceedToCheckout() {
    cy.findByTestId("checkout-button").click();
  }

  verifyCheckoutPage() {
    cy.url().should("include", "/checkout");
  }
}
