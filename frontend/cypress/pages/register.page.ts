/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

export class RegisterPage {
  visit() {
    cy.visit("/register");
  }

  register(email: string, password: string) {
    cy.get('[data-testid="email-input"]').type(email);
    cy.get('[data-testid="password-input"]').type(password);
    cy.get('[data-testid="register-button"]').click();
  }

  verifyRegistrationSuccess() {
    cy.url().should("include", "/dashboard");
  }

  verifyRegistrationError() {
    cy.get('[data-testid="error-message"]').should("be.visible");
  }
}
