/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

export class DashboardPage {
  visit() {
    cy.visit("/dashboard");
  }

  verifyDashboardLoaded() {
    cy.get('[data-testid="dashboard-title"]').should("be.visible");
  }

  verifyUserGreeting(name: string) {
    cy.get('[data-testid="user-greeting"]').should("contain", name);
  }

  verifyRecentActivity() {
    cy.get('[data-testid="recent-activity"]').should("be.visible");
  }

  navigateToFreestyle() {
    cy.findByTestId("freestyle-link").click();
  }

  verifySessionCount(count: number) {
    cy.findByTestId("session-count").should("contain", count);
  }

  verifyPremiumStatus(isPremium: boolean) {
    if (isPremium) {
      cy.findByTestId("premium-badge").should("exist");
    } else {
      cy.findByTestId("premium-badge").should("not.exist");
    }
  }

  verifyRecentSessions() {
    cy.findByTestId("recent-sessions").should("exist");
  }
}
