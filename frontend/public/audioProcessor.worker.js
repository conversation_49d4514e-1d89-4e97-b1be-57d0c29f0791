/**
 * Audio Processor Web Worker
 *
 * This worker handles CPU-intensive audio processing tasks off the main thread:
 * - Decoding audio files
 * - Mixing audio buffers
 * - Converting to WAV format
 * - Applying audio effects
 *
 * Using a Web Worker prevents UI jank during heavy audio processing.
 */

// Create an AudioContext for processing
let audioContext = null;

// Helper function to get or create AudioContext
function getAudioContext() {
  if (!audioContext) {
    audioContext = new AudioContext({
      latencyHint: 'interactive',
      sampleRate: 48000
    });
  }
  return audioContext;
}

// Helper function to write a string to a DataView
function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

// Audio processor worker implementation
const audioProcessorWorker = {
  // Decode audio data
  async decodeAudio(arrayBuffer) {
    try {
      const context = getAudioContext();
      const audioBuffer = await context.decodeAudioData(arrayBuffer.slice(0));

      // Log buffer details
      console.log("[Worker] Decoded audio buffer:", {
        duration: audioBuffer.duration,
        numberOfChannels: audioBuffer.numberOfChannels,
        sampleRate: audioBuffer.sampleRate,
        length: audioBuffer.length
      });

      return audioBuffer;
    } catch (e) {
      console.error("[Worker] Error decoding audio:", e);
      throw e;
    }
  },

  // Mix two audio buffers with gain control
  async mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain = 1.0, beatGain = 0.8) {
    try {
      console.log("[Worker] Mixing audio buffers:", {
        recordingDuration: recordingBuffer.duration,
        beatDuration: beatBuffer.duration,
        recordingChannels: recordingBuffer.numberOfChannels,
        beatChannels: beatBuffer.numberOfChannels
      });

      const context = getAudioContext();

      // Create a new buffer with the length of the longer buffer
      const mixedBuffer = context.createBuffer(
        Math.max(recordingBuffer.numberOfChannels, beatBuffer.numberOfChannels),
        Math.max(recordingBuffer.length, beatBuffer.length),
        recordingBuffer.sampleRate
      );

      // Copy recording data with gain
      for (let channel = 0; channel < recordingBuffer.numberOfChannels; channel++) {
        const recordingData = recordingBuffer.getChannelData(channel);
        const mixedData = mixedBuffer.getChannelData(channel);

        for (let i = 0; i < recordingData.length; i++) {
          mixedData[i] = recordingData[i] * recordingGain;
        }
      }

      // Mix in beat data with gain
      for (let channel = 0; channel < beatBuffer.numberOfChannels; channel++) {
        const beatData = beatBuffer.getChannelData(channel);
        const mixedData = mixedBuffer.getChannelData(channel % mixedBuffer.numberOfChannels);

        for (let i = 0; i < beatData.length && i < mixedData.length; i++) {
          mixedData[i] += beatData[i] * beatGain;
        }
      }

      // Normalize to prevent clipping
      let maxPeak = 0;
      for (let channel = 0; channel < mixedBuffer.numberOfChannels; channel++) {
        const data = mixedBuffer.getChannelData(channel);
        for (let i = 0; i < data.length; i++) {
          maxPeak = Math.max(maxPeak, Math.abs(data[i]));
        }
      }

      if (maxPeak > 0.9) {
        const gainFactor = 0.9 / maxPeak;
        console.log("[Worker] Normalizing with gain factor:", gainFactor);

        for (let channel = 0; channel < mixedBuffer.numberOfChannels; channel++) {
          const data = mixedBuffer.getChannelData(channel);
          for (let i = 0; i < data.length; i++) {
            data[i] *= gainFactor;
          }
        }
      }

      console.log("[Worker] Mixed buffer created:", {
        duration: mixedBuffer.duration,
        channels: mixedBuffer.numberOfChannels,
        sampleRate: mixedBuffer.sampleRate
      });

      return mixedBuffer;
    } catch (e) {
      console.error("[Worker] Error mixing audio buffers:", e);
      throw e;
    }
  },

  // Convert AudioBuffer to WAV blob
  async audioBufferToWav(buffer) {
    try {
      console.log("[Worker] Converting AudioBuffer to WAV");

      const numChannels = buffer.numberOfChannels;
      const sampleRate = buffer.sampleRate;
      const length = buffer.length * numChannels * 2; // 16-bit samples

      // Create WAV header
      const view = new DataView(new ArrayBuffer(44 + length));

      // "RIFF" chunk descriptor
      writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + length, true);
      writeString(view, 8, 'WAVE');

      // "fmt " sub-chunk
      writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true); // subchunk size
      view.setUint16(20, 1, true); // PCM format
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * 2, true); // byte rate
      view.setUint16(32, numChannels * 2, true); // block align
      view.setUint16(34, 16, true); // bits per sample

      // "data" sub-chunk
      writeString(view, 36, 'data');
      view.setUint32(40, length, true);

      // Write audio data
      let offset = 44;
      for (let i = 0; i < buffer.length; i++) {
        for (let channel = 0; channel < numChannels; channel++) {
          const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
          view.setInt16(offset, sample * 0x7FFF, true);
          offset += 2;
        }
      }

      return new Blob([view], { type: 'audio/wav' });
    } catch (e) {
      console.error("[Worker] Error converting to WAV:", e);
      throw e;
    }
  },

  // Process recording for preview
  async processRecordingForPreview(recordingArrayBuffer, beatArrayBuffer, recordingGain = 1.0, beatGain = 0.8) {
    try {
      console.log("[Worker] Processing recording for preview with gains:", {
        recordingGain,
        beatGain,
        recordingBufferSize: recordingArrayBuffer.byteLength,
        beatBufferSize: beatArrayBuffer.byteLength
      });

      // Decode recording
      const recordingBuffer = await this.decodeAudio(recordingArrayBuffer);

      // Decode beat
      const beatBuffer = await this.decodeAudio(beatArrayBuffer);

      // Mix audio with the specified gain values
      const mixedBuffer = await this.mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain);

      // Convert to WAV
      const mixedBlob = await this.audioBufferToWav(mixedBuffer);

      return {
        mixedBuffer,
        mixedBlob,
        duration: mixedBuffer.duration
      };
    } catch (e) {
      console.error("[Worker] Error processing recording for preview:", e);
      throw e;
    }
  }
};

// Handle messages from main thread
self.onmessage = async function(e) {
  const { id, method, args } = e.data;

  try {
    const result = await audioProcessorWorker[method](...args);
    self.postMessage({ id, result });
  } catch (error) {
    self.postMessage({ id, error: error.message });
  }
};

// Notify that the worker is ready
console.log("[Worker] Audio processor worker initialized");