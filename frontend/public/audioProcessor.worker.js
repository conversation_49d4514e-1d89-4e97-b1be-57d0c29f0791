/**
 * Audio Processor Web Worker
 * 
 * This worker handles CPU-intensive audio processing tasks off the main thread:
 * - Decoding audio files
 * - Mixing audio buffers
 * - Converting to WAV format
 * - Applying audio effects
 * 
 * Using a Web Worker prevents UI jank during heavy audio processing.
 */

// Import audio processing utilities
import * as Comlink from 'comlink';

// Define the worker interface
>;
}

// Create an AudioContext for processing
let audioContext = null;

// Helper function to get or create AudioContext
function getAudioContext() {
  if (!audioContext) {
    audioContext = new AudioContext({
      latencyHint: 'interactive',
      sampleRate
    });
  }
  return audioContext;
}

// Helper function to write a string to a DataView
function writeString(view, offset, string) {
  for (let i = 0; i  {
    try {
      const context = getAudioContext();
      const audioBuffer = await context.decodeAudioData(arrayBuffer.slice(0));
      
      // Log buffer details
      console.log("[Worker] Decoded audio buffer:", {
        duration.duration,
        numberOfChannels.numberOfChannels,
        sampleRate.sampleRate,
        length.length
      });
      
      return audioBuffer;
    } catch (e) {
      console.error("[Worker] Error decoding audio:", e);
      throw e;
    }
  },
  
  // Mix two audio buffers with gain control
  async mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain) {
    try {
      console.log("[Worker] Mixing audio buffers:", {
        recordingDuration.duration,
        beatDuration.duration,
        recordingChannels.numberOfChannels,
        beatChannels.numberOfChannels
      });
      
      const context = getAudioContext();
      
      // Create a new buffer with the length of the longer buffer
      const mixedBuffer = context.createBuffer(
        Math.max(recordingBuffer.numberOfChannels, beatBuffer.numberOfChannels),
        Math.max(recordingBuffer.length, beatBuffer.length),
        recordingBuffer.sampleRate
      );
      
      // Copy recording data with gain
      for (let channel = 0; channel  0.9) {
        const gainFactor = 0.9 / maxPeak;
        console.log("[Worker] Normalizing with gain factor:", gainFactor);
        
        for (let channel = 0; channel  {
    try {
      const context = getAudioContext();
      const outputBuffer = context.createBuffer(
        buffer.numberOfChannels,
        buffer.length,
        buffer.sampleRate
      );
      
      // Process each channel
      for (let channel = 0; channel  threshold) {
            // Compressed part
            const gain = threshold + (inputAbs - threshold) / ratio;
            outputData[i] = input > 0 ? gain : -gain;
          } else {
            // Uncompressed part
            outputData[i] = input;
          }
        }
      }
      
      return outputBuffer;
    } catch (e) {
      console.error("[Worker] Error applying compression:", e);
      throw e;
    }
  },
  
  // Apply simple 3-band EQ
  async applyEQ(buffer, lowGain, midGain, highGain) {
    // Simple implementation - in a real app, this would use proper filters
    // This is a placeholder that just adjusts the gain
    try {
      const context = getAudioContext();
      const outputBuffer = context.createBuffer(
        buffer.numberOfChannels,
        buffer.length,
        buffer.sampleRate
      );
      
      // Just apply overall gain for now (simplified)
      const overallGain = (lowGain + midGain + highGain) / 3;
      
      // Process each channel
      for (let channel = 0; channel  {
    try {
      console.log("[Worker] Converting AudioBuffer to WAV");
      
      const numChannels = buffer.numberOfChannels;
      const sampleRate = buffer.sampleRate;
      const length = buffer.length * numChannels * 2; // 16-bit samples
      
      // Create WAV header
      const view = new DataView(new ArrayBuffer(44 + length));
      
      // "RIFF" chunk descriptor
      writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + length, true);
      writeString(view, 8, 'WAVE');
      
      // "fmt " sub-chunk
      writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true); // subchunk size
      view.setUint16(20, 1, true); // PCM format
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * 2, true); // byte rate
      view.setUint16(32, numChannels * 2, true); // block align
      view.setUint16(34, 16, true); // bits per sample
      
      // "data" sub-chunk
      writeString(view, 36, 'data');
      view.setUint32(40, length, true);
      
      // Write audio data
      let offset = 44;
      for (let i = 0; i  {
    try {
      console.log("[Worker] Processing recording for preview with gains:", {
        recordingGain,
        beatGain,
        recordingBufferSize.byteLength,
        beatBufferSize.byteLength
      });
      
      // Decode recording
      const recordingBuffer = await this.decodeAudio(recordingArrayBuffer);
      
      // Decode beat
      const beatBuffer = await this.decodeAudio(beatArrayBuffer);
      
      // Mix audio with the specified gain values
      const mixedBuffer = await this.mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain);
      
      // Apply compression for better sound
      const compressedBuffer = await this.applyCompression(mixedBuffer, 0.5, 4);
      
      // Convert to WAV
      const mixedBlob = await this.audioBufferToWav(compressedBuffer);
      
      return {
        mixedBuffer,
        mixedBlob,
        duration.duration
      };
    } catch (e) {
      console.error("[Worker] Error processing recording for preview:", e);
      throw e;
    }
  }
};

// Export the worker with Comlink
Comlink.expose(audioProcessorWorker);

// Notify that the worker is ready
console.log("[Worker] Audio processor worker initialized");
