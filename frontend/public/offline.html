<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Offline - Freestyle AI</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
          Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        background: #1a1a1a;
        color: #ffffff;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
      .container {
        padding: 2rem;
        max-width: 600px;
        margin: 0 auto;
      }
      h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        background: linear-gradient(45deg, #3b82f6, #60a5fa);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      p {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #a3a3a3;
        margin-bottom: 2rem;
      }
      .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }
      .feature {
        background: rgba(255, 255, 255, 0.05);
        padding: 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
      .feature h3 {
        color: #60a5fa;
        margin-top: 0;
      }
      .retry-button {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      .retry-button:hover {
        background: #2563eb;
      }
      .status {
        margin-top: 1rem;
        font-size: 0.9rem;
        color: #6b7280;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>You're Offline</h1>
      <p>
        Don't worry! Freestyle AI has cached some features for offline use.
        Here's what you can do while offline:
      </p>

      <div class="features">
        <div class="feature">
          <h3>Cached Beats</h3>
          <p>Previously loaded beats are available for playback</p>
        </div>
        <div class="feature">
          <h3>Local Recording</h3>
          <p>You can still record your freestyle sessions</p>
        </div>
        <div class="feature">
          <h3>Auto Sync</h3>
          <p>Your recordings will sync when you're back online</p>
        </div>
      </div>

      <button class="retry-button" onclick="window.location.reload()">
        Try Again
      </button>

      <div class="status">
        Checking connection... <span id="status-text">Offline</span>
      </div>
    </div>

    <script>
      // Update status text when online/offline status changes
      function updateOnlineStatus() {
        const statusText = document.getElementById("status-text");
        if (navigator.onLine) {
          statusText.textContent = "Back Online!";
          setTimeout(() => window.location.reload(), 1000);
        } else {
          statusText.textContent = "Offline";
        }
      }

      window.addEventListener("online", updateOnlineStatus);
      window.addEventListener("offline", updateOnlineStatus);
      updateOnlineStatus();
    </script>
  </body>
</html>
