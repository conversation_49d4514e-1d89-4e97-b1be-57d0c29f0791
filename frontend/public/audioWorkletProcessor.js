class DAWRecorderProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this._isRecording = false;
    this._buffer = [];
    this._timelineStartSample = 0;
    this._sampleRate = sampleRate;
    this._rollingBufferSize = 2048;
    this._rollingBuffer = new Float32Array(this._rollingBufferSize);
    this._rollingOffset = 0;
    this.port.onmessage = (event) => {
      if (event.data.type === "start") {
        this._isRecording = true;
        this._timelineStartSample = event.data.sampleOffset || 0;
        this._buffer = [];
      } else if (event.data.type === "stop") {
        this._isRecording = false;
      } else if (event.data.type === "flush") {
        this.port.postMessage({
          type: "audio",
          buffer: this._buffer,
          startSample: this._timelineStartSample,
          sampleRate: this._sampleRate,
        });
        this._buffer = [];
      }
    };
  }

  process(inputs) {
    const input = inputs[0][0];
    if (this._isRecording && input) {
      this._buffer.push(new Float32Array(input));
      // Rolling buffer for live waveform
      for (let i = 0; i < input.length; i++) {
        this._rollingBuffer[this._rollingOffset] = input[i];
        this._rollingOffset =
          (this._rollingOffset + 1) % this._rollingBufferSize;
      }
      if (this._buffer.length % 2 === 0) {
        this.port.postMessage({
          type: "waveform",
          buffer: this._rollingBuffer.slice(0),
        });
      }
    }
    return true;
  }
}
registerProcessor("daw-recorder-processor", DAWRecorderProcessor);
