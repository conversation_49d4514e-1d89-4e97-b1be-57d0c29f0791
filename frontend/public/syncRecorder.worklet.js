/**
 * SyncRecorder Audio Worklet
 *
 * This worklet provides precise audio recording with synchronization capabilities, sampleRate) are available at runtime
// but not recognized by TypeScript

// Define the processor class
class SyncRecorderProcessor extends AudioWorkletProcessor {
  // Static properties
  static get parameterDescriptors() {
    return [
      {
        name,
        defaultValue,
        minValue,
        maxValue,
        automationRate,
      {
        name,
        defaultValue,
        automationRate= [];
  _recordingStartTime= 0;
  _syncTimestamp= 0;
  _isRecording= false;
  _rollingBuffer= 0;
  _rollingBufferSize= 2048;
  _levelUpdateCounter= 0;
  _levelUpdateInterval= 10; // Update level every 10 blocks
  _currentLevel= 0;

  constructor() {
    super();

    // Initialize rolling buffer for level monitoring
    this._rollingBuffer = new Float32Array(this._rollingBufferSize);

    // Set up message port
    this.port.onmessage = this.handleMessage.bind(this);

    // Send ready message
    this.port.postMessage({
      type);
  }

  // Handle messages from the main thread
  handleMessage(event) {
    const { data } = event;

    switch (data.type) {
      case 'start'= currentTime;
        this._syncTimestamp = data.syncTimestamp || currentTime;
        this._buffer = [];
        this._isRecording = true;
        this.port.postMessage({
          type,
          timestamp,
          syncTimestamp);
        break;

      case 'stop'= false;
        this.port.postMessage({
          type,
          buffer,
          recordingStartTime,
          syncTimestamp,
          duration);
        break;

      case 'clear'= [];
        break;
    }
  }

  // Calculate audio level from a buffer
  calculateLevel(buffer) {
    let sum = 0;
    for (let i = 0; i ) {
    // Get parameters
    const isRecordingParam = parameters.isRecording[0];
    const syncTimestampParam = parameters.syncTimestamp[0];

    // Update recording state if parameter changed
    if (isRecordingParam === 1 && !this._isRecording) {
      this._isRecording = true;
      this._recordingStartTime = currentTime;
      this._syncTimestamp = syncTimestampParam || currentTime;
      this._buffer = [];

      this.port.postMessage({
        type: 'started',
        timestamp: this._recordingStartTime,
        syncTimestamp: this._syncTimestamp
      });
    } else if (isRecordingParam === 0 && this._isRecording) {
      this._isRecording = false;

      this.port.postMessage({
        type: 'stopped',
        buffer: this._buffer,
        recordingStartTime: this._recordingStartTime,
        syncTimestamp: this._syncTimestamp,
        duration: currentTime - this._recordingStartTime
      });
    }

    // Process input
    const input = inputs[0][0];
    if (input) {
      // Record audio if recording is active
      if (this._isRecording) {
        this._buffer.push(new Float32Array(input));
      }

      // Update rolling buffer for level monitoring
      for (let i = 0; i < input.length; i++) {
        this._rollingBuffer[this._rollingOffset] = Math.abs(input[i]);
        this._rollingOffset = (this._rollingOffset + 1) % this._rollingBufferSize;
      }

      // Calculate and send level updates periodically
      this._levelUpdateCounter++;
      if (this._levelUpdateCounter >= this._levelUpdateInterval) {
        this._levelUpdateCounter = 0;

        // Calculate level
        const level = this.calculateLevel(this._rollingBuffer);

        // Only send if level changed significantly
        if (Math.abs(level - this._currentLevel) > 0.01) {
          this._currentLevel = level;
          this.port.postMessage({
            type: 'level',
            value: level
          });
        }
      }

      // Pass through audio to output
      if (outputs[0][0]) {
        outputs[0][0].set(input);
      }
    }

    return true;
  }
}

// Register the processor
registerProcessor('sync-recorder-processor', SyncRecorderProcessor);
