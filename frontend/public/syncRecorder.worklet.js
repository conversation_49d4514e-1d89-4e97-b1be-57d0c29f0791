/**
 * SyncRecorder Audio Worklet
 *
 * This worklet provides precise audio recording with synchronization capabilities:
 * - Records audio with sample-accurate timing
 * - Maintains synchronization with beat playback
 * - Stores timing information for accurate mixing
 * - Provides real-time audio level monitoring
 */

// Define the processor class
// @ts-expect-error - AudioWorkletGlobalScope variables (currentTime, sampleRate) are available at runtime
class SyncRecorderProcessor extends AudioWorkletProcessor {
  // Static properties
  static get parameterDescriptors() {
    return [
      {
        name: 'isRecording',
        defaultValue,
        minValue,
        maxValue,
        automationRate: 'k-rate'
      },
      {
        name: 'syncTimestamp',
        defaultValue,
        automationRate: 'k-rate'
      }
    ];
  }

  // Private properties
  
  }

  // Handle messages from the main thread
  handleMessage(event) {
    const { data } = event;

    switch (data.type) {
      case 'start':
        // currentTime is a global variable in AudioWorkletGlobalScope
        this._recordingStartTime = currentTime;
        this._syncTimestamp = data.syncTimestamp || currentTime;
        this._buffer = [];
        this._isRecording = true;
        this.port.postMessage({
          type: 'started',
          timestamp._recordingStartTime,
          syncTimestamp._syncTimestamp
        });
        break;

      case 'stop':
        this._isRecording = false;
        this.port.postMessage({
          type: 'stopped',
          buffer._buffer,
          recordingStartTime._recordingStartTime,
          syncTimestamp._syncTimestamp,
          duration - this._recordingStartTime
        });
        break;

      case 'clear':
        this._buffer = [];
        break;
    }
  }

  // Calculate audio level from a buffer
  calculateLevel(buffer) {
    let sum = 0;
    for (let i = 0; i ) {
    // Get parameters
    const isRecordingParam = parameters.isRecording[0];
    const syncTimestampParam = parameters.syncTimestamp[0];

    // Update recording state if parameter changed
    if (isRecordingParam === 1 && !this._isRecording) {
      this._isRecording = true;
      this._recordingStartTime = currentTime;
      this._syncTimestamp = syncTimestampParam || currentTime;
      this._buffer = [];

      this.port.postMessage({
        type: 'started',
        timestamp._recordingStartTime,
        syncTimestamp._syncTimestamp
      });
    } else if (isRecordingParam === 0 && this._isRecording) {
      this._isRecording = false;

      this.port.postMessage({
        type: 'stopped',
        buffer._buffer,
        recordingStartTime._recordingStartTime,
        syncTimestamp._syncTimestamp,
        duration - this._recordingStartTime
      });
    }

    // Process input
    const input = inputs[0][0];
    if (input) {
      // Record audio if recording is active
      if (this._isRecording) {
        this._buffer.push(new Float32Array(input));
      }

      // Update rolling buffer for level monitoring
      for (let i = 0; i = this._levelUpdateInterval) {
        this._levelUpdateCounter = 0;

        // Calculate level
        const level = this.calculateLevel(this._rollingBuffer);

        // Only send if level changed significantly
        if (Math.abs(level - this._currentLevel) > 0.01) {
          this._currentLevel = level;
          this.port.postMessage({
            type: 'level',
            value
          });
        }
      }

      // Pass through audio to output
      if (outputs[0][0]) {
        outputs[0][0].set(input);
      }
    }

    return true;
  }
}

// Register the processor
registerProcessor('sync-recorder-processor', SyncRecorderProcessor);
