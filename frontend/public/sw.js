const CACHE_NAME = "freestyle-cache-v1";
const STATIC_CACHE = "static-v1";
const DYNAMIC_CACHE = "dynamic-v1";
const API_CACHE = "api-v1";
const RECORDING_CACHE = "recording-v1";

const STATIC_ASSETS = [
  "/",
  "/beats",
  "/auth/login",
  "/auth/register",
  "/dashboard",
  "/styles/globals.css",
  "/offline.html",
  "/images/logo.png",
  "/images/badge.png",
  "/manifest.json",
];

const API_ROUTES = [
  "/api/beats",
  "/api/freestyle_sessions",
  "/api/ai_suggestions",
];

// Utility function to handle network timeouts
const timeoutPromise = (promise, timeout = 3000) => {
  return Promise.race([
    promise,
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Request timeout")), timeout),
    ),
  ]);
};

// Utility function to notify clients about metrics
const notifyMetric = async (type, data) => {
  const clients = await self.clients.matchAll();
  clients.forEach((client) => {
    client.postMessage({
      type,
      ...data,
    });
  });
};

// Install service worker and cache static resources
self.addEventListener("install", (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => cache.addAll(STATIC_ASSETS)),
      caches.open(API_CACHE),
      caches.open(DYNAMIC_CACHE),
      caches.open(RECORDING_CACHE),
    ]).then(() => self.skipWaiting()),
  );
});

// Cache strategies based on request type
const strategies = {
  // Network first with timeout, falling back to cache for API requests
  api: async (request) => {
    const cache = await caches.open(API_CACHE);
    try {
      const response = await timeoutPromise(fetch(request));
      if (response.ok) {
        cache.put(request, response.clone());
        await notifyMetric("CACHE_METRIC", { cacheType: "api", hit: false });
        return response;
      }
      throw new Error("Network response was not ok");
    } catch (error) {
      console.log("API fetch failed, falling back to cache:", error);
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        await notifyMetric("CACHE_METRIC", { cacheType: "api", hit: true });
        return cachedResponse;
      }
      throw error;
    }
  },

  // Cache first, falling back to network for static assets
  static: async (request) => {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      await notifyMetric("CACHE_METRIC", { cacheType: "static", hit: true });
      // Update cache in background
      fetch(request)
        .then((response) => {
          if (response.ok) {
            cache.put(request, response);
          }
        })
        .catch(() => {
          /* Ignore background fetch errors */
        });
      return cachedResponse;
    }
    try {
      const response = await fetch(request);
      if (response.ok) {
        cache.put(request, response.clone());
        await notifyMetric("CACHE_METRIC", { cacheType: "static", hit: false });
      }
      return response;
    } catch (error) {
      // If offline and no cache, return offline page for navigation
      if (request.mode === "navigate") {
        return cache.match("/offline.html");
      }
      throw error;
    }
  },

  // Network first for dynamic content
  dynamic: async (request) => {
    const cache = await caches.open(DYNAMIC_CACHE);
    try {
      const response = await timeoutPromise(fetch(request));
      if (response.ok) {
        cache.put(request, response.clone());
        await notifyMetric("CACHE_METRIC", {
          cacheType: "dynamic",
          hit: false,
        });
        return response;
      }
      throw new Error("Network response was not ok");
    } catch (error) {
      console.log("Dynamic fetch failed, falling back to cache:", error);
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        await notifyMetric("CACHE_METRIC", { cacheType: "dynamic", hit: true });
        return cachedResponse;
      }
      if (request.mode === "navigate") {
        return caches.match("/offline.html");
      }
      throw error;
    }
  },

  // Special handling for recording uploads
  recording: async (request) => {
    const cache = await caches.open(RECORDING_CACHE);
    try {
      const response = await fetch(request);
      await notifyMetric("SYNC_METRIC", { success: true });
      return response;
    } catch (error) {
      // If offline, store recording for later sync
      if (request.method === "POST") {
        await cache.put(request.url, request.clone());
        // Register for background sync
        await self.registration.sync.register("sync-recordings");
        await notifyMetric("SYNC_METRIC", { success: false });
        return new Response(
          JSON.stringify({
            status: "queued",
            message: "Recording will be uploaded when online",
          }),
          {
            headers: { "Content-Type": "application/json" },
          },
        );
      }
      throw error;
    }
  },
};

// Handle fetch events with appropriate strategies
self.addEventListener("fetch", (event) => {
  const url = new URL(event.request.url);

  // Don't cache chrome-extension requests
  if (url.protocol === "chrome-extension:") {
    return;
  }

  // Choose appropriate strategy based on request type
  let strategy;
  if (
    url.pathname.includes("/api/freestyle_sessions") &&
    event.request.method === "POST" &&
    url.pathname.includes("/recording")
  ) {
    strategy = strategies.recording;
  } else if (API_ROUTES.some((route) => url.pathname.startsWith(route))) {
    strategy = strategies.api;
  } else if (STATIC_ASSETS.includes(url.pathname)) {
    strategy = strategies.static;
  } else {
    strategy = strategies.dynamic;
  }

  event.respondWith(
    strategy(event.request).catch((error) => {
      console.error("Fetch error:", error);
      if (event.request.mode === "navigate") {
        return caches.match("/offline.html");
      }
      throw error;
    }),
  );
});

// Background sync for offline recordings
self.addEventListener("sync", (event) => {
  if (event.tag === "sync-recordings") {
    event.waitUntil(syncRecordings());
  }
});

// Clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              ![
                STATIC_CACHE,
                DYNAMIC_CACHE,
                API_CACHE,
                RECORDING_CACHE,
              ].includes(cacheName)
            ) {
              return caches.delete(cacheName);
            }
          }),
        );
      }),
      // Claim clients to ensure the service worker is in control
      self.clients.claim(),
    ]),
  );
});

// Handle messages from the client
self.addEventListener("message", (event) => {
  if (event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  }
});

// Sync recordings when back online
async function syncRecordings() {
  const cache = await caches.open(RECORDING_CACHE);
  const requests = await cache.keys();

  return Promise.all(
    requests.map(async (request) => {
      try {
        const recordingRequest = await cache.match(request);
        const response = await fetch(request, {
          method: "POST",
          body: await recordingRequest.blob(),
          headers: recordingRequest.headers,
        });

        if (response.ok) {
          await cache.delete(request);
          // Notify clients about successful sync
          const clients = await self.clients.matchAll();
          clients.forEach((client) => {
            client.postMessage({
              type: "RECORDING_SYNCED",
              url: request.url,
            });
          });
        }
        return response;
      } catch (error) {
        console.error("Recording sync failed:", error);
      }
    }),
  );
}

// Handle push notifications
self.addEventListener("push", (event) => {
  const data = event.data?.json() ?? {
    title: "New Update",
    body: "Check out what's new in Freestyle AI",
    icon: "/images/logo.png",
  };

  const options = {
    body: data.body,
    icon: data.icon || "/images/logo.png",
    badge: "/images/badge.png",
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
      url: data.url || "/",
    },
    actions: [
      {
        action: "view",
        title: "View",
      },
      {
        action: "close",
        title: "Close",
      },
    ],
  };

  event.waitUntil(self.registration.showNotification(data.title, options));
});

// Handle notification clicks
self.addEventListener("notificationclick", (event) => {
  event.notification.close();

  if (event.action === "view") {
    const urlToOpen = event.notification.data.url || "/";
    event.waitUntil(
      clients
        .matchAll({
          type: "window",
          includeUncontrolled: true,
        })
        .then((windowClients) => {
          // Check if there is already a window/tab open with the target URL
          for (const client of windowClients) {
            if (client.url === urlToOpen) {
              return client.focus();
            }
          }
          // If no window/tab is open, open a new one
          return clients.openWindow(urlToOpen);
        }),
    );
  }
});
