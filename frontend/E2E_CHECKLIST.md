# E2E Debug & Update Checklist

## 1. Verify Server Health

- [ ] Frontend (`/freestyle`) returns 200 and loads in browser.
- [ ] Backend (`/api/beats`) returns 200 and valid JSON.

## 2. Check for Redirects or Auth

- [ ] Is Cypress being redirected (e.g., to `/auth/join`)?
  - If so, update the test to log in or mock authentication.

## 3. Check for Loading or Error States

- [ ] Is the page stuck on a loading spinner or error message?
  - If so, check the DOM logs in Cypress output.

## 4. Check for Data/Component Mismatches

- [ ] Are the expected elements (e.g., `[data-testid="beat-player"]`) present in the DOM?
  - If not, update selectors in the test to match the new UI.

## 5. Review Cypress Fallback Logs

- [ ] Review logs for:
  - Current URL
  - Full body HTML
  - All visible text
  - Network requests and responses
  - Console errors/warnings

## 6. Update Test Flows

- [ ] Update the E2E test to match the new user journey and selectors.
- [ ] Remove or rewrite steps for features/components that have changed.

## 7. Iterate

- [ ] As you develop, keep the test up to date with new flows and selectors.
- [ ] Use fallback logs to quickly spot what needs to change.
