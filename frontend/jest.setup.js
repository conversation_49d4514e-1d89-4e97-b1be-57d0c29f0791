require("@testing-library/jest-dom");
const { configure } = require("@testing-library/react");
require("whatwg-fetch");

// Import canvas mock for jest-canvas-mock
require('jest-canvas-mock');

// Remove jest-axe for now - can be added later if needed

// Configure testing library
configure({
  testIdAttribute: "data-testid",
});

// Mock IntersectionObserver
class IntersectionObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
}

Object.defineProperty(window, "IntersectionObserver", {
  writable: true,
  configurable: true,
  value: IntersectionObserver,
});

// Mock ResizeObserver
class ResizeObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
}

Object.defineProperty(window, "ResizeObserver", {
  writable: true,
  configurable: true,
  value: ResizeObserver,
});

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock MediaRecorder
class MediaRecorderMock {
  constructor() {
    this.start = jest.fn();
    this.stop = jest.fn();
    this.ondataavailable = null;
    this.onstop = null;
  }
  start() {
    if (this.ondataavailable) {
      this.ondataavailable({
        data: new Blob(["test"], { type: "audio/webm" }),
        size: 4,
      });
    }
  }
  stop() {
    if (this.onstop) {
      this.onstop();
    }
  }
}
Object.defineProperty(window, "MediaRecorder", {
  writable: true,
  configurable: true,
  value: MediaRecorderMock,
});

// Mock SpeechRecognition
class SpeechRecognitionMock {
  constructor() {
    this.continuous = false;
    this.interimResults = false;
    this.onresult = null;
    this.onerror = null;
  }
  start() {}
  stop() {}
}
window.SpeechRecognition = SpeechRecognitionMock;
window.webkitSpeechRecognition = SpeechRecognitionMock;

// Set finite duration for HTMLMediaElement
Object.defineProperty(window.HTMLMediaElement.prototype, "duration", {
  writable: true,
  configurable: true,
  value: 180, // 3 minutes
});

// Mock play and pause for HTMLMediaElement (audio/video)
Object.defineProperty(window.HTMLMediaElement.prototype, "play", {
  configurable: true,
  value: jest.fn(),
});
Object.defineProperty(window.HTMLMediaElement.prototype, "pause", {
  configurable: true,
  value: jest.fn(),
});

// Mock the Worker global for tests
class WorkerMock {
  constructor(stringUrl) {
    this.url = stringUrl;
    this.onmessage = null;
    this.onerror = null;
  }
  postMessage(_msg) {}
  terminate() {}
  addEventListener() {}
  removeEventListener() {}
  dispatchEvent() {
    return false;
  }
}
global.Worker = WorkerMock;

// Mock monitoring services to silence warnings and prevent side effects
// Skip newrelic mock as it's not installed

// Monitoring services are not installed in this project
