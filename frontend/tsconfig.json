{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "types": [], "baseUrl": ".", "typeRoots": ["./node_modules/@types", "./src/types"], "allowSyntheticDefaultImports": true}, "include": ["src", ".next/types/**/*.ts"], "exclude": ["node_modules"]}