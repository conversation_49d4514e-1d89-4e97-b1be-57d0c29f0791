# Audio File Structure

This document explains the file structure of the audio system in the freestyle app.

## Overview

The audio system is organized into several directories:

- `frontend/src/audio/` - Main directory for audio-related code
  - `components/` - React components for audio functionality
  - `hooks/` - React hooks for audio functionality
  - `services/` - Services for audio processing and recording
  - `utils/` - Utility functions for audio
  - `workers/` - TypeScript source for Web Workers
  - `worklets/` - TypeScript source for Audio Worklets

- `frontend/public/` - Compiled JavaScript files for Web Workers and Audio Worklets
  - `audioProcessor.worker.js` - Compiled Web Worker for audio processing
  - `syncRecorder.worklet.js` - Compiled Audio Worklet for recording

## Compilation Process

The TypeScript source files for Web Workers and Audio Worklets are compiled to JavaScript and placed in the `frontend/public/` directory. This is necessary because Web Workers and Audio Worklets must be loaded from separate JavaScript files.

### Web Workers

- Source: `frontend/src/audio/workers/audioProcessor.worker.ts`
- Compiled: `frontend/public/audioProcessor.worker.js`

### Audio Worklets

- Source: `frontend/src/audio/worklets/syncRecorder.worklet.ts`
- Compiled: `frontend/public/syncRecorder.worklet.js`

## Key Files

### Components

- `RecordingSystem.tsx` - Main component for recording
- `PreviewPlayer.tsx` - Component for playing back recordings
- `TransportControls.tsx` - Component for playback controls

### Hooks

- `useSyncAudioRecording.ts` - Hook for recording with synchronization
- `useEnhancedAudioPlayback.ts` - Hook for enhanced audio playback
- `useAudioWorker.ts` - Hook for accessing the Web Worker
- `useWaveform.ts` - Hook for waveform visualization

### Services

- `syncAudioRecorder.ts` - Service for synchronized audio recording
- `audioWorkerService.ts` - Service for interacting with the Web Worker

### Workers and Worklets

- `audioProcessor.worker.ts` - Web Worker for audio processing
- `syncRecorder.worklet.ts` - Audio Worklet for recording

## Deprecated Files

The following files have been deprecated and replaced:

- `audioRecorder.ts` - Replaced by `syncAudioRecorder.ts`
- `audioProcessor.ts` - Functionality moved to the Web Worker
- `useAudioRecording.ts` - Replaced by `useSyncAudioRecording.ts`

## Best Practices

1. **Web Workers and Audio Worklets**:
   - Keep the TypeScript source in the `src` directory
   - Compile to JavaScript in the `public` directory
   - Use the compiled JavaScript files in the code

2. **Services**:
   - Keep audio processing logic in the Web Worker
   - Keep recording logic in the Audio Worklet
   - Use services to interact with the Web Worker and Audio Worklet

3. **Hooks**:
   - Use hooks to provide React integration
   - Keep hooks focused on a single responsibility
   - Use composition to combine hooks

4. **Components**:
   - Keep components focused on UI
   - Use hooks for logic
   - Keep state management in hooks or stores
