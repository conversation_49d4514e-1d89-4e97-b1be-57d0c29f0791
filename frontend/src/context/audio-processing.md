# Audio Processing Architecture

## Overview

The freestyle app uses a Web Worker-based architecture for audio processing to ensure smooth UI performance. This document explains the architecture, components, and workflow of the audio processing system.

## Key Components

### 1. Web Worker (`audioProcessor.worker.js`)

The Web Worker is the core of the audio processing system. It runs in a separate thread from the main UI thread, allowing CPU-intensive audio processing tasks to be performed without causing UI jank.

**Location:** `frontend/public/audioProcessor.worker.js` (compiled from `frontend/src/audio/workers/audioProcessor.worker.ts`)

**Key Features:**
- Audio decoding and encoding
- Mixing audio buffers (recording + beat)
- Applying audio effects (compression, reverb, stereo widening)
- Generating waveform data for visualization
- Analyzing audio levels
- Exporting recordings with metadata

### 2. Audio Worklet (`syncRecorder.worklet.js`)

The Audio Worklet provides sample-accurate audio recording with precise timing. It runs in the audio thread, ensuring high-quality recording without dropouts.

**Location:** `frontend/public/syncRecorder.worklet.js` (compiled from `frontend/src/audio/worklets/syncRecorder.worklet.ts`)

**Key Features:**
- Sample-accurate audio recording
- Synchronization with beat playback
- Real-time audio level monitoring
- Precise timing information for accurate mixing

### 3. Audio Worker Service (`audioWorkerService.ts`)

This service provides a centralized interface to the Web Worker, handling initialization, communication, and lifecycle management.

**Location:** `frontend/src/audio/services/audioWorkerService.ts`

**Key Features:**
- Worker initialization and termination
- Type-safe communication with the worker
- High-level methods for common audio processing tasks
- Error handling and fallback mechanisms

### 4. Sync Audio Recorder (`syncAudioRecorder.ts`)

This service handles the recording process, using the Audio Worklet for high-quality recording and the Web Worker for processing.

**Location:** `frontend/src/audio/services/syncAudioRecorder.ts`

**Key Features:**
- Microphone access and management
- Recording state management
- Integration with Audio Worklet
- Processing recordings with the Web Worker

### 5. React Hooks

The app provides several React hooks for easy integration with components:

#### `useAudioWorker`

**Location:** `frontend/src/audio/hooks/useAudioWorker.ts`

Provides direct access to the Web Worker for audio processing tasks.

#### `useSyncAudioRecording`

**Location:** `frontend/src/audio/hooks/useSyncAudioRecording.ts`

Provides a high-level API for recording with synchronization to beat playback.

#### `useEnhancedAudioPlayback`

**Location:** `frontend/src/audio/hooks/useEnhancedAudioPlayback.ts`

Provides enhanced audio playback capabilities with waveform visualization.

## Workflow

### Recording Process

1. **Initialization:**
   - The `useSyncAudioRecording` hook initializes the Web Worker and Audio Worklet
   - The user selects a beat and adjusts microphone gain and beat volume

2. **Start Recording:**
   - The user clicks the microphone button
   - The `SyncAudioRecorder` requests microphone access
   - The Audio Worklet starts recording with precise timing
   - The beat playback starts simultaneously

3. **During Recording:**
   - The Audio Worklet captures audio samples with precise timing
   - The Audio Worklet monitors audio levels and sends them to the main thread
   - The UI updates to show the recording state and audio levels

4. **Stop Recording:**
   - The user clicks the microphone button again
   - The Audio Worklet stops recording and sends the recorded data
   - The beat playback stops
   - The `SyncAudioRecorder` processes the recording data

5. **Processing:**
   - The Web Worker decodes the recording and beat audio
   - The Web Worker mixes the recording and beat with the specified gain values
   - The Web Worker applies effects (compression, etc.)
   - The Web Worker generates waveform data for visualization
   - The Web Worker creates a WAV blob of the mixed audio

6. **Preview:**
   - The app navigates to the preview page
   - The `PreviewPlayer` component loads the mixed audio
   - The `WaveformVisualizer` displays the waveform using data from the Web Worker
   - The user can play, pause, and download the mixed recording

### Playback Process

1. **Initialization:**
   - The `useEnhancedAudioPlayback` hook initializes the audio element
   - The `useAudioWorker` hook initializes the Web Worker

2. **Loading Audio:**
   - The mixed audio blob is loaded into an audio element
   - The Web Worker generates waveform data for visualization

3. **Playback:**
   - The user controls playback with play/pause buttons
   - The waveform visualization shows the current playback position
   - The user can seek to different positions by clicking on the waveform

4. **Export:**
   - The user can download the mixed recording
   - The Web Worker adds metadata to the exported file

## Performance Considerations

- All CPU-intensive audio processing is offloaded to the Web Worker
- The Audio Worklet ensures sample-accurate recording without dropouts
- Waveform data is generated in the Web Worker to prevent UI jank
- Audio effects are applied in the Web Worker for better performance
- The architecture is designed to handle large audio files smoothly

## Future Enhancements

- Additional audio effects (EQ, delay, etc.)
- More advanced waveform visualizations
- Real-time effects during recording
- Multi-track recording and mixing
- Audio analysis for beat detection and synchronization
