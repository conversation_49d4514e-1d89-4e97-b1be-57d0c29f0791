# Audio Components Documentation

## Overview

The freestyle app uses a modular architecture for audio recording, processing, and playback. This document provides a comprehensive overview of all audio-related components and their relationships.

## Component Architecture

### 1. Core Components

#### RecordingSystem

**File:** `frontend/src/audio/components/RecordingSystem.tsx`

The main controller component for the recording process. It:
- Manages the recording state
- Handles user interactions (start/stop recording)
- Coordinates between UI components and audio services
- Manages navigation between recording and preview modes

#### PreviewPlayer

**File:** `frontend/src/audio/components/PreviewPlayer.tsx`

The component for playing back mixed recordings. It:
- Displays the waveform visualization
- Provides playback controls (play/pause, seek)
- Allows adjusting volume
- Provides options to download or record again

#### MicVinylIntegrated

**File:** `frontend/src/ui/MicVinylIntegrated.tsx`

The UI component for the microphone and vinyl visualization. It:
- Provides a visual representation of the recording state
- <PERSON><PERSON> click events for starting/stopping recording
- Animates based on recording state and audio level

### 2. Services

#### SyncAudioRecorder

**File:** `frontend/src/audio/services/syncAudioRecorder.ts`

The service for synchronized audio recording. It:
- Manages the AudioContext and AudioWorklet
- Handles microphone access and recording
- Provides precise timing information for synchronization
- Processes recording data for preview

#### AudioWorkerService

**File:** `frontend/src/audio/services/audioWorkerService.ts`

The service for interacting with the Web Worker. It:
- Initializes and manages the Web Worker
- Provides methods for audio processing
- Handles communication between the main thread and the Web Worker
- Provides error handling and fallback mechanisms

### 3. Hooks

#### useSyncAudioRecording

**File:** `frontend/src/audio/hooks/useSyncAudioRecording.ts`

The hook for recording with synchronization. It:
- Initializes the SyncAudioRecorder
- Manages recording state
- Handles starting and stopping recording
- Processes recordings for preview
- Provides audio level information for visualization

#### useEnhancedAudioPlayback

**File:** `frontend/src/audio/hooks/useEnhancedAudioPlayback.ts`

The hook for enhanced audio playback. It:
- Manages audio element state
- Provides playback controls
- Handles time updates and seeking
- Manages volume control
- Provides error handling

#### useAudioWorker

**File:** `frontend/src/audio/hooks/useAudioWorker.ts`

The hook for accessing the Web Worker. It:
- Initializes the Web Worker
- Provides methods for audio processing
- Generates waveform data for visualization
- Analyzes audio levels
- Exports recordings with metadata

#### useWaveform

**File:** `frontend/src/audio/hooks/useWaveform.ts`

The hook for waveform visualization. It:
- Generates waveform data from audio
- Renders the waveform on a canvas
- Handles user interactions (seeking)
- Provides loading and error states
- Supports precomputed peaks from the Web Worker

### 4. Web Worker and Worklet

#### audioProcessor.worker.js

**File:** `frontend/public/audioProcessor.worker.js`

The Web Worker for audio processing. It:
- Decodes audio files
- Mixes audio buffers
- Applies audio effects
- Generates waveform data
- Analyzes audio levels
- Exports recordings with metadata

#### syncRecorder.worklet.js

**File:** `frontend/public/syncRecorder.worklet.js`

The AudioWorklet for precise recording. It:
- Records audio with sample-accurate timing
- Maintains synchronization with beat playback
- Provides real-time audio level monitoring
- Stores timing information for accurate mixing

### 5. UI Components

#### WaveformVisualizer

**File:** `frontend/src/ui/visualizers/WaveformVisualizer.tsx`

The component for visualizing audio waveforms. It:
- Renders the waveform on a canvas
- Shows the current playback position
- Handles user interactions (seeking)
- Supports precomputed peaks from the Web Worker
- Provides loading and error states

#### VolumeSlider

**File:** `frontend/src/ui/controls/VolumeSlider.tsx`

The component for adjusting volume. It:
- Provides a slider for adjusting volume
- Shows the current volume level
- Persists volume settings in localStorage
- Supports different volume ranges

## Data Flow

### Recording Flow

1. User clicks the microphone button in `MicVinylIntegrated`
2. `RecordingSystem` calls `handleStartRecording` from `useSyncAudioRecording`
3. `useSyncAudioRecording` initializes `SyncAudioRecorder` and starts recording
4. `SyncAudioRecorder` initializes the AudioWorklet and starts recording
5. The AudioWorklet records audio samples with precise timing
6. User clicks the microphone button again to stop recording
7. `RecordingSystem` calls `handleStopRecording` from `useSyncAudioRecording`
8. `useSyncAudioRecording` stops recording and processes the recording
9. The Web Worker processes the recording and beat, mixing them together
10. The processed recording is stored in the audio store
11. The app navigates to the preview page

### Playback Flow

1. `PreviewPlayer` initializes `useEnhancedAudioPlayback` and `useAudioWorker`
2. `useAudioWorker` generates waveform data from the mixed audio
3. `WaveformVisualizer` displays the waveform using the precomputed peaks
4. User clicks play/pause button to control playback
5. `useEnhancedAudioPlayback` manages the audio element and playback state
6. The waveform visualization updates to show the current playback position
7. User can seek to different positions by clicking on the waveform
8. User can adjust volume using the volume slider
9. User can download the mixed recording or record again

## State Management

The app uses Zustand for state management:

### AudioStore

**File:** `frontend/src/stores/audioStore.ts`

Stores audio-related state:
- Recording state
- Mixed audio buffer and blob
- Audio URL
- Duration
- Error state

### BeatStore

**File:** `frontend/src/stores/beatStore.ts`

Stores beat-related state:
- Current beat
- Beat audio buffer
- Playback state (playing, current time, duration)
- Beat volume
- Recently used and favorite beats

## Performance Considerations

1. **Web Worker:** All CPU-intensive audio processing is offloaded to the Web Worker to prevent UI jank.
2. **AudioWorklet:** The AudioWorklet ensures sample-accurate recording without dropouts.
3. **Precomputed Peaks:** Waveform data is generated in the Web Worker and passed to the UI for efficient rendering.
4. **Lazy Loading:** Audio files are loaded only when needed to reduce initial load time.
5. **Caching:** Waveform data is cached to prevent regenerating it for the same audio.

## Future Enhancements

1. **Additional Effects:** Add more audio effects like EQ, delay, and reverb.
2. **Multi-track Recording:** Support recording multiple tracks and mixing them.
3. **Real-time Effects:** Apply effects during recording for immediate feedback.
4. **Advanced Visualization:** Add more visualization options like spectrum analyzer.
5. **Export Options:** Support more export formats and quality settings.
