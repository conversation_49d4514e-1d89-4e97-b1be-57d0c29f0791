# Freestyle App Frontend Architecture

This document outlines the architecture of the Freestyle App frontend codebase.

## Directory Structure

```
frontend/src/
├── app/                    # Next.js app router pages
├── audio/                  # All audio-related functionality
│   ├── components/         # Audio UI components
│   ├── hooks/              # Audio-related hooks
│   ├── services/           # Audio processing services
│   └── utils/              # Audio utility functions
├── ui/                     # UI components
│   ├── common/             # Common UI components
│   ├── controls/           # UI controls (sliders, buttons, etc.)
│   ├── layout/             # Layout components
│   └── visualizers/        # Visual components (waveforms, etc.)
├── stores/                 # Zustand stores
├── types/                  # TypeScript type definitions
├── utils/                  # General utility functions
└── styles/                 # Global styles
```

## Key Components

### Audio Components

- **RecordingSystem**: Main component for recording freestyles with beats
- **PreviewPlayer**: Component for playing back recorded freestyles
- **TransportControls**: Audio playback controls (play/pause, seek, etc.)

### UI Components

- **MicVinylIntegrated**: Vinyl record with integrated microphone visualization
- **TonearmComponent**: Tonearm visualization for the vinyl record
- **WaveformVisualizer**: Audio waveform visualization with playhead
- **CollapsiblePanel**: Expandable panel for technical controls
- **AudioControlsTab**: Technical audio controls (mic input, gain, etc.)
- **BeatLibraryTab**: Beat selection interface

### Hooks

- **useAudioPlayback**: Hook for audio playback functionality
- **useAudioRecording**: Hook for audio recording functionality
- **useWaveform**: Hook for waveform visualization

### Services

- **audioProcessor**: Service for processing audio data
- **audioRecorder**: Service for recording audio from the microphone

### Stores

- **audioStore**: State management for audio recording and playback
- **beatStore**: State management for beats
- **previewStore**: State management for preview mode

## Main Pages

- **session/page.tsx**: Main freestyle recording interface
- **session/preview/page.tsx**: Preview and export recorded freestyles

## Component Responsibilities

### Audio Components

- **RecordingSystem**: Manages the recording process, including microphone access, beat playback, and navigation to preview mode
- **PreviewPlayer**: Handles playback of recorded freestyles, including waveform visualization and download functionality
- **TransportControls**: Provides play/pause controls, time display, and seeking functionality

### UI Components

- **MicVinylIntegrated**: Renders the vinyl record visualization with integrated microphone, handles user interactions
- **TonearmComponent**: Renders the tonearm visualization with animations for playback state
- **WaveformVisualizer**: Renders waveform visualization with playhead for seeking and gradient fill
- **CollapsiblePanel**: Provides an expandable panel for technical controls
- **AudioControlsTab**: Provides controls for microphone input, gain, and beat volume
- **BeatLibraryTab**: Provides an interface for selecting beats

## Data Flow

1. User interacts with the **MicVinylIntegrated** component to start recording
2. **RecordingSystem** handles the recording process using the **useAudioRecording** hook
3. **audioRecorder** service captures audio from the microphone
4. When recording is complete, **audioProcessor** service processes the audio
5. **RecordingSystem** navigates to preview mode
6. **PreviewPlayer** plays back the recorded freestyle using the **useAudioPlayback** hook
7. **WaveformVisualizer** displays the waveform of the recorded freestyle

## State Management

- **audioStore**: Manages recording state, audio blobs, and playback state
- **beatStore**: Manages the current beat and beat library
- **previewStore**: Manages preview mode state and navigation

## Future Improvements

- Implement Web Workers for audio processing to prevent UI jank
- Add beat selection functionality to the BeatLibraryTab
- Improve error handling and recovery
- Add unit tests for core functionality
