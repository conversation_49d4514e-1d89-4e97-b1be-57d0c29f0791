import { Config } from "./types";
import { productionConfig } from "./production";

const developmentConfig: Config = {
  ...productionConfig,
  api: {
    ...productionConfig.api,
    baseUrl: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001",
  },
  monitoring: {
    ...productionConfig.monitoring,
    sentry: {
      ...productionConfig.monitoring.sentry,
      environment: "development",
      tracesSampleRate: 1.0,
    },
    datadog: {
      ...productionConfig.monitoring.datadog,
      env: "development",
      sessionSampleRate: 100,
      sessionReplaySampleRate: 100,
    },
  },
  security: {
    ...productionConfig.security,
    corsOrigins: ["http://localhost:3000", "http://localhost:3001"],
  },
};

const stagingConfig: Config = {
  ...productionConfig,
  api: {
    ...productionConfig.api,
    baseUrl:
      process.env.NEXT_PUBLIC_STAGING_API_URL ||
      "https://api-staging.freestyle-ai.com",
  },
  monitoring: {
    ...productionConfig.monitoring,
    sentry: {
      ...productionConfig.monitoring.sentry,
      environment: "staging",
      tracesSampleRate: 0.5,
    },
    datadog: {
      ...productionConfig.monitoring.datadog,
      env: "staging",
      sessionSampleRate: 100,
      sessionReplaySampleRate: 50,
    },
  },
};

type Environment = "development" | "production" | "staging" | "test";

const getConfig = (): Config => {
  const env = process.env.NODE_ENV as Environment;
  switch (env) {
    case "production":
      return productionConfig;
    case "staging":
      return stagingConfig;
    default:
      return developmentConfig;
  }
};

export const config = getConfig();

// Type-safe environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Base Node.js process.env properties
      [key: string]: string | undefined;
      // Custom environment variables
      NEXT_PUBLIC_API_URL?: string;
      NEXT_PUBLIC_STAGING_API_URL?: string;
      NEXT_PUBLIC_PRODUCTION_API_URL?: string;
      NEXT_PUBLIC_SENTRY_DSN?: string;
      NEXT_PUBLIC_DD_APPLICATION_ID?: string;
      NEXT_PUBLIC_DD_CLIENT_TOKEN?: string;
      DD_SITE?: string;
      NEW_RELIC_LICENSE_KEY?: string;
      NEW_RELIC_APP_ID?: string;
      NEW_RELIC_ACCOUNT_ID?: string;
      ALLOWED_ORIGINS?: string;
    }
  }
}
