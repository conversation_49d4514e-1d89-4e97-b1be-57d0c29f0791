import { Config } from "./types";

export const productionConfig: Config = {
  api: {
    baseUrl:
      process.env.NEXT_PUBLIC_PRODUCTION_API_URL ||
      "https://api.freestyle-ai.com",
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  monitoring: {
    sentry: {
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      environment: "production",
      tracesSampleRate: 0.1,
    },
    datadog: {
      applicationId: process.env.NEXT_PUBLIC_DD_APPLICATION_ID,
      clientToken: process.env.NEXT_PUBLIC_DD_CLIENT_TOKEN,
      site: process.env.DD_SITE || "datadoghq.com",
      service: "freestyle-ai-frontend",
      env: "production",
      sessionSampleRate: 100,
      sessionReplaySampleRate: 20,
      trackUserInteractions: true,
      trackResources: true,
      trackLongTasks: true,
    },
    newrelic: {
      licenseKey: process.env.NEW_RELIC_LICENSE_KEY,
      applicationId: process.env.NEW_RELIC_APP_ID,
      accountId: process.env.NEW_RELIC_ACCOUNT_ID,
    },
  },
  cache: {
    staticMaxAge: 30 * 24 * 60 * 60, // 30 days
    dynamicMaxAge: 60 * 60, // 1 hour
    apiMaxAge: 5 * 60, // 5 minutes
    recordingMaxRetries: 3,
    cleanupInterval: 24 * 60 * 60, // 1 day
  },
  security: {
    rateLimiting: {
      maxRequests: 100,
      windowMs: 15 * 60 * 1000, // 15 minutes
    },
    csrfEnabled: true,
    corsOrigins: process.env.ALLOWED_ORIGINS?.split(",") || [
      "https://freestyle-ai.com",
    ],
  },
  features: {
    offlineMode: true,
    pushNotifications: true,
    backgroundSync: true,
    analytics: true,
  },
  audio: {
    maxDuration: 300, // 5 minutes
    format: "audio/webm",
    sampleRate: 44100,
    channelCount: 2,
    bitRate: 128000,
  },
  ai: {
    suggestionInterval: 2000, // 2 seconds
    maxSuggestionsPerSession: 50,
    confidenceThreshold: 0.7,
  },
};
