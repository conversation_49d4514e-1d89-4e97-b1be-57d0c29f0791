export interface Config {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  monitoring: {
    sentry: {
      dsn?: string;
      environment: string;
      tracesSampleRate: number;
    };
    datadog: {
      applicationId?: string;
      clientToken?: string;
      site: string;
      service: string;
      env: string;
      sessionSampleRate: number;
      sessionReplaySampleRate: number;
      trackUserInteractions: boolean;
      trackResources: boolean;
      trackLongTasks: boolean;
    };
    newrelic: {
      licenseKey?: string;
      applicationId?: string;
      accountId?: string;
    };
  };
  cache: {
    staticMaxAge: number;
    dynamicMaxAge: number;
    apiMaxAge: number;
    recordingMaxRetries: number;
    cleanupInterval: number;
  };
  security: {
    rateLimiting: {
      maxRequests: number;
      windowMs: number;
    };
    csrfEnabled: boolean;
    corsOrigins: string[];
  };
  features: {
    offlineMode: boolean;
    pushNotifications: boolean;
    backgroundSync: boolean;
    analytics: boolean;
  };
  audio: {
    maxDuration: number;
    format: string;
    sampleRate: number;
    channelCount: number;
    bitRate: number;
  };
  ai: {
    suggestionInterval: number;
    maxSuggestionsPerSession: number;
    confidenceThreshold: number;
  };
}
