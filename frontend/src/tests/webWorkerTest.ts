/**
 * Web Worker Test Script
 * 
 * This script tests the Web Worker implementation by simulating a recording workflow.
 * It verifies that the Web Worker can:
 * - Initialize properly
 * - Decode audio files
 * - Mix audio buffers
 * - Apply effects
 * - Generate waveform data
 * - Process recordings for preview
 * 
 * Run this script in the browser console to test the Web Worker implementation.
 */

import { initAudioWorker, getAudioWorker } from '../audio/services/audioWorkerService';

// Test the Web Worker implementation
async function testWebWorker() {
  console.log('Starting Web Worker test...');
  
  try {
    // Initialize the Web Worker
    console.log('Initializing Web Worker...');
    const worker = await initAudioWorker();
    console.log('Web Worker initialized successfully');
    
    // Test decoding audio
    console.log('Testing audio decoding...');
    const beatResponse = await fetch('/beats/beat1.mp3');
    const beatArrayBuffer = await beatResponse.arrayBuffer();
    const beatBuffer = await worker.decodeAudio(beatArrayBuffer);
    console.log('Beat decoded successfully:', {
      duration: beatBuffer.duration,
      sampleRate: beatBuffer.sampleRate,
      numberOfChannels: beatBuffer.numberOfChannels
    });
    
    // Create a test recording (sine wave)
    console.log('Creating test recording...');
    const audioContext = new AudioContext();
    const recordingBuffer = audioContext.createBuffer(
      1, // mono
      audioContext.sampleRate * 5, // 5 seconds
      audioContext.sampleRate
    );
    
    // Fill with sine wave
    const channelData = recordingBuffer.getChannelData(0);
    for (let i = 0; i < channelData.length; i++) {
      channelData[i] = Math.sin(i * 440 * Math.PI * 2 / audioContext.sampleRate) * 0.5;
    }
    
    console.log('Test recording created:', {
      duration: recordingBuffer.duration,
      sampleRate: recordingBuffer.sampleRate,
      numberOfChannels: recordingBuffer.numberOfChannels
    });
    
    // Test mixing audio
    console.log('Testing audio mixing...');
    const mixedBuffer = await worker.mixAudioBuffers(
      recordingBuffer,
      beatBuffer,
      1.0, // recording gain
      0.8  // beat gain
    );
    
    console.log('Audio mixed successfully:', {
      duration: mixedBuffer.duration,
      sampleRate: mixedBuffer.sampleRate,
      numberOfChannels: mixedBuffer.numberOfChannels
    });
    
    // Test applying compression
    console.log('Testing compression...');
    const compressedBuffer = await worker.applyCompression(
      mixedBuffer,
      0.5, // threshold
      4    // ratio
    );
    
    console.log('Compression applied successfully:', {
      duration: compressedBuffer.duration,
      sampleRate: compressedBuffer.sampleRate,
      numberOfChannels: compressedBuffer.numberOfChannels
    });
    
    // Test generating waveform data
    console.log('Testing waveform generation...');
    const waveformData = await worker.generateWaveform(
      compressedBuffer,
      1000 // number of points
    );
    
    console.log('Waveform generated successfully:', {
      points: waveformData.length,
      firstPoint: waveformData[0],
      lastPoint: waveformData[waveformData.length - 1]
    });
    
    // Test analyzing audio levels
    console.log('Testing audio level analysis...');
    const levels = await worker.analyzeAudioLevel(compressedBuffer);
    
    console.log('Audio levels analyzed successfully:', {
      rms: levels.rms,
      db: levels.db,
      peak: levels.peak
    });
    
    // Test converting to WAV
    console.log('Testing WAV conversion...');
    const wavBlob = await worker.audioBufferToWav(compressedBuffer);
    
    console.log('WAV conversion successful:', {
      size: wavBlob.size,
      type: wavBlob.type
    });
    
    // Test processing recording for preview
    console.log('Testing processing recording for preview...');
    
    // Convert recording buffer to ArrayBuffer
    const recordingArrayBuffer = await audioBufferToArrayBuffer(recordingBuffer);
    
    const result = await worker.processRecordingForPreview(
      recordingArrayBuffer,
      beatArrayBuffer,
      1.0, // recording gain
      0.8, // beat gain
      {
        applyCompression: true,
        compressionThreshold: 0.5,
        compressionRatio: 4,
        applyReverb: true,
        reverbMix: 0.2,
        applyStereoWidening: true,
        stereoWidth: 0.3
      }
    );
    
    console.log('Processing recording for preview successful:', {
      duration: result.duration,
      waveformPoints: result.waveformData.length,
      blobSize: result.mixedBlob.size,
      levels: result.levels
    });
    
    // Test exporting recording
    console.log('Testing exporting recording...');
    const exportResult = await worker.exportRecording(
      recordingArrayBuffer,
      beatArrayBuffer,
      {
        title: 'Test Recording',
        artist: 'Test Artist',
        beatTitle: 'Test Beat',
        beatProducer: 'Test Producer',
        date: new Date().toISOString()
      },
      {
        recordingGain: 1.0,
        beatGain: 0.8,
        format: 'wav',
        applyCompression: true,
        compressionThreshold: 0.5,
        compressionRatio: 4,
        applyReverb: true,
        reverbMix: 0.2,
        applyStereoWidening: true,
        stereoWidth: 0.3
      }
    );
    
    console.log('Exporting recording successful:', {
      duration: exportResult.duration,
      blobSize: exportResult.blob.size,
      metadata: exportResult.metadata
    });
    
    console.log('Web Worker test completed successfully!');
    return true;
  } catch (error) {
    console.error('Web Worker test failed:', error);
    return false;
  }
}

// Helper function to convert AudioBuffer to ArrayBuffer
async function audioBufferToArrayBuffer(buffer: AudioBuffer): Promise<ArrayBuffer> {
  // Create offline context
  const offlineContext = new OfflineAudioContext(
    buffer.numberOfChannels,
    buffer.length,
    buffer.sampleRate
  );
  
  // Create buffer source
  const source = offlineContext.createBufferSource();
  source.buffer = buffer;
  source.connect(offlineContext.destination);
  
  // Start source and render
  source.start();
  const renderedBuffer = await offlineContext.startRendering();
  
  // Convert to WAV
  const wavBlob = await new Promise<Blob>((resolve) => {
    const numChannels = renderedBuffer.numberOfChannels;
    const sampleRate = renderedBuffer.sampleRate;
    const length = renderedBuffer.length * numChannels * 2; // 16-bit samples
    
    // Create WAV header
    const view = new DataView(new ArrayBuffer(44 + length));
    
    // "RIFF" chunk descriptor
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    writeString(view, 8, 'WAVE');
    
    // "fmt " sub-chunk
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true); // subchunk size
    view.setUint16(20, 1, true); // PCM format
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * 2, true); // byte rate
    view.setUint16(32, numChannels * 2, true); // block align
    view.setUint16(34, 16, true); // bits per sample
    
    // "data" sub-chunk
    writeString(view, 36, 'data');
    view.setUint32(40, length, true);
    
    // Write audio data
    let offset = 44;
    for (let i = 0; i < renderedBuffer.length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = renderedBuffer.getChannelData(channel)[i];
        const value = Math.max(-1, Math.min(1, sample)); // Clamp
        const int16 = value < 0 ? value * 32768 : value * 32767; // Convert to 16-bit
        view.setInt16(offset, int16, true);
        offset += 2;
      }
    }
    
    // Create blob
    const blob = new Blob([view], { type: 'audio/wav' });
    resolve(blob);
  });
  
  // Convert to ArrayBuffer
  return await wavBlob.arrayBuffer();
}

// Helper function to write a string to a DataView
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

// Export the test function
export default testWebWorker;
