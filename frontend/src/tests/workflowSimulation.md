# Audio Recording and Processing Workflow Simulation

This document simulates the complete workflow of recording, processing, and playing back audio in the freestyle app.

## Components Involved

1. **Recording Components:**
   - `RecordingSystem` - Main controller component for recording
   - `MicVinylIntegrated` - UI component for microphone and vinyl visualization
   - `SyncAudioRecorder` - Service for synchronized audio recording
   - `syncRecorder.worklet.js` - AudioWorklet for precise recording

2. **Processing Components:**
   - `audioProcessor.worker.js` - Web Worker for audio processing
   - `audioWorkerService.ts` - Service for interacting with the Web Worker
   - `useSyncAudioRecording` - Hook for recording with synchronization

3. **Playback Components:**
   - `PreviewPlayer` - Component for playing back mixed recordings
   - `WaveformVisualizer` - Component for visualizing audio waveforms
   - `useEnhancedAudioPlayback` - Hook for enhanced audio playback
   - `useAudioWorker` - Hook for accessing the Web Worker

## Workflow Simulation

### 1. Recording Initialization

When the user navigates to the session page, the following initialization occurs:

```javascript
// In RecordingSystem.tsx
const {
  error,
  transportRef,
  handleStartRecording,
  handleStopRecording,
  level
} = useSyncAudioRecording({
  onRecordingComplete: (result) => {
    // Store recording data and navigate to preview page
    storeRecordingData();
    setPreviewMode(true);
    router.push('/session/preview');
  }
});
```

The `useSyncAudioRecording` hook initializes:

```javascript
// In useSyncAudioRecording.ts
// Initialize Web Worker
const workerApi = await initAudioWorker();
workerApiRef.current = workerApi;

// Initialize SyncAudioRecorder
recorderRef.current = new SyncAudioRecorder({
  deviceId: selectedInput,
  onStateChange: (state) => {
    setRecordingState(state);
  },
  onError: (errorMsg) => {
    setError(errorMsg);
  },
  onLevelChange: (newLevel) => {
    setLevel(newLevel);
  }
});
```

### 2. Starting Recording

When the user clicks the microphone button:

```javascript
// In RecordingSystem.tsx
const handleMicClick = useCallback(async () => {
  if (recordingState === 'idle' || recordingState === 'stopped' || recordingState === 'error') {
    // Start recording
    await handleStartRecording();
  } else if (recordingState === 'recording') {
    // Stop recording
    await handleStopRecording();
  }
}, [recordingState, handleStartRecording, handleStopRecording]);
```

The `handleStartRecording` function:

```javascript
// In useSyncAudioRecording.ts
const handleStartRecording = useCallback(async () => {
  try {
    // Start recording and beat playback almost simultaneously
    await startRecording(currentBeatUrl);
    await startBeat();
  } catch (error) {
    console.error('Error starting recording:', error);
    setError(error?.message || 'Failed to start recording');
  }
}, [recordingState, startRecording, currentBeatUrl, startBeat]);
```

The `startRecording` function initializes the AudioWorklet:

```javascript
// In syncAudioRecorder.ts
public async start(syncWithTimestamp?: number): Promise<void> {
  // Initialize audio context
  const initialized = await this.initAudioContext();
  
  // Get media stream
  this.mediaStream = await navigator.mediaDevices.getUserMedia({
    audio: {
      deviceId: this.deviceId ? { exact: this.deviceId } : undefined,
      echoCancellation: false,
      noiseSuppression: false,
      autoGainControl: false
    }
  });
  
  // Create source node
  this.sourceNode = this.audioContext!.createMediaStreamSource(this.mediaStream);
  
  // Connect source to gain node
  this.sourceNode.connect(this.gainNode!);
  
  // Start recording in worklet
  if (this.workletNode) {
    // Set parameters
    const isRecordingParam = this.workletNode.parameters.get('isRecording');
    if (isRecordingParam) isRecordingParam.setValueAtTime(1, this.audioContext!.currentTime);
  }
}
```

The AudioWorklet starts recording:

```javascript
// In syncRecorder.worklet.js
process(inputs, outputs, parameters) {
  // Get parameters
  const isRecordingParam = parameters.isRecording[0];
  
  // Update recording state if parameter changed
  if (isRecordingParam === 1 && !this._isRecording) {
    this._isRecording = true;
    this._recordingStartTime = currentTime;
    this._buffer = [];
  }
  
  // Process input
  const input = inputs[0][0];
  if (input && this._isRecording) {
    // Record audio
    this._buffer.push(new Float32Array(input));
  }
  
  return true;
}
```

### 3. Stopping Recording

When the user clicks the microphone button again:

```javascript
// In useSyncAudioRecording.ts
const handleStopRecording = useCallback(async () => {
  try {
    // Stop beat playback
    stopBeat();
    
    // Stop recording
    const result = await stopRecording();
    
    // Process recording for preview
    processRecordingForPreview(blob, currentBeatUrl, micGain, beatVolume)
      .then((result) => {
        // Store the mixed buffer in the audio store
        useAudioStore.setState({
          mixedBuffer,
          mixedUrl,
          mixedBlob,
          duration
        });
        
        // Call the callback
        if (onRecordingComplete) {
          onRecordingComplete({
            mixedBuffer,
            mixedUrl,
            mixedBlob,
            duration
          });
        }
      });
  } catch (error) {
    console.error('Error stopping recording:', error);
    setError(error?.message || 'Error stopping recording');
  }
}, [recordingState, stopRecording, stopBeat, currentBeatUrl, onRecordingComplete, gainPercent, beatVolume]);
```

The `stopRecording` function stops the AudioWorklet:

```javascript
// In syncAudioRecorder.ts
public async stop(): Promise<RecordingResult | null> {
  // Stop recording in worklet
  if (this.workletNode) {
    // Set parameter
    const isRecordingParam = this.workletNode.parameters.get('isRecording');
    if (isRecordingParam) isRecordingParam.setValueAtTime(0, this.audioContext!.currentTime);
  }
  
  // Stop media stream tracks
  if (this.mediaStream) {
    this.mediaStream.getTracks().forEach(track => {
      track.stop();
    });
  }
}
```

### 4. Processing the Recording

The `processRecordingForPreview` function uses the Web Worker:

```javascript
// In audioWorkerService.ts
export async function processRecordingForPreview(
  recordingBlob: Blob,
  beatUrl: string,
  recordingGain: number = 1.0,
  beatGain: number = 0.8,
  options = {
    applyCompression: true,
    compressionThreshold: 0.5,
    compressionRatio: 4
  }
): Promise<{
  mixedBuffer: AudioBuffer;
  mixedBlob: Blob;
  duration: number;
  waveformData: number[][];
  levels: {rms: number, db: number, peak: number};
}> {
  // Get worker
  const worker = await getAudioWorker();
  
  // Convert recording blob to array buffer
  const recordingArrayBuffer = await recordingBlob.arrayBuffer();
  
  // Fetch beat
  const beatResponse = await fetch(beatUrl);
  const beatArrayBuffer = await beatResponse.arrayBuffer();
  
  // Process with worker
  return await worker.processRecordingForPreview(
    recordingArrayBuffer,
    beatArrayBuffer,
    recordingGain,
    beatGain,
    options
  );
}
```

The Web Worker processes the recording:

```javascript
// In audioProcessor.worker.js
async processRecordingForPreview(
  recordingArrayBuffer,
  beatArrayBuffer,
  recordingGain = 1.0,
  beatGain = 1.0,
  options = {
    applyCompression: true,
    compressionThreshold: 0.5,
    compressionRatio: 4
  }
) {
  // Decode recording
  const recordingBuffer = await this.decodeAudio(recordingArrayBuffer);
  
  // Decode beat
  const beatBuffer = await this.decodeAudio(beatArrayBuffer);
  
  // Mix audio with the specified gain values
  const mixedBuffer = await this.mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain);
  
  // Apply effects based on options
  let processedBuffer = mixedBuffer;
  if (options.applyCompression) {
    processedBuffer = await this.applyCompression(processedBuffer, options.compressionThreshold, options.compressionRatio);
  }
  
  // Generate waveform data for visualization
  const waveformData = await this.generateWaveform(processedBuffer, 1000);
  
  // Convert to WAV
  const mixedBlob = await this.audioBufferToWav(processedBuffer);
  
  return {
    mixedBuffer: processedBuffer,
    mixedBlob,
    duration: processedBuffer.duration,
    waveformData,
    levels: await this.analyzeAudioLevel(processedBuffer)
  };
}
```

### 5. Playback in Preview Mode

When the user navigates to the preview page:

```javascript
// In PreviewPlayer.tsx
const {
  isPlaying,
  isLoading,
  currentTime,
  duration,
  error,
  readyState,
  togglePlayPause,
  handleSeek,
  setVolume
} = useEnhancedAudioPlayback({
  autoPlay,
  onEnded: () => console.log("Playback ended"),
  onTimeUpdate: () => {
    // Additional time update handling if needed
  }
});

// Initialize audio worker for processing
const { 
  worker, 
  isInitialized: isWorkerInitialized,
  generateWaveform 
} = useAudioWorker();

// Generate waveform data using the Web Worker
useEffect(() => {
  if (isWorkerInitialized && mixedBuffer && worker) {
    const generateWaveformData = async () => {
      const data = await generateWaveform(mixedBuffer, 1000);
      setWaveformData(data);
    };
    
    generateWaveformData();
  }
}, [isWorkerInitialized, mixedBuffer, worker, generateWaveform]);
```

The waveform is displayed using the `WaveformVisualizer` component:

```jsx
<WaveformVisualizer
  audioBuffer={mixedBuffer}
  audioUrl={mixedBlob || mixedUrl || recordingBlob}
  currentTime={currentTime}
  duration={duration || 180}
  isPlaying={isPlaying}
  onSeek={handleSeek}
  height={128}
  gradientColors={["#5f6fff", "#7a6fff", "#b367ff", "#d45fff", "#ff5f7e", "#ff7a5f"]}
  waveformKey={`preview-waveform-${mixedUrl || (mixedBlob ? mixedBlob.size : 'default')}`}
  precomputedPeaks={waveformData.length > 0 ? waveformData : undefined}
/>
```

## Conclusion

The workflow simulation demonstrates that the freestyle app has a complete audio recording and processing system:

1. **Recording:** The app can record microphone input with precise timing using the AudioWorklet.
2. **Synchronization:** The recording is synchronized with the beat playback.
3. **Processing:** The Web Worker processes the recording and beat, mixing them together and applying effects.
4. **Visualization:** The waveform is generated by the Web Worker and displayed in the UI.
5. **Playback:** The mixed audio is played back with accurate timing and visualization.

All components work together to provide a seamless experience for the user, from recording to playback.
