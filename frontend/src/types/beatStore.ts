/**
 * Beat Store Types
 * 
 * This file defines the types for the beat store.
 */

export interface Beat {
  id: string | number;
  title: string;
  artist?: string;
  bpm?: number;
  key?: string;
  genre?: string;
  audio_url: string;
  createdAt?: string;
  attributes?: {
    audio_url?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface BeatStore {
  beats: Beat[];
  currentBeat: Beat | null;
  isLoading: boolean;
  error: string | null;
  favoriteBeats: string[];
  beatAudioBuffer: AudioBuffer | null;
  
  setCurrentBeat: (beat: Beat) => void;
  selectBeatByObject: (beat: Beat) => void;
  setBeatAudioBuffer: (buffer: AudioBuffer) => void;
  fetchBeats: () => Promise<void>;
  loadBeats: () => Promise<Beat[]>;
  addBeat: (beat: Beat) => void;
  updateBeat: (beat: Beat) => void;
  removeBeat: (beatId: string | number) => void;
  toggleFavorite: (beatId: string | number) => void;
  loadFavorites: () => void;
}
