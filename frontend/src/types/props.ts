import type { Beat } from "./beat";
import type { User } from "./user";
import type { ReactNode } from "react";
// import type { FreestyleSession } from "./session"; // Not currently used

// BeatPlayerProps removed

export interface BeatSearchProps {
  user?: User;
  onBeatSelect: (beat: Beat) => void;
}

// BeatQueueProps removed

export interface AudioRecorderProps {
  isPlaying: boolean;
  onRecordingComplete: (audioBlob: Blob) => void;
  onTranscriptUpdate?: (transcript: string) => void;
}

// FreestyleSessionProps removed

// AI Suggestions interface removed

// MobileAudioVisualizerProps removed

export interface AuthFormProps {
  type: "login" | "register";
  onSubmit: (data: {
    email: string;
    password: string;
    username?: string;
  }) => Promise<void>;
}

export interface FormProps {
  children: ReactNode;
  onSubmit: (data: Record<string, unknown>) => void | Promise<void>;
  className?: string;
}

export interface FormFieldProps {
  name: string;
  label?: string;
  required?: boolean;
  className?: string;
  validate?: (value: unknown) => string | undefined;
}

export interface ToastProps {
  message: string;
  type?: "success" | "error" | "info";
  duration?: number;
  onClose?: () => void;
}

export interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface DropdownProps {
  label: string;
  options: Array<{ value: string; label: string }>;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
}

export interface AudioInputFieldProps extends FormFieldProps {
  accept?: string;
  maxSize?: number;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  className?: string;
}

export interface BeatSelectorProps {
  name: string;
  label: string;
  beats: Beat[];
  required?: boolean;
  className?: string;
  onBeatSelect?: (beat: Beat) => void;
}

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}
