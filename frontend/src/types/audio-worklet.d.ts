declare class AudioWorkletProcessor {
  // Static properties
  static get parameterDescriptors(): AudioParamDescriptor[];

  // Instance properties
  readonly port: MessagePort;

  // Constructor
  constructor();

  // Process method
  process(
    inputs: Float32Array[][],
    outputs: Float32Array[][],
    parameters: Record<string, Float32Array>
  ): boolean;
}

declare interface AudioParamDescriptor {
  name: string;
  defaultValue?: number;
  minValue?: number;
  maxValue?: number;
}

declare interface AudioWorkletGlobalScope {
  registerProcessor(
    name: string,
    processorCtor: new (
      options?: AudioWorkletNodeOptions,
    ) => AudioWorkletProcessor,
  ): void;
  currentTime: number;
  sampleRate: number;
}

// Global registerProcessor function
declare function registerProcessor(
  name: string,
  processorCtor: typeof AudioWorkletProcessor
): void;

declare const globalThis: AudioWorkletGlobalScope;

declare interface WorkletMessageEvent extends MessageEvent {
  data: {
    type:
      | "updateOptions"
      | "speechStart"
      | "speechEnd"
      | "audioLevel"
      | "error";
    options?: {
      fftSize?: number;
      silenceThreshold?: number;
      pauseThreshold?: number;
    };
    level?: number;
    timestamp?: number;
    duration?: number;
    message?: string;
  };
}
