import React from 'react';

// This allows JSX to be used without explicitly importing React in every file
declare global {
  // Extend the Window interface
  interface Window {
    DD_RUM?: {
      addAction: (name: string, data?: Record<string, unknown>) => void;
      addError: (error: Error, context?: Record<string, unknown>) => void;
      addTiming: (name: string, time?: number) => void;
    };
    newrelic?: {
      addPageAction: (name: string, data?: Record<string, unknown>) => void;
      noticeError: (error: Error, context?: Record<string, unknown>) => void;
      setCustomAttribute: (key: string, value: string | number | boolean) => void;
    };
  }

  // Allow JSX without React import
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}
