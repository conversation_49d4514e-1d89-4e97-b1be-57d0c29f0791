/**
 * Audio Types
 *
 * This file defines TypeScript types for audio-related functionality.
 */

/**
 * Recording state
 */
export type RecordingState =
  | 'idle'        // Not recording
  | 'initializing' // Setting up recording
  | 'recording'   // Currently recording
  | 'stopping'    // Stopping recording
  | 'paused'      // Recording paused
  | 'stopped'     // Recording stopped
  | 'error';      // Error state

/**
 * Recording result
 */
export interface RecordingResult {
  blob: Blob;
  url: string;
  mimeType: string;
  duration: number;
}

/**
 * Audio store state
 */
export interface AudioStoreState {
  // Recording state
  recordingState: RecordingState;
  error: string | null;

  // Recording data
  recordingBlob: Blob | null;
  recordingUrl: string | null;
  recordingBuffer: AudioBuffer | null;

  // Mixed data (recording + beat)
  mixedBlob: Blob | null;
  mixedUrl: string | null;
  mixedBuffer: AudioBuffer | null;

  // Metadata
  duration: number;

  // Settings
  gainPercent: number;
  selectedInput: string;

  // Actions
  startRecording: (beatUrl?: string | null) => Promise<void>;
  stopRecording: () => Promise<RecordingResult | null>;
  setGainPercent: (percent: number) => void;
  setSelectedInput: (deviceId: string) => void;
}

/**
 * Preview store state
 */
export interface PreviewStoreState {
  isPreviewMode: boolean;
  previewRecordingUrl: string | null;
  previewBeatUrl: string | null;
  autoPlay: boolean;

  // Actions
  setPreviewMode: (isPreview: boolean) => void;
  setPreviewData: (recordingUrl: string | null, beatUrl: string | null) => void;
  setAutoPlay: (autoPlay: boolean) => void;
  clearPreviewData: () => void;
}
