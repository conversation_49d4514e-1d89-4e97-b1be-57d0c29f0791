import { useEffect, useCallback } from "react";

type KeyCombo = string[];
type ShortcutHandler = (e: KeyboardEvent) => void;

interface ShortcutConfig {
  keys: KeyCombo;
  handler: ShortcutHandler;
  description: string;
  scope?: string;
  preventDefault?: boolean;
}

interface UseKeyboardShortcutsOptions {
  scope?: string;
  enabled?: boolean;
}

const isInputElement = (element: Element | null): boolean => {
  if (!element) return false;
  const tagName = element.tagName.toLowerCase();
  return (
    tagName === "input" ||
    tagName === "textarea" ||
    tagName === "select" ||
    element.hasAttribute("contenteditable")
  );
};

export function useKeyboardShortcuts(
  shortcuts: ShortcutConfig[],
  options: UseKeyboardShortcutsOptions = {},
) {
  const { scope = "global", enabled = true } = options;

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      // Don't trigger shortcuts when typing in input elements
      if (isInputElement(document.activeElement)) return;

      const pressedKeys: string[] = [];
      if (event.ctrlKey || event.metaKey) pressedKeys.push("Control");
      if (event.altKey) pressedKeys.push("Alt");
      if (event.shiftKey) pressedKeys.push("Shift");
      pressedKeys.push(event.key.toLowerCase());

      for (const shortcut of shortcuts) {
        if (shortcut.scope && shortcut.scope !== scope) continue;

        const shortcutKeys = shortcut.keys.map((key) => key.toLowerCase());
        const isMatch =
          pressedKeys.length === shortcutKeys.length &&
          pressedKeys.every((key, i) => key === shortcutKeys[i]);

        if (isMatch) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          shortcut.handler(event);
          break;
        }
      }
    },
    [shortcuts, scope, enabled],
  );

  useEffect(() => {
    if (enabled) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [enabled, handleKeyDown]);

  // Return list of available shortcuts for documentation
  const getShortcutList = useCallback(() => {
    return shortcuts
      .filter((s) => !s.scope || s.scope === scope)
      .map((s) => ({
        keys: s.keys,
        description: s.description,
      }));
  }, [shortcuts, scope]);

  return { getShortcutList };
}
