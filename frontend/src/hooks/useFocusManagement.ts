import { useRef, useEffect, useCallback } from "react";

interface UseFocusManagementOptions {
  trapFocus?: boolean;
  restoreFocus?: boolean;
  autoFocus?: boolean;
}

export function useFocusManagement<T extends HTMLElement>(
  options: UseFocusManagementOptions = {},
) {
  const {
    trapFocus = false,
    restoreFocus = false,
    autoFocus = false,
  } = options;

  const containerRef = useRef<T>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  const getFocusableElements = useCallback(() => {
    if (!containerRef.current) return [];

    return Array.from(
      containerRef.current.querySelectorAll<HTMLElement>(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      ),
    ).filter(
      (el) =>
        !el.hasAttribute("disabled") &&
        el.getAttribute("aria-hidden") !== "true",
    );
  }, []);

  const handleFocusTrap = useCallback(
    (e: KeyboardEvent) => {
      if (!trapFocus || !containerRef.current) return;

      const focusableElements = getFocusableElements();
      if (focusableElements.length === 0) return;

      const firstFocusable = focusableElements[0];
      const lastFocusable = focusableElements[focusableElements.length - 1];

      if (e.key === "Tab") {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusable) {
            e.preventDefault();
            lastFocusable.focus();
          }
        } else {
          if (document.activeElement === lastFocusable) {
            e.preventDefault();
            firstFocusable.focus();
          }
        }
      }
    },
    [trapFocus, getFocusableElements],
  );

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Save current active element
    if (restoreFocus) {
      previousActiveElement.current = document.activeElement as HTMLElement;
    }

    // Auto focus first focusable element
    if (autoFocus) {
      const focusableElements = getFocusableElements();
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
    }

    // Add event listeners for focus trapping
    if (trapFocus) {
      document.addEventListener("keydown", handleFocusTrap);
    }

    return () => {
      // Cleanup event listeners
      if (trapFocus) {
        document.removeEventListener("keydown", handleFocusTrap);
      }

      // Restore focus
      if (restoreFocus && previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [
    trapFocus,
    restoreFocus,
    autoFocus,
    handleFocusTrap,
    getFocusableElements,
  ]);

  return {
    containerRef,
    getFocusableElements,
  };
}
