/**
 * useAudioPlayback Hook
 *
 * A custom hook that provides audio playback functionality.
 * This hook encapsulates all the logic for:
 * - Creating and managing audio elements
 * - Playing and pausing audio
 * - Tracking playback state and time
 * - Handling errors and recovery
 * - Seeking to specific times
 *
 * By moving this logic to a hook, we can reuse it across components
 * and keep the components focused on rendering.
 */

import { useState, useRef, useEffect, useCallback } from 'react';
import { useAudioStore } from '../../stores/audioStore';

export interface UseAudioPlaybackOptions {
  autoPlay?: boolean;
  onEnded?: () => void;
}

export function useAudioPlayback(options: UseAudioPlaybackOptions = {}) {
  const { autoPlay = false, onEnded } = options;

  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const rafRef = useRef<number | null>(null);

  // Get data from store
  const { mixedBlob, recordingBlob, mixedUrl } = useAudioStore();

  // Initialize audio element
  useEffect(() => {
    console.log("Initializing audio playback");

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      console.log("Creating new Audio element");
      // Check if we already have an audio element in the window object
      if ((window as any).__audioElement) {
        console.log("Reusing existing audio element");
        audioRef.current = (window as any).__audioElement;
      } else {
        const audio = new Audio();
        audioRef.current = audio;
        (window as any).__audioElement = audio;
        console.log("Created new audio element");
      }

      const audio = audioRef.current;

      if (audio) {
        // Add event listeners
        const handlePlay = () => {
          console.log("Audio started playing");
          setIsPlaying(true);
        };

        const handlePause = () => {
          console.log("Audio paused");
          setIsPlaying(false);
        };

        const handleEnded = () => {
          console.log("Audio ended");
          setIsPlaying(false);
          setCurrentTime(0);
          if (onEnded) onEnded();
        };

        const handleTimeUpdate = () => {
          setCurrentTime(audio.currentTime);
        };

        const handleLoadedMetadata = () => {
          console.log("Audio metadata loaded, duration:", audio.duration);
          setDuration(audio.duration);
          setIsLoading(false);
        };

        const handleCanPlay = () => {
          console.log("Audio can play");
          setIsLoading(false);
        };

        const handleError = () => {
          console.error("Audio error:", audio.error?.message || "Unknown error");
          setError(`Error: ${audio.error?.message || "Unknown error"}`);
          setIsLoading(false);
        };

        // Add event listeners
        audio.addEventListener('play', handlePlay);
        audio.addEventListener('pause', handlePause);
        audio.addEventListener('ended', handleEnded);
        audio.addEventListener('timeupdate', handleTimeUpdate);
        audio.addEventListener('loadedmetadata', handleLoadedMetadata);
        audio.addEventListener('canplay', handleCanPlay);
        audio.addEventListener('error', handleError);

        // Store event handlers for cleanup
        (audio as any).__eventHandlers = {
          play: handlePlay,
          pause: handlePause,
          ended: handleEnded,
          timeupdate: handleTimeUpdate,
          loadedmetadata: handleLoadedMetadata,
          canplay: handleCanPlay,
          error: handleError
        };
      }
    }

    // Set audio source
    setAudioSource();

    // Auto-play if specified
    if (autoPlay) {
      setTimeout(() => {
        if (audioRef.current) {
          audioRef.current.play().catch(e => {
            console.error("Error auto-playing:", e);
          });
        }
      }, 500);
    }

    // Clean up on unmount
    return () => {
      const audio = audioRef.current;
      if (audio) {
        // Remove event listeners
        if ((audio as any).__eventHandlers) {
          const handlers = (audio as any).__eventHandlers;
          Object.entries(handlers).forEach(([event, handler]) => {
            audio.removeEventListener(event, handler as EventListener);
          });
        }

        // Stop playback
        audio.pause();
        audio.src = '';
        audio.load();
      }

      // Cancel animation frame
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
        rafRef.current = null;
      }

      // Revoke blob URL
      if ((window as any).__currentBlobUrl) {
        URL.revokeObjectURL((window as any).__currentBlobUrl);
        (window as any).__currentBlobUrl = null;
      }
    };
  }, [autoPlay, onEnded]);

  // Set audio source
  const setAudioSource = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) return false;

    setIsLoading(true);
    setError(null);

    console.log("Setting audio source with available options:", {
      mixedBlob: mixedBlob ? `${mixedBlob.size} bytes` : 'missing',
      mixedUrl: mixedUrl || 'missing',
      recordingBlob: recordingBlob ? `${recordingBlob.size} bytes` : 'missing'
    });

    // Try sources in order of preference

    // 1. Try mixedBlob first (most reliable)
    if (mixedBlob && mixedBlob.size > 0) {
      try {
        // Revoke any existing blob URL
        if ((window as any).__currentBlobUrl) {
          URL.revokeObjectURL((window as any).__currentBlobUrl);
        }

        // Create new blob URL
        const blobUrl = URL.createObjectURL(mixedBlob);

        // Set up a promise to check if the audio loads successfully
        const loadPromise = new Promise<boolean>((resolve) => {
          if (!audio) {
            resolve(false);
            return;
          }

          const handleCanPlay = () => {
            console.log("Audio can play from mixedBlob");
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(true);
          };

          const handleError = () => {
            console.error("Error loading audio from mixedBlob:", audio.error);
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(false);
          };

          audio.addEventListener('canplay', handleCanPlay);
          audio.addEventListener('error', handleError);

          // Set a timeout in case the events don't fire
          setTimeout(() => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            if (audio.readyState >= 3) {
              console.log("Audio loaded from mixedBlob (timeout)");
              resolve(true);
            } else {
              console.warn("Audio failed to load from mixedBlob (timeout)");
              resolve(false);
            }
          }, 5000);
        });

        // Set the source and load
        audio.src = blobUrl;

        // Store for cleanup
        (window as any).__currentBlobUrl = blobUrl;

        console.log("Set audio source from mixedBlob");
        audio.load();

        // Wait for the audio to load or fail
        const success = await loadPromise;
        if (success) {
          return true;
        }

        console.warn("Failed to load audio from mixedBlob, trying next source");
      } catch (e) {
        console.error("Error setting source from mixedBlob:", e);
      }
    }

    // 2. Try recordingBlob next
    if (recordingBlob && recordingBlob.size > 0) {
      try {
        // Revoke any existing blob URL
        if ((window as any).__currentBlobUrl) {
          URL.revokeObjectURL((window as any).__currentBlobUrl);
        }

        // Create new blob URL
        const blobUrl = URL.createObjectURL(recordingBlob);

        // Set up a promise to check if the audio loads successfully
        const loadPromise = new Promise<boolean>((resolve) => {
          if (!audio) {
            resolve(false);
            return;
          }

          const handleCanPlay = () => {
            console.log("Audio can play from recordingBlob");
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(true);
          };

          const handleError = () => {
            console.error("Error loading audio from recordingBlob:", audio.error);
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(false);
          };

          audio.addEventListener('canplay', handleCanPlay);
          audio.addEventListener('error', handleError);

          // Set a timeout in case the events don't fire
          setTimeout(() => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            if (audio.readyState >= 3) {
              console.log("Audio loaded from recordingBlob (timeout)");
              resolve(true);
            } else {
              console.warn("Audio failed to load from recordingBlob (timeout)");
              resolve(false);
            }
          }, 5000);
        });

        // Set the source and load
        audio.src = blobUrl;

        // Store for cleanup
        (window as any).__currentBlobUrl = blobUrl;

        console.log("Set audio source from recordingBlob");
        audio.load();

        // Wait for the audio to load or fail
        const success = await loadPromise;
        if (success) {
          return true;
        }

        console.warn("Failed to load audio from recordingBlob, trying next source");
      } catch (e) {
        console.error("Error setting source from recordingBlob:", e);
      }
    }

    // 3. Try mixedUrl next
    if (mixedUrl) {
      try {
        // Set up a promise to check if the audio loads successfully
        const loadPromise = new Promise<boolean>((resolve) => {
          if (!audio) {
            resolve(false);
            return;
          }

          const handleCanPlay = () => {
            console.log("Audio can play from mixedUrl");
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(true);
          };

          const handleError = () => {
            console.error("Error loading audio from mixedUrl:", audio.error);
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(false);
          };

          audio.addEventListener('canplay', handleCanPlay);
          audio.addEventListener('error', handleError);

          // Set a timeout in case the events don't fire
          setTimeout(() => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            if (audio.readyState >= 3) {
              console.log("Audio loaded from mixedUrl (timeout)");
              resolve(true);
            } else {
              console.warn("Audio failed to load from mixedUrl (timeout)");
              resolve(false);
            }
          }, 5000);
        });

        // Set the source and load
        audio.src = mixedUrl;

        console.log("Set audio source from mixedUrl");
        audio.load();

        // Wait for the audio to load or fail
        const success = await loadPromise;
        if (success) {
          return true;
        }

        console.warn("Failed to load audio from mixedUrl, trying next source");
      } catch (e) {
        console.error("Error setting source from mixedUrl:", e);
      }
    }

    // 4. Try window.__mixedRecordingUrl as last resort
    if ((window as any).__mixedRecordingUrl) {
      try {
        // Set up a promise to check if the audio loads successfully
        const loadPromise = new Promise<boolean>((resolve) => {
          if (!audio) {
            resolve(false);
            return;
          }

          const handleCanPlay = () => {
            console.log("Audio can play from window.__mixedRecordingUrl");
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(true);
          };

          const handleError = () => {
            console.error("Error loading audio from window.__mixedRecordingUrl:", audio.error);
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            resolve(false);
          };

          audio.addEventListener('canplay', handleCanPlay);
          audio.addEventListener('error', handleError);

          // Set a timeout in case the events don't fire
          setTimeout(() => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            if (audio.readyState >= 3) {
              console.log("Audio loaded from window.__mixedRecordingUrl (timeout)");
              resolve(true);
            } else {
              console.warn("Audio failed to load from window.__mixedRecordingUrl (timeout)");
              resolve(false);
            }
          }, 5000);
        });

        // Set the source and load
        audio.src = (window as any).__mixedRecordingUrl;

        console.log("Set audio source from window.__mixedRecordingUrl");
        audio.load();

        // Wait for the audio to load or fail
        const success = await loadPromise;
        if (success) {
          return true;
        }

        console.warn("Failed to load audio from window.__mixedRecordingUrl");
      } catch (e) {
        console.error("Error setting source from window.__mixedRecordingUrl:", e);
      }
    }

    // If we get here, all sources failed
    console.error("No valid audio source available");
    setError("No audio source available");
    setIsLoading(false);
    return false;
  }, [mixedBlob, mixedUrl, recordingBlob]);

  // Toggle play/pause
  const togglePlayPause = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) {
      setError("Audio player not initialized");
      return;
    }

    try {
      if (isPlaying) {
        // Pause
        audio.pause();
        setIsPlaying(false);
      } else {
        // Play
        setIsLoading(true);

        // Ensure we have a source
        if (!audio.src || audio.src === 'about:blank' || audio.error) {
          console.log("No valid source, setting audio source");
          const sourceSet = await setAudioSource();
          if (!sourceSet) {
            console.error("Failed to set audio source");
            setError("Failed to load audio. Please try again.");
            setIsLoading(false);
            return;
          }
        }

        // Check if the audio is ready to play
        if (audio.readyState < 3) {
          console.log("Audio not ready to play, waiting for canplay event");

          // Wait for the audio to be ready
          await new Promise<void>((resolve, reject) => {
            const handleCanPlay = () => {
              console.log("Audio can play now");
              audio.removeEventListener('canplay', handleCanPlay);
              audio.removeEventListener('error', handleError);
              resolve();
            };

            const handleError = () => {
              console.error("Error loading audio:", audio.error);
              audio.removeEventListener('canplay', handleCanPlay);
              audio.removeEventListener('error', handleError);
              reject(new Error(`Audio error: ${audio.error?.message || 'Unknown error'}`));
            };

            audio.addEventListener('canplay', handleCanPlay);
            audio.addEventListener('error', handleError);

            // Set a timeout in case the events don't fire
            setTimeout(() => {
              audio.removeEventListener('canplay', handleCanPlay);
              audio.removeEventListener('error', handleError);

              if (audio.readyState >= 3) {
                console.log("Audio ready to play (timeout)");
                resolve();
              } else {
                console.warn("Audio not ready to play after timeout");
                reject(new Error("Audio not ready to play after timeout"));
              }
            }, 5000);
          }).catch(error => {
            console.error("Error waiting for audio to be ready:", error);
            throw error;
          });
        }

        // Play
        try {
          console.log("Attempting to play audio");
          await audio.play();
          console.log("Audio playing successfully");
          setIsPlaying(true);
        } catch (e) {
          console.error("Error playing audio:", e);
          setError("Error playing audio. Please try again.");

          // Try to set the audio source again
          console.log("Trying to set audio source again after play error");
          await setAudioSource();
        } finally {
          setIsLoading(false);
        }
      }
    } catch (e) {
      console.error("Error toggling playback:", e);
      setError("Playback error");
      setIsLoading(false);
    }
  }, [isPlaying, setAudioSource]);

  // Handle seeking
  const handleSeek = useCallback((time: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = time;
    setCurrentTime(time);
  }, []);

  // Return the hook API
  return {
    isPlaying,
    isLoading,
    currentTime,
    duration,
    error,
    togglePlayPause,
    handleSeek
  };
}
