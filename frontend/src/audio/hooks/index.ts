/**
 * Audio Hooks
 *
 * This module exports all audio-related hooks for the freestyle app.
 *
 * The primary hooks for audio processing are:
 * - useAudioWorker: Direct access to the Web Worker for audio processing
 * - useSyncAudioRecording: Recording with precise synchronization
 * - useEnhancedAudioPlayback: Advanced audio playback with waveform visualization
 */

// Core hooks
export { useWaveform } from './useWaveform';
export type { UseWaveformOptions } from './useWaveform';

// Web Worker-based hooks (primary implementation)
export { useAudioWorker } from './useAudioWorker';
export type { UseAudioWorkerOptions, UseAudioWorkerReturn } from './useAudioWorker';

// Recording and playback hooks
export { useEnhancedAudioPlayback } from './useEnhancedAudioPlayback';
export { useSyncAudioRecording } from './useSyncAudioRecording';
export type { UseSyncAudioRecordingOptions, UseSyncAudioRecordingReturn } from './useSyncAudioRecording';
