/**
 * useSyncAudioRecording Hook
 *
 * A custom hook that provides synchronized audio recording functionality.
 * This hook encapsulates all the logic for:
 * - Recording audio with precise timing
 * - Synchronizing with beat playback
 * - Processing the recording for preview
 * - Managing recording state
 *
 * By moving this logic to a hook, we can reuse it across components
 * and keep the components focused on rendering.
 */

import { useState, useRef, useEffect, useCallback } from 'react';
import { useAudioStore } from '../../stores/audioStore';
import { useBeatStore } from '../../stores/beatStore';
import { SyncAudioRecorder } from '../services/syncAudioRecorder';
import {
  initAudioWorker,
  processRecordingForPreview as workerProcessRecording
} from '../services/audioWorkerService';

// Define hook options
export interface UseSyncAudioRecordingOptions {
  onRecordingComplete?: (result: any) => void;
}

// Define hook return type
export interface UseSyncAudioRecordingReturn {
  error: string | null;
  transportRef: React.RefObject<any>;
  handleStartRecording: () => Promise<void>;
  handleStopRecording: () => Promise<void>;
  level: number;
}

// Main hook function
export function useSyncAudioRecording(options: UseSyncAudioRecordingOptions = {}): UseSyncAudioRecordingReturn {
  // Destructure options
  const { onRecordingComplete } = options;

  // State
  const [error, setError] = useState<string | null>(null);
  const [level, setLevel] = useState<number>(0);

  // Refs
  const recorderRef = useRef<SyncAudioRecorder | null>(null);
  const beatAudioRef = useRef<HTMLAudioElement | null>(null);
  const transportRef = useRef<any>(null);
  const workerApiRef = useRef<any>(null);
  const recordingBlobRef = useRef<Blob | null>(null);

  // Get state from stores
  const {
    recordingState,
    setRecordingState,
    gainPercent,
    selectedInput
  } = useAudioStore();

  const {
    currentBeat,
    beatVolume
  } = useBeatStore();

  // Get current beat URL
  const currentBeatUrl = currentBeat?.audio_url ||
                        (currentBeat?.attributes && currentBeat.attributes.audio_url) ||
                        null;

  // Initialize worker
  useEffect(() => {
    const initWorker = async () => {
      try {
        // Initialize the audio worker service
        const workerApi = await initAudioWorker();
        workerApiRef.current = workerApi;

        console.log("Audio processor worker initialized in hook");
      } catch (error) {
        console.error("Error initializing audio processor worker:", error);
        setError("Failed to initialize audio processor");
      }
    };

    initWorker();

    // No need to clean up - the worker service handles termination
  }, []);

  // Initialize audio elements
  useEffect(() => {
    // Create beat audio element
    if (!beatAudioRef.current) {
      beatAudioRef.current = new Audio();
      beatAudioRef.current.preload = 'auto';
    }

    // Create recorder
    if (!recorderRef.current) {
      recorderRef.current = new SyncAudioRecorder({
        deviceId: selectedInput,
        onStateChange: (state) => {
          console.log("Recorder state changed:", state);
          setRecordingState(state);
        },
        onError: (errorMsg) => {
          console.error("Recorder error:", errorMsg);
          setError(errorMsg);
        },
        onLevelChange: (newLevel) => {
          setLevel(newLevel);
        }
      });
    }

    // Clean up on unmount
    return () => {
      if (recorderRef.current) {
        recorderRef.current.dispose();
      }

      if (beatAudioRef.current) {
        beatAudioRef.current.pause();
        beatAudioRef.current.src = '';
      }
    };
  }, [selectedInput, setRecordingState]);

  // Update recorder gain when gainPercent changes
  useEffect(() => {
    if (recorderRef.current) {
      const gainValue = gainPercent / 100;
      recorderRef.current.setGain(gainValue);
    }
  }, [gainPercent]);

  // Update beat audio element when currentBeatUrl changes
  useEffect(() => {
    if (beatAudioRef.current && currentBeatUrl) {
      beatAudioRef.current.src = currentBeatUrl;
      beatAudioRef.current.volume = beatVolume;
      beatAudioRef.current.load();
    }
  }, [currentBeatUrl, beatVolume]);

  // Start recording
  const startRecording = useCallback(async (beatUrl?: string | null) => {
    if (!recorderRef.current) {
      setError("Recorder not initialized");
      return;
    }

    try {
      console.log("Starting recording with beat URL:", beatUrl);

      // Store beat URL in global space for redundancy
      (window as any).__currentBeatUrl = beatUrl;

      // Start recording
      await recorderRef.current.start();

      console.log("Recording started successfully");
    } catch (error: any) {
      console.error("Error starting recording:", error);
      setError(error?.message || "Failed to start recording");
    }
  }, []);

  // Stop recording
  const stopRecording = useCallback(async () => {
    if (!recorderRef.current) {
      setError("Recorder not initialized");
      return null;
    }

    try {
      console.log("Stopping recording");

      // Stop recording
      const result = await recorderRef.current.stop();

      if (!result) {
        console.error("No recording result returned");
        return null;
      }

      console.log("Recording stopped successfully:", {
        duration: result.duration,
        mimeType: result.mimeType,
        blobSize: result.blob.size
      });

      // Store the blob in our ref
      recordingBlobRef.current = result.blob;

      // Store the URL in global space for redundancy
      (window as any).__currentRecordingUrl = result.url;

      return result;
    } catch (error: any) {
      console.error("Error stopping recording:", error);
      setError(error?.message || "Failed to stop recording");
      return null;
    }
  }, []);

  // Start beat playback
  const startBeat = useCallback(async () => {
    if (beatAudioRef.current && currentBeatUrl) {
      try {
        if (beatAudioRef.current.paused) {
          // Reset to beginning
          beatAudioRef.current.currentTime = 0;

          // Ensure volume is set correctly before playing
          beatAudioRef.current.volume = beatVolume;

          // Set audio output to headphones if possible to reduce feedback
          if (beatAudioRef.current.setSinkId) {
            try {
              // Try to use the Audio Output Devices API if available
              // This is a newer API and might not be available in all browsers
              // @ts-ignore - TypeScript doesn't know about this API yet
              if (navigator.mediaDevices.selectAudioOutput) {
                try {
                  // @ts-ignore - TypeScript doesn't know about this API yet
                  const audioDevice = await navigator.mediaDevices.selectAudioOutput({
                    audioOutputSelector: 'headphones'
                  });

                  if (audioDevice && audioDevice.deviceId) {
                    await beatAudioRef.current.setSinkId(audioDevice.deviceId);
                    console.log("Set beat audio output to selected device:", audioDevice.label);
                  }
                } catch (selectErr) {
                  console.log("Could not select audio output device:", selectErr);
                }
              } else {
                // Fallback to using the default audio output
                console.log("Audio output selection API not available");
              }
            } catch (err) {
              // This is optional, so just log the error
              console.log("Could not set audio output device:", err);
            }
          }

          // Play the beat
          const playPromise = beatAudioRef.current.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log("Beat started successfully at volume:", beatVolume);
              })
              .catch(error => {
                console.error("Error in beat play promise:", error);
              });
          }
        } else {
          console.log("Beat is already playing, not starting again");
        }
      } catch (error) {
        console.error("Error starting beat:", error);
      }
    } else {
      console.log("No beat audio element or URL available");
    }
  }, [currentBeatUrl, beatVolume]);

  // Stop beat playback
  const stopBeat = useCallback(() => {
    if (beatAudioRef.current) {
      try {
        beatAudioRef.current.pause();
        console.log("Beat stopped");
      } catch (error) {
        console.error("Error stopping beat:", error);
      }
    }
  }, []);

  // Process recording for preview
  const processRecordingForPreview = useCallback(async (
    recordingBlob: Blob,
    beatUrl: string,
    recordingGain: number,
    beatGain: number
  ) => {
    try {
      console.log("Processing recording for preview with worker service");

      // Use the worker service to process the recording
      const result = await workerProcessRecording(
        recordingBlob,
        beatUrl,
        recordingGain,
        beatGain,
        {
          applyCompression: true,
          compressionThreshold: 0.5,
          compressionRatio: 4
          // Optional effects can be enabled in the worker service
        }
      );

      console.log("Worker processing complete:", result);

      return result;
    } catch (error) {
      console.error("Error processing recording for preview:", error);
      throw error;
    }
  }, []);

  // Handle start recording
  const handleStartRecording = useCallback(async () => {
    try {
      if (recordingState !== 'idle' &&
          recordingState !== 'stopped' &&
          recordingState !== 'error') {
        console.log("Cannot start recording in current state:", recordingState);
        return;
      }

      setError(null);

      // Start recording and beat playback almost simultaneously
      console.log("Starting recording and beat playback");

      // Start recording first
      await startRecording(currentBeatUrl);

      // Start beat playback immediately after
      await startBeat();

      console.log("Recording and beat playback started successfully");
    } catch (error: any) {
      console.error('Error starting recording:', error);
      setError(error?.message || 'Failed to start recording');

      // Reset state to allow trying again
      useAudioStore.setState({ recordingState: 'idle' });
    }
  }, [recordingState, startRecording, currentBeatUrl, startBeat]);

  // Handle stop recording
  const handleStopRecording = useCallback(async () => {
    try {
      if (recordingState !== 'recording') {
        console.log("Cannot stop recording in current state:", recordingState);
        return;
      }

      // Stop beat first
      stopBeat();

      // Then stop recording
      const result = await stopRecording();

      if (!result) {
        console.error("No recording result returned");
        return;
      }

      const { blob, url, duration } = result;

      // Store the blob in our ref
      recordingBlobRef.current = blob;

      console.log("Recording stopped successfully, blob size:", blob.size);

      // Store the URL in global space for redundancy
      (window as any).__currentRecordingUrl = url;
      (window as any).__mixedRecordingUrl = url;

      // Update the audio store
      useAudioStore.setState({
        recordingUrl: url,
        recordingBlob: blob,
        duration,
        recordingState: 'stopped'
      });

      // Process recording for preview if we have a beat
      if (currentBeatUrl) {
        try {
          console.log("Processing recording for preview...");

          // Get the microphone gain value from audioStore (convert from percentage to decimal)
          const micGain = gainPercent / 100;

          // Process the recording synchronously to ensure we complete before navigating
          try {
            console.log("Processing recording for preview synchronously...");

            // Process the recording
            const result = await processRecordingForPreview(blob, currentBeatUrl, micGain, beatVolume);
            const { mixedBuffer, mixedBlob, duration } = result;

            // Create a URL from the blob
            const mixedUrl = URL.createObjectURL(mixedBlob);

            console.log("Successfully processed recording for preview");

            // Create an AudioBuffer from the transferable object if needed
            let actualMixedBuffer = mixedBuffer;

            // Check if we received a simplified object instead of an actual AudioBuffer
            if (mixedBuffer && typeof mixedBuffer === 'object' &&
                !mixedBuffer.getChannelData && mixedBuffer.sampleRate) {
              console.log("Received simplified buffer object, creating AudioBuffer");

              // Create a new AudioBuffer with the properties from the simplified object
              const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
              actualMixedBuffer = audioContext.createBuffer(
                mixedBuffer.numberOfChannels || 2,
                mixedBuffer.length || 0,
                mixedBuffer.sampleRate || 44100
              );

              // We don't have the actual audio data, but we have the WAV blob
              // which is what we'll actually use for playback
              console.log("Created empty AudioBuffer as placeholder");
            }

            // Store the mixed buffer in the audio store
            useAudioStore.setState({
              mixedBuffer: actualMixedBuffer,
              mixedUrl,
              mixedBlob,
              duration,
              // Ensure recordingUrl is also set if it wasn't already
              recordingUrl: useAudioStore.getState().recordingUrl || mixedUrl
            });

            // Log what we're storing
            console.log("Storing processed recording with URLs:", {
              mixedUrl,
              recordingUrl: useAudioStore.getState().recordingUrl || mixedUrl,
              mixedBlobSize: mixedBlob ? mixedBlob.size : 0,
              duration
            });

            // Store in window for redundancy
            (window as any).__mixedRecordingBuffer = actualMixedBuffer;
            (window as any).__mixedRecordingUrl = mixedUrl;
            (window as any).__mixedDuration = duration;
            (window as any).__mixedBlob = mixedBlob;

            // Also store the recordingUrl as a fallback
            if (!useAudioStore.getState().recordingUrl) {
              (window as any).__currentRecordingUrl = mixedUrl;
            }

            // Call the callback
            if (onRecordingComplete) {
              console.log("Calling onRecordingComplete with mixed recording");
              onRecordingComplete({
                mixedBuffer,
                mixedUrl,
                mixedBlob,
                duration
              });
            }
          } catch (processingError) {
            console.error("Error processing recording for preview:", processingError);

            // Still call the callback with the original recording
            if (onRecordingComplete) {
              console.log("Calling onRecordingComplete with original recording due to processing error");
              onRecordingComplete({
                recordingBlob: blob,
                recordingUrl: url,
                duration
              });
            }
          }
        } catch (error) {
          console.error("Error initiating recording processing:", error);

          // Call the callback with the original recording
          if (onRecordingComplete) {
            onRecordingComplete({
              recordingBlob: blob,
              recordingUrl: url,
              duration
            });
          }
        }
      } else {
        // Call the callback with the original recording
        if (onRecordingComplete) {
          onRecordingComplete({
            recordingBlob: blob,
            recordingUrl: url,
            duration
          });
        }
      }
    } catch (error: any) {
      console.error('Error stopping recording:', error);
      setError(error?.message || 'Error stopping recording');

      // Reset state to allow trying again
      useAudioStore.setState({ recordingState: 'idle' });
    }
  }, [recordingState, stopRecording, stopBeat, currentBeatUrl, onRecordingComplete, gainPercent, beatVolume, processRecordingForPreview]);

  // Return the hook API
  return {
    error,
    transportRef,
    handleStartRecording,
    handleStopRecording,
    level
  };
}
