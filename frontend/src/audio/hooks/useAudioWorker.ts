/**
 * useAudioWorker Hook
 * 
 * A custom hook that provides direct access to the audio processing Web Worker.
 * This hook encapsulates all the logic for:
 * - Initializing and accessing the Web Worker
 * - Processing audio with various effects
 * - Generating waveforms for visualization
 * - Analyzing audio levels
 * 
 * By moving this logic to a hook, we can reuse it across components
 * and keep the components focused on rendering.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  initAudioWorker, 
  getAudioWorker, 
  AudioProcessorWorker 
} from '../services/audioWorkerService';
import type { Remote } from 'comlink';

export interface UseAudioWorkerOptions {
  autoInit?: boolean;
}

export interface UseAudioWorkerReturn {
  worker: Remote<AudioProcessorWorker> | null;
  isInitialized: boolean;
  error: string | null;
  
  // High-level operations
  processRecordingForPreview: (
    recordingBlob: Blob,
    beatUrl: string,
    recordingGain?: number,
    beatGain?: number,
    options?: {
      applyCompression?: boolean;
      compressionThreshold?: number;
      compressionRatio?: number;
    }
  ) => Promise<{
    mixedBuffer: AudioBuffer;
    mixedBlob: Blob;
    duration: number;
    waveformData: number[][];
    levels: {rms: number, db: number, peak: number};
  }>;
  
  generateWaveform: (
    audioBuffer: AudioBuffer,
    numPoints?: number
  ) => Promise<number[][]>;
  
  analyzeAudioLevel: (
    audioBuffer: AudioBuffer
  ) => Promise<{rms: number, db: number, peak: number}>;
  
  exportRecording: (
    recordingBlob: Blob,
    beatUrl: string,
    metadata?: {
      title: string;
      artist: string;
      beatTitle: string;
      beatProducer: string;
      date: string;
    },
    options?: {
      recordingGain: number;
      beatGain: number;
      format: string;
      applyCompression: boolean;
      compressionThreshold: number;
      compressionRatio: number;
    }
  ) => Promise<{
    blob: Blob;
    duration: number;
    metadata: any;
  }>;
}

export function useAudioWorker(options: UseAudioWorkerOptions = {}): UseAudioWorkerReturn {
  const { autoInit = true } = options;
  
  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Refs
  const workerRef = useRef<Remote<AudioProcessorWorker> | null>(null);
  
  // Initialize worker
  useEffect(() => {
    if (autoInit) {
      const init = async () => {
        try {
          const worker = await initAudioWorker();
          workerRef.current = worker;
          setIsInitialized(true);
        } catch (err: any) {
          console.error("Failed to initialize audio worker:", err);
          setError(err?.message || "Failed to initialize audio processing");
        }
      };
      
      init();
    }
  }, [autoInit]);
  
  // Process recording for preview
  const processRecordingForPreview = useCallback(async (
    recordingBlob: Blob,
    beatUrl: string,
    recordingGain: number = 1.0,
    beatGain: number = 0.8,
    options = {
      applyCompression: true,
      compressionThreshold: 0.5,
      compressionRatio: 4
    }
  ) => {
    try {
      // Get worker
      const worker = workerRef.current || await getAudioWorker();
      
      // Convert recording blob to array buffer
      const recordingArrayBuffer = await recordingBlob.arrayBuffer();
      
      // Fetch beat
      const beatResponse = await fetch(beatUrl);
      const beatArrayBuffer = await beatResponse.arrayBuffer();
      
      // Process with worker
      return await worker.processRecordingForPreview(
        recordingArrayBuffer,
        beatArrayBuffer,
        recordingGain,
        beatGain,
        options
      );
    } catch (error: any) {
      console.error("Error processing recording:", error);
      setError(error?.message || "Error processing recording");
      throw error;
    }
  }, []);
  
  // Generate waveform
  const generateWaveform = useCallback(async (
    audioBuffer: AudioBuffer,
    numPoints: number = 1000
  ) => {
    try {
      // Get worker
      const worker = workerRef.current || await getAudioWorker();
      
      // Generate waveform
      return await worker.generateWaveform(audioBuffer, numPoints);
    } catch (error: any) {
      console.error("Error generating waveform:", error);
      setError(error?.message || "Error generating waveform");
      throw error;
    }
  }, []);
  
  // Analyze audio level
  const analyzeAudioLevel = useCallback(async (
    audioBuffer: AudioBuffer
  ) => {
    try {
      // Get worker
      const worker = workerRef.current || await getAudioWorker();
      
      // Analyze audio
      return await worker.analyzeAudioLevel(audioBuffer);
    } catch (error: any) {
      console.error("Error analyzing audio level:", error);
      setError(error?.message || "Error analyzing audio");
      throw error;
    }
  }, []);
  
  // Export recording
  const exportRecording = useCallback(async (
    recordingBlob: Blob,
    beatUrl: string,
    metadata = {
      title: "Freestyle Recording",
      artist: "Freestyle App User",
      beatTitle: "Unknown Beat",
      beatProducer: "Unknown Producer",
      date: new Date().toISOString()
    },
    options = {
      recordingGain: 1.0,
      beatGain: 0.8,
      format: "wav",
      applyCompression: true,
      compressionThreshold: 0.5,
      compressionRatio: 4
    }
  ) => {
    try {
      // Get worker
      const worker = workerRef.current || await getAudioWorker();
      
      // Convert recording blob to array buffer
      const recordingArrayBuffer = await recordingBlob.arrayBuffer();
      
      // Fetch beat
      const beatResponse = await fetch(beatUrl);
      const beatArrayBuffer = await beatResponse.arrayBuffer();
      
      // Process with worker
      return await worker.exportRecording(
        recordingArrayBuffer,
        beatArrayBuffer,
        metadata,
        options
      );
    } catch (error: any) {
      console.error("Error exporting recording:", error);
      setError(error?.message || "Error exporting recording");
      throw error;
    }
  }, []);
  
  return {
    worker: workerRef.current,
    isInitialized,
    error,
    processRecordingForPreview,
    generateWaveform,
    analyzeAudioLevel,
    exportRecording
  };
}
