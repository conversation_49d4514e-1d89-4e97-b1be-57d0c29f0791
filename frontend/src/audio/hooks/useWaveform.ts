/**
 * useWaveform Hook
 *
 * A custom hook that provides waveform visualization functionality.
 * This hook encapsulates all the logic for:
 * - Processing audio data to generate waveform peaks
 * - Handling responsive sizing
 * - Managing canvas drawing
 * - Handling user interactions (seeking)
 *
 * By moving this logic to a hook, we can reuse it across components
 * and keep the components focused on rendering.
 */

import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useBeatStore } from '../../stores/beatStore';
import {
  generateDummyPeaks,
  generatePeaksFromBuffer,
  generatePeaksFromUrl
} from '../utils/waveformGenerator';

export interface UseWaveformOptions {
  audioUrl?: string | Blob | null;
  audioBuffer?: AudioBuffer | null;
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onSeek?: (time: number) => void;
  height?: number;
  gradientColors?: string[];
  waveformKey?: string;
  precomputedPeaks?: number[][];
}

export function useWaveform(options: UseWaveformOptions) {
  const {
    audioUrl,
    audioBuffer,
    currentTime,
    duration,
    // isPlaying, // Not used but kept for API consistency
    onSeek,
    // height = 100, // Not used but kept for API consistency
    gradientColors,
    waveformKey,
    precomputedPeaks
  } = options;

  // Refs for canvas elements
  const waveformCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const playheadCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  // State for peaks data
  const [peaks, setPeaks] = useState<Float32Array | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [width, setWidth] = useState(1000); // Default width that will be updated on mount

  // Generate a unique key for this waveform instance
  const effectKey = useMemo(() =>
    waveformKey || `waveform-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
  [waveformKey]);

  // Default gradient colors
  const defaultGradientColors = useMemo(() =>
    ["#5f6fff", "#7a6fff", "#b367ff", "#d45fff", "#ff5f7e", "#ff7a5f"],
  []);

  // Use provided gradient colors or default
  const extendedGradientColors = useMemo(() =>
    gradientColors && gradientColors.length >= 2 ? gradientColors : defaultGradientColors,
  [gradientColors, defaultGradientColors]);

  // Responsive width using ResizeObserver
  useEffect(() => {
    // Skip during server-side rendering
    if (typeof window === 'undefined') return;

    // Get the parent container of the canvas
    const container = containerRef.current;
    if (!container) return;

    // Set initial width
    setWidth(container.clientWidth);

    // Use ResizeObserver for accurate container size tracking
    let resizeObserver: ResizeObserver | null = null;

    try {
      resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const containerWidth = entry.contentRect.width;
          if (containerWidth > 0) {
            setWidth(containerWidth);
          }
        }
      });

      // Start observing the container
      resizeObserver.observe(container);
    } catch (error) {
      console.warn("ResizeObserver not supported, falling back to window resize event", error);
    }

    // Also handle window resize as a fallback
    const handleResize = () => {
      if (container && container.clientWidth > 0) {
        setWidth(container.clientWidth);
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Generate waveform peaks
  useEffect(() => {
    // Skip if we're in the initial render with no container
    if (!containerRef.current) return;

    // If we have precomputed peaks, use them directly
    if (precomputedPeaks && precomputedPeaks.length > 0) {
      console.log("Using precomputed waveform peaks:", precomputedPeaks.length, "points");

      // Convert the 2D array of [min, max] peaks to a Float32Array
      // by taking the max absolute value of each pair
      const flatPeaks = new Float32Array(precomputedPeaks.length);
      for (let i = 0; i < precomputedPeaks.length; i++) {
        const [min, max] = precomputedPeaks[i];
        flatPeaks[i] = Math.max(Math.abs(min), Math.abs(max));
      }

      setPeaks(flatPeaks);
      return;
    }

    // Create a cache key based on the current inputs
    const cacheKey = `${effectKey}-${audioBuffer?.length || 0}-${audioUrl || ''}-${width}`;

    // Check if we already have peaks for this audio source in the cache
    if (typeof window !== 'undefined' && (window as any).__waveformCache?.[cacheKey]) {
      console.log("Using cached waveform peaks for:", cacheKey);
      setPeaks((window as any).__waveformCache[cacheKey]);
      return;
    }

    // Check if we're already generating peaks for this audio source
    if (typeof window !== 'undefined' && (window as any).__generatingWaveform === cacheKey) {
      console.log("Already generating waveform for:", cacheKey);
      return;
    }

    // Mark that we're generating peaks for this audio source
    if (typeof window !== 'undefined') {
      (window as any).__generatingWaveform = cacheKey;
    }

    const generatePeaks = async () => {
      setLoading(true);
      setError(null);

      try {
        let newPeaks: Float32Array | null = null;

        // Always use high resolution for consistent quality
        const peakCount = Math.max(width * 20, 2000); // Ensure we have at least 2000 peaks for high quality

        // Common options for peak generation - consistent across all generation methods
        const peakOptions = {
          enhanceFactor: 0.7, // More aggressive enhancement for better visibility
          prioritizeVocals: false,
          addNoise: true,
          cacheKey
        };

        // First priority: Use provided AudioBuffer
        if (audioBuffer) {
          console.log("Generating peaks from provided AudioBuffer", {
            length: audioBuffer.length,
            duration: audioBuffer.duration,
            sampleRate: audioBuffer.sampleRate,
            numberOfChannels: audioBuffer.numberOfChannels
          });

          // Let the waveform generator handle validation
          newPeaks = generatePeaksFromBuffer(audioBuffer, peakCount, peakOptions);

          // If we got valid peaks, cache them
          if (newPeaks && newPeaks.length > 0) {
            // Check if the peaks are all dummy values
            let isDummyPeaks = true;
            for (let i = 0; i < Math.min(10, newPeaks.length); i++) {
              // Dummy peaks have a specific pattern we can detect
              if (Math.abs(newPeaks[i]) > 0.0001) {
                isDummyPeaks = false;
                break;
              }
            }

            if (!isDummyPeaks) {
              // Cache the result
              if (typeof window !== 'undefined') {
                if (!(window as any).__waveformCache) {
                  (window as any).__waveformCache = {};
                }
                (window as any).__waveformCache[cacheKey] = newPeaks;
                console.log("Cached waveform peaks for:", cacheKey);
              }
            } else {
              console.warn("AudioBuffer generated dummy peaks, will try alternative sources");
            }
          } else {
            console.warn("AudioBuffer contains all zeros or very small values, will try alternative sources");
          }
        }
        // Second priority: Use provided URL
        else if (audioUrl) {
          console.log("Generating peaks from URL:", audioUrl);
          newPeaks = await generatePeaksFromUrl(audioUrl, peakCount, peakOptions);
        }
        // Third priority: Use current beat from store
        else {
          const currentBeat = useBeatStore.getState().currentBeat;

          if (currentBeat && (currentBeat.audio_url || (currentBeat.attributes && currentBeat.attributes.audio_url))) {
            const beatUrl = currentBeat.audio_url || (currentBeat.attributes && currentBeat.attributes.audio_url) || '';
            console.log("Generating peaks from beat URL:", beatUrl);

            // Ensure the beat URL is absolute
            const absoluteBeatUrl = beatUrl.startsWith('http')
              ? beatUrl
              : new URL(beatUrl, window.location.origin).href;

            newPeaks = await generatePeaksFromUrl(absoluteBeatUrl, peakCount, peakOptions);
          }
        }

        // If we still don't have peaks, generate high-quality dummy peaks
        if (!newPeaks) {
          console.log("No audio source available, generating high-quality dummy peaks");
          newPeaks = generateDummyPeaks(peakCount, {
            enhanceFactor: 0.7,
            addNoise: true
          });
        }

        setPeaks(newPeaks);
      } catch (error) {
        console.error("Error generating waveform peaks:", error);
        setError("Error generating waveform");

        // Generate dummy peaks as fallback with same high resolution and parameters
        const peakCount = Math.max(width * 20, 2000);
        const dummyPeaks = generateDummyPeaks(peakCount, {
          enhanceFactor: 0.7,
          addNoise: true
        });
        setPeaks(dummyPeaks);
      } finally {
        setLoading(false);
        // Clear the generating flag
        if (typeof window !== 'undefined') {
          (window as any).__generatingWaveform = null;
        }
      }
    };

    generatePeaks();

    // Cleanup function to clear the generating flag when the component unmounts
    return () => {
      if (typeof window !== 'undefined') {
        (window as any).__generatingWaveform = null;
      }
    };
  }, [audioBuffer, audioUrl, effectKey, width, precomputedPeaks]);

  // Draw the waveform
  useEffect(() => {
    if (!peaks || !waveformCanvasRef.current) return;

    // Check if we've already drawn this waveform
    const peaksKey = `${peaks.length}-${effectKey}`;
    if (typeof window !== 'undefined' && (window as any).__lastDrawnWaveform === peaksKey) {
      console.log("Skipping redraw of already drawn waveform:", peaksKey);
      return;
    }

    // Mark that we're drawing this waveform
    if (typeof window !== 'undefined') {
      (window as any).__lastDrawnWaveform = peaksKey;
    }

    console.log("Drawing waveform with", peaks.length, "peaks");

    const canvas = waveformCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Get the actual canvas dimensions
    const dpr = window.devicePixelRatio || 1;
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;

    // Only resize the canvas if dimensions have changed
    if (canvas.width !== displayWidth * dpr || canvas.height !== displayHeight * dpr) {
      canvas.width = displayWidth * dpr;
      canvas.height = displayHeight * dpr;
      ctx.scale(dpr, dpr);
    } else {
      // Reset the transformation matrix
      ctx.setTransform(1, 0, 0, 1, 0, 0);
      ctx.scale(dpr, dpr);
    }

    // Clear the canvas
    ctx.clearRect(0, 0, displayWidth, displayHeight);

    // Create gradient
    const gradient = ctx.createLinearGradient(0, 0, displayWidth, 0);
    extendedGradientColors.forEach((color, index) => {
      gradient.addColorStop(index / (extendedGradientColors.length - 1), color);
    });

    // Draw the waveform
    const barWidth = displayWidth / peaks.length;
    const halfHeight = displayHeight / 2;

    ctx.fillStyle = gradient;
    ctx.beginPath();

    // Draw the top half of the waveform
    ctx.moveTo(0, halfHeight);

    // Draw the top half of the waveform
    for (let i = 0; i < peaks.length; i++) {
      const x = i * barWidth;
      // Scale to ensure the maximum peaks just touch the top/bottom
      // with a small 1px margin
      const y = halfHeight - (peaks[i] * (halfHeight - 1));
      ctx.lineTo(x, y);
    }

    // Draw the bottom half (mirror of top)
    ctx.lineTo(displayWidth, halfHeight);

    for (let i = peaks.length - 1; i >= 0; i--) {
      const x = i * barWidth;
      // Scale to ensure the maximum peaks just touch the top/bottom
      // with a small 1px margin
      const y = halfHeight + (peaks[i] * (halfHeight - 1));
      ctx.lineTo(x, y);
    }

    ctx.closePath();
    ctx.fill();

    // Return a cleanup function to ensure we don't leave stale references
    return () => {
      if (typeof window !== 'undefined') {
        // Don't clear the lastDrawnWaveform flag here, as we want to prevent redraws
        // even if the component remounts with the same data
      }
    };
  }, [peaks, extendedGradientColors, effectKey]);

  // Draw the playhead - with throttling to reduce unnecessary redraws
  useEffect(() => {
    if (!playheadCanvasRef.current) return;

    // Skip if duration is invalid
    if (!duration || duration <= 0) return;

    // Throttle playhead updates to reduce CPU usage
    // Only update every ~50ms (about 20fps) which is smooth enough for playhead
    const lastUpdateTime = (window as any).__lastPlayheadUpdate || 0;
    const now = Date.now();
    if (now - lastUpdateTime < 50 && (window as any).__lastPlayheadPosition) {
      return; // Skip this update if we updated recently
    }
    (window as any).__lastPlayheadUpdate = now;

    // Store the current position for comparison
    const progress = Math.min(currentTime / duration, 1);

    // Skip if position hasn't changed significantly
    if ((window as any).__lastPlayheadPosition === progress) {
      return;
    }
    (window as any).__lastPlayheadPosition = progress;

    const canvas = playheadCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Get the actual canvas dimensions
    const dpr = window.devicePixelRatio || 1;
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;

    // Only resize the canvas if dimensions have changed
    if (canvas.width !== displayWidth * dpr || canvas.height !== displayHeight * dpr) {
      canvas.width = displayWidth * dpr;
      canvas.height = displayHeight * dpr;
      ctx.scale(dpr, dpr);
    } else {
      // Reset the transformation matrix
      ctx.setTransform(1, 0, 0, 1, 0, 0);
      ctx.scale(dpr, dpr);
    }

    // Clear the canvas
    ctx.clearRect(0, 0, displayWidth, displayHeight);

    // Calculate the position of the playhead
    const playheadX = progress * displayWidth;

    // Draw the playhead
    ctx.beginPath();
    ctx.moveTo(playheadX, 0);
    ctx.lineTo(playheadX, displayHeight);
    ctx.strokeStyle = "#ffffff";
    ctx.lineWidth = 2;
    ctx.stroke();
  }, [currentTime, duration]);

  // Handle seek interaction
  const handleSeekClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current || !onSeek) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const width = rect.width;

    // Calculate the seek time
    const seekTime = (x / width) * duration;

    // Call the onSeek callback
    onSeek(seekTime);
  }, [duration, onSeek]);

  // Return the hook API
  return {
    waveformCanvasRef,
    playheadCanvasRef,
    containerRef,
    loading,
    error,
    handleSeekClick
  };
}
