/**
 * useEnhancedAudioPlayback Hook
 * 
 * A custom hook that provides enhanced audio playback functionality.
 * This hook encapsulates all the logic for:
 * - Creating and managing audio elements
 * - Playing and pausing audio with precise timing
 * - Tracking playback state and time
 * - Handling errors and recovery
 * - Seeking to specific times
 * - Providing precise synchronization
 * 
 * By moving this logic to a hook, we can reuse it across components
 * and keep the components focused on rendering.
 */

import { useState, useRef, useEffect, useCallback } from 'react';
import { useAudioStore } from '../../stores/audioStore';

export interface UseEnhancedAudioPlaybackOptions {
  autoPlay?: boolean;
  onEnded?: () => void;
  onTimeUpdate?: (time: number) => void;
  onDurationChange?: (duration: number) => void;
  onError?: (error: string) => void;
}

export function useEnhancedAudioPlayback(options: UseEnhancedAudioPlaybackOptions = {}) {
  // Destructure options with defaults
  const { 
    autoPlay = false, 
    onEnded, 
    onTimeUpdate, 
    onDurationChange,
    onError
  } = options;

  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [readyState, setReadyState] = useState(0);

  // Refs
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceNodeRef = useRef<MediaElementAudioSourceNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const analyserNodeRef = useRef<AnalyserNode | null>(null);
  const rafRef = useRef<number | null>(null);
  const lastPlayPromiseRef = useRef<Promise<void> | null>(null);

  // Get data from store
  const { mixedBlob, recordingBlob, mixedUrl, recordingUrl } = useAudioStore();

  // Initialize audio context and nodes
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current && audioRef.current) {
      try {
        // Create audio context
        audioContextRef.current = new AudioContext({
          latencyHint: 'interactive',
          sampleRate: 48000
        });

        // Create source node
        sourceNodeRef.current = audioContextRef.current.createMediaElementSource(audioRef.current);

        // Create gain node
        gainNodeRef.current = audioContextRef.current.createGain();
        gainNodeRef.current.gain.value = 1.0;

        // Create analyser node
        analyserNodeRef.current = audioContextRef.current.createAnalyser();
        analyserNodeRef.current.fftSize = 2048;

        // Connect nodes
        sourceNodeRef.current.connect(gainNodeRef.current);
        gainNodeRef.current.connect(analyserNodeRef.current);
        analyserNodeRef.current.connect(audioContextRef.current.destination);

        console.log("Audio context and nodes initialized");
      } catch (e) {
        console.error("Error initializing audio context:", e);
        setError("Error initializing audio system");
      }
    }
  }, []);

  // Initialize audio element
  useEffect(() => {
    console.log("Initializing audio playback");

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio();
      audioRef.current.preload = 'auto';

      // Add event listeners
      const audio = audioRef.current;

      const handleCanPlay = () => {
        console.log("Audio can play");
        setIsLoading(false);
        setReadyState(audio.readyState);
        
        // Auto-play if enabled
        if (autoPlay && audio.paused) {
          audio.play().catch(e => {
            console.error("Auto-play failed:", e);
          });
        }
      };

      const handleTimeUpdate = () => {
        const newTime = audio.currentTime;
        setCurrentTime(newTime);
        if (onTimeUpdate) onTimeUpdate(newTime);
      };

      const handleDurationChange = () => {
        const newDuration = audio.duration;
        if (isFinite(newDuration)) {
          setDuration(newDuration);
          if (onDurationChange) onDurationChange(newDuration);
        }
      };

      const handleEnded = () => {
        setIsPlaying(false);
        if (onEnded) onEnded();
      };

      const handleError = () => {
        const errorMessage = audio.error ? `Audio error: ${audio.error.message}` : "Unknown audio error";
        console.error(errorMessage);
        setError(errorMessage);
        setIsLoading(false);
        if (onError) onError(errorMessage);
      };

      const handlePlay = () => {
        setIsPlaying(true);
      };

      const handlePause = () => {
        setIsPlaying(false);
      };

      const handleLoadStart = () => {
        setIsLoading(true);
        setReadyState(audio.readyState);
      };

      const handleLoadedMetadata = () => {
        setReadyState(audio.readyState);
        handleDurationChange();
      };

      const handleLoadedData = () => {
        setReadyState(audio.readyState);
      };

      // Add event listeners
      audio.addEventListener('canplay', handleCanPlay);
      audio.addEventListener('timeupdate', handleTimeUpdate);
      audio.addEventListener('durationchange', handleDurationChange);
      audio.addEventListener('ended', handleEnded);
      audio.addEventListener('error', handleError);
      audio.addEventListener('play', handlePlay);
      audio.addEventListener('pause', handlePause);
      audio.addEventListener('loadstart', handleLoadStart);
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('loadeddata', handleLoadedData);

      // Initialize audio context
      initAudioContext();

      // Clean up on unmount
      return () => {
        // Cancel animation frame
        if (rafRef.current) {
          cancelAnimationFrame(rafRef.current);
        }

        // Remove event listeners
        audio.removeEventListener('canplay', handleCanPlay);
        audio.removeEventListener('timeupdate', handleTimeUpdate);
        audio.removeEventListener('durationchange', handleDurationChange);
        audio.removeEventListener('ended', handleEnded);
        audio.removeEventListener('error', handleError);
        audio.removeEventListener('play', handlePlay);
        audio.removeEventListener('pause', handlePause);
        audio.removeEventListener('loadstart', handleLoadStart);
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audio.removeEventListener('loadeddata', handleLoadedData);

        // Pause and reset audio
        audio.pause();
        audio.src = '';

        // Close audio context
        if (audioContextRef.current) {
          audioContextRef.current.close();
        }
      };
    }
  }, [autoPlay, onEnded, onTimeUpdate, onDurationChange, onError, initAudioContext]);

  // Set audio source
  const setAudioSource = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) return false;

    setIsLoading(true);
    setError(null);

    console.log("Setting audio source with available options:", {
      mixedBlob: mixedBlob ? `${mixedBlob.size} bytes` : 'missing',
      mixedUrl: mixedUrl || 'missing',
      recordingBlob: recordingBlob ? `${recordingBlob.size} bytes` : 'missing',
      recordingUrl: recordingUrl || 'missing'
    });

    // Try sources in order of preference

    // 1. Try mixedBlob first (most reliable)
    if (mixedBlob && mixedBlob.size > 0) {
      try {
        // Revoke any existing blob URL
        if ((window as any).__currentBlobUrl) {
          URL.revokeObjectURL((window as any).__currentBlobUrl);
        }

        // Create new blob URL
        const blobUrl = URL.createObjectURL(mixedBlob);
        (window as any).__currentBlobUrl = blobUrl;

        // Set the source and load
        audio.src = blobUrl;
        audio.load();

        console.log("Set audio source from mixedBlob");
        return true;
      } catch (e) {
        console.error("Error setting source from mixedBlob:", e);
      }
    }

    // 2. Try mixedUrl next
    if (mixedUrl) {
      try {
        // Set the source and load
        audio.src = mixedUrl;
        audio.load();

        console.log("Set audio source from mixedUrl");
        return true;
      } catch (e) {
        console.error("Error setting source from mixedUrl:", e);
      }
    }

    // 3. Try recordingBlob next
    if (recordingBlob && recordingBlob.size > 0) {
      try {
        // Revoke any existing blob URL
        if ((window as any).__currentBlobUrl) {
          URL.revokeObjectURL((window as any).__currentBlobUrl);
        }

        // Create new blob URL
        const blobUrl = URL.createObjectURL(recordingBlob);
        (window as any).__currentBlobUrl = blobUrl;

        // Set the source and load
        audio.src = blobUrl;
        audio.load();

        console.log("Set audio source from recordingBlob");
        return true;
      } catch (e) {
        console.error("Error setting source from recordingBlob:", e);
      }
    }

    // 4. Try recordingUrl next
    if (recordingUrl) {
      try {
        // Set the source and load
        audio.src = recordingUrl;
        audio.load();

        console.log("Set audio source from recordingUrl");
        return true;
      } catch (e) {
        console.error("Error setting source from recordingUrl:", e);
      }
    }

    // 5. Try window.__mixedRecordingUrl as a last resort
    if ((window as any).__mixedRecordingUrl) {
      try {
        // Create a promise to wait for the audio to load or fail
        const loadPromise = new Promise<boolean>((resolve) => {
          const handleCanPlay = () => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            console.log("Audio loaded from window.__mixedRecordingUrl");
            resolve(true);
          };

          const handleError = () => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            console.warn("Audio failed to load from window.__mixedRecordingUrl");
            resolve(false);
          };

          audio.addEventListener('canplay', handleCanPlay);
          audio.addEventListener('error', handleError);

          // Set a timeout in case the events don't fire
          setTimeout(() => {
            audio.removeEventListener('canplay', handleCanPlay);
            audio.removeEventListener('error', handleError);
            if (audio.readyState >= 3) {
              console.log("Audio loaded from window.__mixedRecordingUrl (timeout)");
              resolve(true);
            } else {
              console.warn("Audio failed to load from window.__mixedRecordingUrl (timeout)");
              resolve(false);
            }
          }, 5000);
        });

        // Set the source and load
        audio.src = (window as any).__mixedRecordingUrl;

        console.log("Set audio source from window.__mixedRecordingUrl");
        audio.load();

        // Wait for the audio to load or fail
        const success = await loadPromise;
        if (success) {
          return true;
        }

        console.warn("Failed to load audio from window.__mixedRecordingUrl");
      } catch (e) {
        console.error("Error setting source from window.__mixedRecordingUrl:", e);
      }
    }

    // If we get here, all sources failed
    console.error("No valid audio source available");
    setError("No audio source available");
    setIsLoading(false);
    return false;
  }, [mixedBlob, mixedUrl, recordingBlob, recordingUrl]);

  // Set audio source when sources change
  useEffect(() => {
    if (audioRef.current) {
      setAudioSource();
    }
  }, [mixedBlob, mixedUrl, recordingBlob, recordingUrl, setAudioSource]);

  // Toggle play/pause
  const togglePlayPause = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) {
      setError("Audio player not initialized");
      return;
    }

    try {
      // Resume audio context if suspended
      if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      // If playing, pause
      if (!audio.paused) {
        console.log("Pausing audio");
        audio.pause();
        setIsPlaying(false);
      } else {
        // If paused, play
        setIsLoading(true);

        // If we're at the end, restart from beginning
        if (audio.currentTime >= audio.duration) {
          audio.currentTime = 0;
        }

        // Play
        try {
          console.log("Attempting to play audio");
          
          // Cancel any existing play promise
          if (lastPlayPromiseRef.current) {
            console.log("Cancelling previous play promise");
            // We can't actually cancel the promise, but we can ignore its result
          }
          
          // Start a new play promise
          const playPromise = audio.play();
          lastPlayPromiseRef.current = playPromise;
          
          await playPromise;
          console.log("Audio playing successfully");
          setIsPlaying(true);
        } catch (e) {
          console.error("Error playing audio:", e);
          setError("Error playing audio. Please try again.");

          // Try to set the audio source again
          console.log("Trying to set audio source again after play error");
          await setAudioSource();
        } finally {
          setIsLoading(false);
        }
      }
    } catch (e) {
      console.error("Error toggling playback:", e);
      setError("Playback error");
      setIsLoading(false);
    }
  }, [isPlaying, setAudioSource]);

  // Handle seeking
  const handleSeek = useCallback((time: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    // Clamp time to valid range
    const clampedTime = Math.max(0, Math.min(time, audio.duration || 0));
    
    // Set current time
    audio.currentTime = clampedTime;
    setCurrentTime(clampedTime);
    
    console.log(`Seeked to ${clampedTime.toFixed(2)}s`);
  }, []);

  // Set volume
  const setVolume = useCallback((volume: number) => {
    // Clamp volume to valid range
    const clampedVolume = Math.max(0, Math.min(volume, 1));
    
    // Set volume on audio element
    if (audioRef.current) {
      audioRef.current.volume = clampedVolume;
    }
    
    // Set gain on gain node
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = clampedVolume;
    }
    
    console.log(`Set volume to ${clampedVolume.toFixed(2)}`);
  }, []);

  // Return the hook API
  return {
    isPlaying,
    isLoading,
    currentTime,
    duration,
    error,
    readyState,
    togglePlayPause,
    handleSeek,
    setVolume,
    audioRef
  };
}
