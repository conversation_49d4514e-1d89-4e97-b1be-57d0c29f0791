/**
 * Optimized Audio Recording Hook
 * 
 * An enhanced version of the audio recording hook with:
 * - Better error handling and recovery
 * - Performance monitoring
 * - Memory leak prevention
 * - Automatic cleanup
 * - Fallback mechanisms
 */

import { useState, useRef, useEffect, useCallback } from 'react';
import { useAudioStore } from '../../stores/audioStore';
import { useBeatStore } from '../../stores/beatStore';
import { SyncAudioRecorder } from '../services/syncAudioRecorder';
import { initAudioWorker, processRecordingForPreview } from '../services/audioWorkerService';
import { performanceMonitor, usePerformanceMonitor } from '../../utils/performance';

export interface UseOptimizedAudioRecordingOptions {
  onRecordingComplete?: (result: any) => void;
  onError?: (error: Error) => void;
  enablePerformanceMonitoring?: boolean;
  maxRecordingDuration?: number; // in seconds
  autoCleanup?: boolean;
}

export interface UseOptimizedAudioRecordingReturn {
  // Recording state
  isRecording: boolean;
  isInitializing: boolean;
  isStopping: boolean;
  isProcessing: boolean;
  
  // Audio data
  level: number;
  duration: number;
  
  // Error handling
  error: string | null;
  hasError: boolean;
  
  // Controls
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  resetRecording: () => void;
  
  // Performance data
  performanceMetrics?: any;
  
  // Transport controls
  transportRef: React.RefObject<any>;
}

export function useOptimizedAudioRecording(
  options: UseOptimizedAudioRecordingOptions = {}
): UseOptimizedAudioRecordingReturn {
  const {
    onRecordingComplete,
    onError,
    enablePerformanceMonitoring = true,
    maxRecordingDuration = 300, // 5 minutes default
    autoCleanup = true
  } = options;

  // Performance monitoring
  const { measureAsync, recordMetric } = usePerformanceMonitor();

  // State management
  const [isRecording, setIsRecording] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [level, setLevel] = useState(0);
  const [duration, setDuration] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  // Refs
  const recorderRef = useRef<SyncAudioRecorder | null>(null);
  const transportRef = useRef<any>(null);
  const recordingStartTimeRef = useRef<number>(0);
  const levelUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const durationUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Store access
  const recordingState = useAudioStore(state => state.recordingState);
  const setRecordingState = useAudioStore(state => state.setRecordingState);
  const currentBeat = useBeatStore(state => state.currentBeat);

  // Error handling
  const handleError = useCallback((err: Error | string) => {
    const errorMessage = err instanceof Error ? err.message : err;
    console.error('Audio recording error:', errorMessage);
    
    setError(errorMessage);
    setIsRecording(false);
    setIsInitializing(false);
    setIsStopping(false);
    setIsProcessing(false);
    setRecordingState('error');

    if (enablePerformanceMonitoring) {
      recordMetric({
        name: 'audio_recording_error',
        value: 1,
        timestamp: Date.now(),
        type: 'audio',
        metadata: { error: errorMessage }
      });
    }

    if (onError) {
      onError(err instanceof Error ? err : new Error(errorMessage));
    }
  }, [onError, setRecordingState, enablePerformanceMonitoring, recordMetric]);

  // Initialize recorder
  const initializeRecorder = useCallback(async () => {
    if (recorderRef.current) {
      return recorderRef.current;
    }

    try {
      const recorder = new SyncAudioRecorder({
        onError: handleError,
        onLevelUpdate: (newLevel: number) => {
          setLevel(newLevel);
        }
      });

      await recorder.initialize();
      recorderRef.current = recorder;
      
      if (enablePerformanceMonitoring) {
        recordMetric({
          name: 'audio_recorder_init_success',
          value: 1,
          timestamp: Date.now(),
          type: 'audio'
        });
      }

      return recorder;
    } catch (err) {
      handleError(err instanceof Error ? err : new Error('Failed to initialize recorder'));
      throw err;
    }
  }, [handleError, enablePerformanceMonitoring, recordMetric]);

  // Start recording
  const startRecording = useCallback(async () => {
    if (isRecording || isInitializing) {
      return;
    }

    setError(null);
    setIsInitializing(true);
    setRecordingState('initializing');

    try {
      await measureAsync('start_recording', async () => {
        // Initialize recorder
        const recorder = await initializeRecorder();
        
        // Initialize audio worker
        await initAudioWorker();

        // Start recording
        await recorder.startRecording();
        
        // Update state
        setIsRecording(true);
        setIsInitializing(false);
        setRecordingState('recording');
        recordingStartTimeRef.current = Date.now();

        // Start level monitoring
        if (levelUpdateIntervalRef.current) {
          clearInterval(levelUpdateIntervalRef.current);
        }
        levelUpdateIntervalRef.current = setInterval(() => {
          if (recorder.getState() === 'recording') {
            const currentLevel = recorder.getCurrentLevel();
            setLevel(currentLevel);
          }
        }, 50); // Update every 50ms

        // Start duration tracking
        if (durationUpdateIntervalRef.current) {
          clearInterval(durationUpdateIntervalRef.current);
        }
        durationUpdateIntervalRef.current = setInterval(() => {
          if (recordingStartTimeRef.current > 0) {
            const elapsed = (Date.now() - recordingStartTimeRef.current) / 1000;
            setDuration(elapsed);
            
            // Auto-stop if max duration reached
            if (elapsed >= maxRecordingDuration) {
              stopRecording();
            }
          }
        }, 100); // Update every 100ms

        console.log('Recording started successfully');
      });
    } catch (err) {
      handleError(err instanceof Error ? err : new Error('Failed to start recording'));
    }
  }, [
    isRecording,
    isInitializing,
    setRecordingState,
    initializeRecorder,
    measureAsync,
    maxRecordingDuration,
    handleError
  ]);

  // Stop recording
  const stopRecording = useCallback(async () => {
    if (!isRecording || isStopping) {
      return;
    }

    setIsStopping(true);
    setRecordingState('stopping');

    try {
      await measureAsync('stop_recording', async () => {
        const recorder = recorderRef.current;
        if (!recorder) {
          throw new Error('No recorder available');
        }

        // Stop recording
        const result = await recorder.stopRecording();
        
        if (result) {
          setIsProcessing(true);
          
          // Process recording for preview
          if (currentBeat) {
            const beatUrl = currentBeat.attributes?.audio_url || currentBeat.audio_url;
            if (beatUrl) {
              const processedResult = await processRecordingForPreview(
                result.blob,
                beatUrl
              );
              
              if (onRecordingComplete) {
                onRecordingComplete({
                  ...result,
                  processed: processedResult
                });
              }
            }
          }
        }

        // Update state
        setIsRecording(false);
        setIsStopping(false);
        setIsProcessing(false);
        setRecordingState('idle');
        
        console.log('Recording stopped successfully');
      });
    } catch (err) {
      handleError(err instanceof Error ? err : new Error('Failed to stop recording'));
    } finally {
      // Clear intervals
      if (levelUpdateIntervalRef.current) {
        clearInterval(levelUpdateIntervalRef.current);
        levelUpdateIntervalRef.current = null;
      }
      if (durationUpdateIntervalRef.current) {
        clearInterval(durationUpdateIntervalRef.current);
        durationUpdateIntervalRef.current = null;
      }
    }
  }, [
    isRecording,
    isStopping,
    setRecordingState,
    measureAsync,
    currentBeat,
    onRecordingComplete,
    handleError
  ]);

  // Reset recording
  const resetRecording = useCallback(() => {
    setError(null);
    setLevel(0);
    setDuration(0);
    setIsRecording(false);
    setIsInitializing(false);
    setIsStopping(false);
    setIsProcessing(false);
    setRecordingState('idle');
    recordingStartTimeRef.current = 0;

    // Clear intervals
    if (levelUpdateIntervalRef.current) {
      clearInterval(levelUpdateIntervalRef.current);
      levelUpdateIntervalRef.current = null;
    }
    if (durationUpdateIntervalRef.current) {
      clearInterval(durationUpdateIntervalRef.current);
      durationUpdateIntervalRef.current = null;
    }
  }, [setRecordingState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoCleanup) {
        // Clear intervals
        if (levelUpdateIntervalRef.current) {
          clearInterval(levelUpdateIntervalRef.current);
        }
        if (durationUpdateIntervalRef.current) {
          clearInterval(durationUpdateIntervalRef.current);
        }

        // Cleanup recorder
        if (recorderRef.current) {
          recorderRef.current.cleanup?.();
          recorderRef.current = null;
        }
      }
    };
  }, [autoCleanup]);

  // Performance metrics update
  useEffect(() => {
    if (enablePerformanceMonitoring) {
      const interval = setInterval(() => {
        setPerformanceMetrics(performanceMonitor.getSummary());
      }, 5000); // Update every 5 seconds

      return () => clearInterval(interval);
    }
  }, [enablePerformanceMonitoring]);

  return {
    // State
    isRecording,
    isInitializing,
    isStopping,
    isProcessing,
    level,
    duration,
    error,
    hasError: !!error,
    
    // Controls
    startRecording,
    stopRecording,
    resetRecording,
    
    // Performance
    performanceMetrics,
    
    // Transport
    transportRef
  };
}
