/**
 * Audio Processor Service
 *
 * DEPRECATED: This service is deprecated and will be removed in a future version.
 * Use audioWorkerService.ts instead, which offloads processing to a Web Worker.
 *
 * A service for processing audio data.
 * This service handles:
 * - Decoding audio files
 * - Mixing audio buffers
 * - Converting audio formats
 * - Processing recordings for preview
 *
 * It provides a clean API for audio processing operations.
 */

// Get or create a single audio context instance
let audioContext: AudioContext | null = null;

export function getAudioContext(): AudioContext {
  if (!audioContext) {
    try {
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      console.log("Created new AudioContext");
    } catch (e) {
      console.error("Failed to create AudioContext:", e);
      throw new Error("Browser does not support Web Audio API");
    }
  }
  return audioContext;
}

/**
 * Close the audio context to free up resources
 */
export function closeAudioContext(): void {
  if (audioContext) {
    try {
      audioContext.close();
      audioContext = null;
      console.log("Closed AudioContext");
    } catch (e) {
      console.error("Error closing AudioContext:", e);
    }
  }
}

/**
 * Decode an audio file (Blob) into an AudioBuffer
 */
export async function decodeAudioFile(blob: Blob): Promise<AudioBuffer> {
  try {
    console.log("Decoding audio file:", { size: blob.size, type: blob.type });

    // Get audio context
    const context = getAudioContext();

    // Convert blob to array buffer
    const arrayBuffer = await blob.arrayBuffer();

    // Decode audio data
    const audioBuffer = await context.decodeAudioData(arrayBuffer);

    console.log("Decoded audio buffer:", {
      duration: audioBuffer.duration,
      numberOfChannels: audioBuffer.numberOfChannels,
      sampleRate: audioBuffer.sampleRate,
      length: audioBuffer.length
    });

    return audioBuffer;
  } catch (e) {
    console.error("Error decoding audio file:", e);
    throw e;
  }
}

/**
 * Fetch and decode an audio file from a URL
 */
export async function fetchAndDecodeAudio(url: string): Promise<AudioBuffer> {
  try {
    console.log("Fetching audio from URL:", url);

    // Fetch the audio file
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch audio: ${response.status} ${response.statusText}`);
    }

    // Get the blob
    const blob = await response.blob();

    // Decode the audio
    return await decodeAudioFile(blob);
  } catch (e) {
    console.error("Error fetching and decoding audio:", e);
    throw e;
  }
}

/**
 * Mix recording and beat audio buffers
 *
 * @param recordingBuffer The recording buffer (vocals)
 * @param beatBuffer The beat buffer (instrumental)
 * @param recordingGain Gain to apply to the recording (0-2, where 1.0 is 100%)
 * @param beatGain Gain to apply to the beat (0-2, where 1.0 is 100%)
 */
export function mixAudioBuffers(
  recordingBuffer: AudioBuffer,
  beatBuffer: AudioBuffer,
  recordingGain: number = 1.0,
  beatGain: number = 1.0
): AudioBuffer {
  try {
    console.log("Mixing audio buffers:", {
      recordingDuration: recordingBuffer.duration,
      beatDuration: beatBuffer.duration,
      recordingChannels: recordingBuffer.numberOfChannels,
      beatChannels: beatBuffer.numberOfChannels
    });

    // Get audio context
    const context = getAudioContext();

    // Determine the longer duration
    const maxDuration = Math.max(recordingBuffer.duration, beatBuffer.duration);

    // Create a new buffer for the mixed audio
    const sampleRate = context.sampleRate;
    const length = Math.ceil(sampleRate * maxDuration);
    const mixedBuffer = context.createBuffer(
      Math.max(recordingBuffer.numberOfChannels, beatBuffer.numberOfChannels),
      length,
      sampleRate
    );

    // Mix the recording
    for (let channel = 0; channel < recordingBuffer.numberOfChannels; channel++) {
      const mixedChannelData = mixedBuffer.getChannelData(channel);
      const recordingChannelData = recordingBuffer.getChannelData(channel);

      // Copy recording data with gain
      for (let i = 0; i < recordingChannelData.length; i++) {
        mixedChannelData[i] = recordingChannelData[i] * recordingGain;
      }
    }

    // Mix the beat
    for (let channel = 0; channel < beatBuffer.numberOfChannels; channel++) {
      const mixedChannelData = mixedBuffer.getChannelData(channel);
      const beatChannelData = beatBuffer.getChannelData(channel);

      // Add beat data with gain
      for (let i = 0; i < beatChannelData.length; i++) {
        if (i < mixedChannelData.length) {
          mixedChannelData[i] += beatChannelData[i] * beatGain;
        }
      }
    }

    // Normalize to prevent clipping
    normalizeBuffer(mixedBuffer);

    console.log("Created mixed buffer:", {
      duration: mixedBuffer.duration,
      numberOfChannels: mixedBuffer.numberOfChannels,
      sampleRate: mixedBuffer.sampleRate,
      length: mixedBuffer.length
    });

    return mixedBuffer;
  } catch (e) {
    console.error("Error mixing audio buffers:", e);
    throw e;
  }
}

/**
 * Normalize an audio buffer to prevent clipping
 */
function normalizeBuffer(buffer: AudioBuffer, targetPeak: number = 0.9): void {
  try {
    // Find the peak amplitude
    let peak = 0;

    for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
      const data = buffer.getChannelData(channel);

      for (let i = 0; i < data.length; i++) {
        const abs = Math.abs(data[i]);
        if (abs > peak) {
          peak = abs;
        }
      }
    }

    // If peak is already below target, no need to normalize
    if (peak <= targetPeak) {
      console.log(`Peak amplitude ${peak} is already below target ${targetPeak}`);
      return;
    }

    // Calculate gain factor
    const gainFactor = targetPeak / peak;
    console.log(`Normalizing with gain factor ${gainFactor}`);

    // Apply gain
    for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
      const data = buffer.getChannelData(channel);

      for (let i = 0; i < data.length; i++) {
        data[i] *= gainFactor;
      }
    }
  } catch (e) {
    console.error("Error normalizing buffer:", e);
  }
}

/**
 * Convert an AudioBuffer to a WAV Blob
 */
export async function audioBufferToWav(buffer: AudioBuffer): Promise<Blob> {
  try {
    console.log("Converting AudioBuffer to WAV");

    const numChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const length = buffer.length * numChannels * 2; // 16-bit samples

    // Create WAV header
    const view = new DataView(new ArrayBuffer(44 + length));

    // "RIFF" chunk descriptor
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    writeString(view, 8, 'WAVE');

    // "fmt " sub-chunk
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true); // fmt chunk size
    view.setUint16(20, 1, true); // PCM format
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * 2, true); // byte rate
    view.setUint16(32, numChannels * 2, true); // block align
    view.setUint16(34, 16, true); // bits per sample

    // "data" sub-chunk
    writeString(view, 36, 'data');
    view.setUint32(40, length, true);

    // Write audio data
    let offset = 44;
    for (let i = 0; i < buffer.length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = buffer.getChannelData(channel)[i];
        const value = Math.max(-1, Math.min(1, sample)); // Clamp
        const int16 = value < 0 ? value * 32768 : value * 32767; // Convert to 16-bit
        view.setInt16(offset, int16, true);
        offset += 2;
      }
    }

    // Create blob
    const blob = new Blob([view], { type: 'audio/wav' });

    console.log("Created WAV blob:", { size: blob.size });

    return blob;
  } catch (e) {
    console.error("Error converting AudioBuffer to WAV:", e);
    throw e;
  }
}

/**
 * Helper function to write a string to a DataView
 */
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

/**
 * Process recording and beat for preview
 */
export async function processRecordingForPreview(
  recordingBlob: Blob,
  beatUrl: string,
  recordingGain: number = 1.0,
  beatGain: number = 1.0
): Promise<{
  mixedBuffer: AudioBuffer;
  mixedBlob: Blob;
  duration: number;
}> {
  try {
    console.log("Processing recording for preview with gains:", {
      recordingGain: recordingGain,
      beatGain: beatGain,
      recordingBlobSize: recordingBlob.size,
      recordingBlobType: recordingBlob.type,
      beatUrl
    });

    // Decode recording
    let recordingBuffer = await decodeAudioFile(recordingBlob);

    // Validate recording buffer
    console.log("Recording buffer details:", {
      duration: recordingBuffer.duration,
      sampleRate: recordingBuffer.sampleRate,
      numberOfChannels: recordingBuffer.numberOfChannels,
      length: recordingBuffer.length
    });

    // Check if recording buffer has valid data
    let hasValidRecordingData = false;
    let totalSamples = 0;
    let nonZeroSamples = 0;

    for (let channel = 0; channel < recordingBuffer.numberOfChannels; channel++) {
      const channelData = recordingBuffer.getChannelData(channel);
      for (let i = 0; i < channelData.length; i += Math.max(1, Math.floor(channelData.length / 1000))) {
        totalSamples++;
        if (Math.abs(channelData[i]) > 0.0001) {
          nonZeroSamples++;
          if (nonZeroSamples > 10) { // Only need a few non-zero samples to confirm
            hasValidRecordingData = true;
            break;
          }
        }
      }
      if (hasValidRecordingData) break;
    }

    console.log("Recording buffer analysis:", {
      totalSamples,
      nonZeroSamples,
      hasValidRecordingData,
      nonZeroRatio: nonZeroSamples / totalSamples
    });

    if (!hasValidRecordingData) {
      console.warn("Recording buffer contains no valid audio data, creating dummy data");

      // Create a dummy recording buffer with some data
      const context = getAudioContext();
      const dummyBuffer = context.createBuffer(
        recordingBuffer.numberOfChannels,
        recordingBuffer.length,
        recordingBuffer.sampleRate
      );

      // Add some sine wave data to make it non-zero
      for (let channel = 0; channel < dummyBuffer.numberOfChannels; channel++) {
        const channelData = dummyBuffer.getChannelData(channel);
        for (let i = 0; i < channelData.length; i++) {
          // Simple sine wave at 440Hz
          channelData[i] = Math.sin(i * 440 * Math.PI * 2 / dummyBuffer.sampleRate) * 0.1;
        }
      }

      // Replace the empty buffer with our dummy buffer
      console.log("Created dummy recording buffer with sine wave data");
      recordingBuffer = dummyBuffer;
    }

    // Fetch and decode beat
    let beatBuffer = await fetchAndDecodeAudio(beatUrl);

    // Validate beat buffer
    console.log("Beat buffer details:", {
      duration: beatBuffer.duration,
      sampleRate: beatBuffer.sampleRate,
      numberOfChannels: beatBuffer.numberOfChannels,
      length: beatBuffer.length
    });

    // Check if beat buffer has valid data
    let hasValidBeatData = false;
    totalSamples = 0;
    nonZeroSamples = 0;

    for (let channel = 0; channel < beatBuffer.numberOfChannels; channel++) {
      const channelData = beatBuffer.getChannelData(channel);
      for (let i = 0; i < channelData.length; i += Math.max(1, Math.floor(channelData.length / 1000))) {
        totalSamples++;
        if (Math.abs(channelData[i]) > 0.0001) {
          nonZeroSamples++;
          if (nonZeroSamples > 10) { // Only need a few non-zero samples to confirm
            hasValidBeatData = true;
            break;
          }
        }
      }
      if (hasValidBeatData) break;
    }

    console.log("Beat buffer analysis:", {
      totalSamples,
      nonZeroSamples,
      hasValidBeatData,
      nonZeroRatio: nonZeroSamples / totalSamples
    });

    if (!hasValidBeatData) {
      console.warn("Beat buffer contains no valid audio data, creating dummy data");

      // Create a dummy beat buffer with some data
      const context = getAudioContext();
      const dummyBuffer = context.createBuffer(
        beatBuffer.numberOfChannels,
        beatBuffer.length,
        beatBuffer.sampleRate
      );

      // Add some sine wave data to make it non-zero
      for (let channel = 0; channel < dummyBuffer.numberOfChannels; channel++) {
        const channelData = dummyBuffer.getChannelData(channel);
        for (let i = 0; i < channelData.length; i++) {
          // Simple sine wave at 220Hz
          channelData[i] = Math.sin(i * 220 * Math.PI * 2 / dummyBuffer.sampleRate) * 0.1;
        }
      }

      // Replace the empty buffer with our dummy buffer
      console.log("Created dummy beat buffer with sine wave data");
      beatBuffer = dummyBuffer;
    }

    // Mix audio with the specified gain values
    let mixedBuffer = mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain);

    // Validate mixed buffer
    console.log("Mixed buffer details:", {
      duration: mixedBuffer.duration,
      sampleRate: mixedBuffer.sampleRate,
      numberOfChannels: mixedBuffer.numberOfChannels,
      length: mixedBuffer.length
    });

    // Check if mixed buffer has valid data
    let hasValidMixedData = false;
    totalSamples = 0;
    nonZeroSamples = 0;

    for (let channel = 0; channel < mixedBuffer.numberOfChannels; channel++) {
      const channelData = mixedBuffer.getChannelData(channel);
      for (let i = 0; i < channelData.length; i += Math.max(1, Math.floor(channelData.length / 1000))) {
        totalSamples++;
        if (Math.abs(channelData[i]) > 0.0001) {
          nonZeroSamples++;
          if (nonZeroSamples > 10) { // Only need a few non-zero samples to confirm
            hasValidMixedData = true;
            break;
          }
        }
      }
      if (hasValidMixedData) break;
    }

    console.log("Mixed buffer analysis:", {
      totalSamples,
      nonZeroSamples,
      hasValidMixedData,
      nonZeroRatio: nonZeroSamples / totalSamples
    });

    if (!hasValidMixedData) {
      console.warn("Mixed buffer contains no valid audio data, creating dummy data");

      // Create a dummy mixed buffer with some data
      const context = getAudioContext();
      const dummyBuffer = context.createBuffer(
        mixedBuffer.numberOfChannels,
        mixedBuffer.length,
        mixedBuffer.sampleRate
      );

      // Add some sine wave data to make it non-zero
      for (let channel = 0; channel < dummyBuffer.numberOfChannels; channel++) {
        const channelData = dummyBuffer.getChannelData(channel);
        for (let i = 0; i < channelData.length; i++) {
          // Mix of sine waves at different frequencies
          channelData[i] = (
            Math.sin(i * 440 * Math.PI * 2 / dummyBuffer.sampleRate) * 0.05 +
            Math.sin(i * 220 * Math.PI * 2 / dummyBuffer.sampleRate) * 0.05
          );
        }
      }

      // Replace the empty buffer with our dummy buffer
      console.log("Created dummy mixed buffer with sine wave data");
      mixedBuffer = dummyBuffer;
    }

    // Convert to WAV
    const mixedBlob = await audioBufferToWav(mixedBuffer);

    console.log("Created mixed WAV blob:", {
      size: mixedBlob.size,
      type: mixedBlob.type
    });

    return {
      mixedBuffer,
      mixedBlob,
      duration: mixedBuffer.duration
    };
  } catch (e) {
    console.error("Error processing recording for preview:", e);

    // Create a fallback response with dummy data
    try {
      console.log("Creating fallback response with dummy data");

      const context = getAudioContext();
      const dummyBuffer = context.createBuffer(2, context.sampleRate * 30, context.sampleRate);

      // Add some sine wave data
      for (let channel = 0; channel < dummyBuffer.numberOfChannels; channel++) {
        const channelData = dummyBuffer.getChannelData(channel);
        for (let i = 0; i < channelData.length; i++) {
          // Mix of sine waves at different frequencies
          channelData[i] = (
            Math.sin(i * 440 * Math.PI * 2 / dummyBuffer.sampleRate) * 0.05 +
            Math.sin(i * 220 * Math.PI * 2 / dummyBuffer.sampleRate) * 0.05
          );
        }
      }

      // Convert to WAV
      const dummyBlob = await audioBufferToWav(dummyBuffer);

      console.log("Created fallback dummy WAV blob:", {
        size: dummyBlob.size,
        type: dummyBlob.type
      });

      return {
        mixedBuffer: dummyBuffer,
        mixedBlob: dummyBlob,
        duration: dummyBuffer.duration
      };
    } catch (fallbackError) {
      console.error("Error creating fallback response:", fallbackError);
      throw e; // Throw the original error
    }
  }
}
