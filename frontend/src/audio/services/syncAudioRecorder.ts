/**
 * SyncAudioRecorder Service
 * 
 * Simplified, robust audio recording with beat synchronization
 */

export interface RecordingResult {
  blob: Blob;
  url: string;
  duration: number;
  mimeType: string;
  syncTimestamp: number;
}

export type RecorderState = 'idle' | 'initializing' | 'recording' | 'stopping' | 'stopped' | 'error';

export interface SyncAudioRecorderOptions {
  onStateChange?: (state: RecorderState) => void;
  onError?: (error: string) => void;
  onLevelChange?: (level: number) => void;
  deviceId?: string;
}

export class SyncAudioRecorder {
  private audioContext: AudioContext | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private mediaStream: MediaStream | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private gainNode: GainNode | null = null;
  
  private state: RecorderState = 'idle';
  private deviceId: string = 'default';
  private gainValue: number = 0.3;
  private syncTimestamp: number = 0;
  private recordingData: Float32Array[] = [];
  private lastRecording: RecordingResult | null = null;

  // Callbacks
  private onStateChange: (state: RecorderState) => void = () => {};
  private onError: (error: string) => void = () => {};
  private onLevelChange: (level: number) => void = () => {};

  constructor(options: SyncAudioRecorderOptions = {}) {
    if (options.onStateChange) this.onStateChange = options.onStateChange;
    if (options.onError) this.onError = options.onError;
    if (options.onLevelChange) this.onLevelChange = options.onLevelChange;
    if (options.deviceId) this.deviceId = options.deviceId;
  }

  // Get current state
  getState(): RecorderState {
    return this.state;
  }

  // Set state and notify
  private setState(newState: RecorderState): void {
    if (this.state !== newState) {
      this.state = newState;
      this.onStateChange(newState);
    }
  }

  // Set gain value
  setGain(value: number): void {
    this.gainValue = Math.max(0, Math.min(2, value));
    if (this.gainNode) {
      this.gainNode.gain.value = this.gainValue;
    }
  }

  // Get current gain
  getGain(): number {
    return this.gainValue;
  }

  // Initialize audio system
  async initialize(): Promise<boolean> {
    try {
      this.setState('initializing');
      
      // Create audio context
      if (!this.audioContext) {
        this.audioContext = new AudioContext({
          latencyHint: 'interactive',
          sampleRate: 48000
        });
      }

      // Resume if suspended
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // Load worklet
      if (!this.workletNode) {
        await this.audioContext.audioWorklet.addModule('/syncRecorder.worklet.js');
        
        this.workletNode = new AudioWorkletNode(this.audioContext, 'sync-recorder-processor');
        this.workletNode.port.onmessage = this.handleWorkletMessage.bind(this);

        // Create gain node
        this.gainNode = this.audioContext.createGain();
        this.gainNode.gain.value = this.gainValue;
      }

      this.setState('idle');
      return true;
    } catch (error) {
      console.error("Failed to initialize audio system:", error);
      this.onError("Failed to initialize audio system");
      this.setState('error');
      return false;
    }
  }

  // Handle worklet messages
  private handleWorkletMessage(event: MessageEvent): void {
    const { data } = event;

    switch (data.type) {
      case 'ready':
        console.log("Worklet ready");
        break;

      case 'started':
        console.log("Recording started at:", data.timestamp);
        this.syncTimestamp = data.syncTimestamp;
        this.setState('recording');
        break;

      case 'stopped':
        console.log("Recording stopped, duration:", data.duration);
        this.recordingData = data.buffer;
        this.processRecording();
        break;

      case 'level':
        this.onLevelChange(data.value);
        break;

      default:
        console.warn("Unknown worklet message:", data);
    }
  }

  // Start recording
  async start(syncTimestamp?: number): Promise<boolean> {
    try {
      if (this.state !== 'idle') {
        console.warn("Cannot start recording, current state:", this.state);
        return false;
      }

      // Initialize if needed
      if (!this.audioContext || !this.workletNode) {
        const initialized = await this.initialize();
        if (!initialized) return false;
      }

      // Get microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          deviceId: this.deviceId !== 'default' ? { exact: this.deviceId } : undefined,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 48000
        }
      });

      // Create source node
      this.sourceNode = this.audioContext!.createMediaStreamSource(this.mediaStream);
      
      // Connect audio graph
      this.sourceNode.connect(this.gainNode!);
      this.gainNode!.connect(this.workletNode!);

      // Start recording via worklet
      this.workletNode!.port.postMessage({
        type: 'start',
        syncTimestamp: syncTimestamp || this.audioContext!.currentTime
      });

      return true;
    } catch (error) {
      console.error("Failed to start recording:", error);
      this.onError("Failed to start recording");
      this.setState('error');
      return false;
    }
  }

  // Stop recording
  async stop(): Promise<RecordingResult | null> {
    try {
      if (this.state !== 'recording') {
        console.warn("Cannot stop recording, current state:", this.state);
        return null;
      }

      this.setState('stopping');

      // Stop recording via worklet
      if (this.workletNode) {
        this.workletNode.port.postMessage({ type: 'stop' });
      }

      // Stop media stream
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => track.stop());
        this.mediaStream = null;
      }

      // Disconnect audio nodes
      if (this.sourceNode) {
        this.sourceNode.disconnect();
        this.sourceNode = null;
      }

      // Wait for processing to complete
      return new Promise((resolve) => {
        const checkProcessing = () => {
          if (this.state === 'stopped') {
            resolve(this.getLastRecording());
          } else {
            setTimeout(checkProcessing, 10);
          }
        };
        checkProcessing();
      });
    } catch (error) {
      console.error("Failed to stop recording:", error);
      this.onError("Failed to stop recording");
      this.setState('error');
      return null;
    }
  }

  // Process recording data
  private async processRecording(): Promise<void> {
    try {
      if (!this.audioContext || !this.recordingData.length) {
        this.setState('error');
        return;
      }

      // Convert Float32Array chunks to a single buffer
      const context = this.audioContext;
      const recordingLength = this.recordingData.reduce((acc: number, chunk: Float32Array) => acc + chunk.length, 0);
      const recordingBuffer = context.createBuffer(1, recordingLength, context.sampleRate);
      const channelData = recordingBuffer.getChannelData(0);

      let offset = 0;
      for (const chunk of this.recordingData) {
        channelData.set(chunk, offset);
        offset += chunk.length;
      }

      // Convert to WAV
      const arrayBuffer = this.audioBufferToWav(recordingBuffer);
      const blob = new Blob([arrayBuffer], { type: 'audio/wav' });
      const url = URL.createObjectURL(blob);

      // Store result
      this.lastRecording = {
        blob,
        url,
        duration: recordingBuffer.duration,
        mimeType: 'audio/wav',
        syncTimestamp: this.syncTimestamp
      };

      this.setState('stopped');
    } catch (error) {
      console.error("Failed to process recording:", error);
      this.onError("Failed to process recording");
      this.setState('error');
    }
  }

  // Get last recording result
  getLastRecording(): RecordingResult | null {
    return this.lastRecording;
  }

  // Convert AudioBuffer to WAV ArrayBuffer
  private audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
    const numChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const length = buffer.length * numChannels * 2; // 16-bit samples
    
    // Create WAV header
    const view = new DataView(new ArrayBuffer(44 + length));
    
    // "RIFF" chunk descriptor
    this.writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    this.writeString(view, 8, 'WAVE');
    
    // "fmt " sub-chunk
    this.writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true); // subchunk size
    view.setUint16(20, 1, true); // PCM format
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * 2, true); // byte rate
    view.setUint16(32, numChannels * 2, true); // block align
    view.setUint16(34, 16, true); // bits per sample
    
    // "data" sub-chunk
    this.writeString(view, 36, 'data');
    view.setUint32(40, length, true);
    
    // Write audio data
    let offset = 44;
    for (let i = 0; i < buffer.length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }
    
    return view.buffer;
  }

  // Helper to write string to DataView
  private writeString(view: DataView, offset: number, string: string): void {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }

  // Cleanup (alias for dispose)
  dispose(): void {
    this.cleanup();
  }

  // Cleanup
  cleanup(): void {
    // Stop any ongoing recording
    if (this.state === 'recording') {
      this.stop();
    }

    // Disconnect nodes
    if (this.sourceNode) {
      this.sourceNode.disconnect();
    }
    if (this.gainNode) {
      this.gainNode.disconnect();
    }
    if (this.workletNode) {
      this.workletNode.disconnect();
    }

    // Close audio context
    if (this.audioContext) {
      this.audioContext.close();
    }

    // Reset state
    this.setState('idle');
  }
}
