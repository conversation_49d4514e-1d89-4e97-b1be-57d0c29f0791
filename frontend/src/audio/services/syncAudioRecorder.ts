/**
 * SyncAudioRecorder Service
 *
 * This service provides synchronized audio recording capabilities:
 * - Uses AudioWorklet for precise timing
 * - Synchronizes recording with beat playback
 * - Provides accurate timing information for mixing
 * - Handles audio input device selection
 * - Monitors audio levels
 */

import * as Comlink from 'comlink';

// Define types
export interface RecordingResult {
  blob: Blob;
  url: string;
  duration: number;
  mimeType: string;
  syncTimestamp: number;
}

export type RecorderState = 'idle' | 'initializing' | 'recording' | 'stopping' | 'stopped' | 'error';

export interface SyncAudioRecorderOptions {
  onStateChange?: (state: RecorderState) => void;
  onError?: (error: string) => void;
  onLevelChange?: (level: number) => void;
  deviceId?: string;
  mimeType?: string;
}

// Main recorder class
export class SyncAudioRecorder {
  private audioContext: AudioContext | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private mediaStream: MediaStream | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private gainNode: GainNode | null = null;
  private analyserNode: AnalyserNode | null = null;
  // We don't use this directly but it's set in the worklet and returned to us
  private _recordingStartTime: number = 0;
  private syncTimestamp: number = 0;
  private chunks: Float32Array[] = [];
  private worker: Worker | null = null;
  private _workerApi: any = null; // Using underscore to avoid unused variable warning

  // State
  private state: RecorderState = 'idle';
  private deviceId: string = 'default';
  private mimeType: string = 'audio/webm;codecs=opus';
  private gainValue: number = 1.0;

  // Callbacks
  private onStateChange: (state: RecorderState) => void = () => {};
  private onError: (error: string) => void = () => {};
  private onLevelChange: (level: number) => void = () => {};

  constructor(options: SyncAudioRecorderOptions = {}) {
    // Set options
    if (options.onStateChange) this.onStateChange = options.onStateChange;
    if (options.onError) this.onError = options.onError;
    if (options.onLevelChange) this.onLevelChange = options.onLevelChange;
    if (options.deviceId) this.deviceId = options.deviceId;
    if (options.mimeType) this.mimeType = options.mimeType;

    // Initialize worker
    this.initWorker();
  }

  // Initialize the Web Worker
  private async initWorker() {
    try {
      // Create a new worker directly from the URL
      this.worker = new Worker('/audioProcessor.worker.js');

      // Wrap with Comlink
      this._workerApi = Comlink.wrap(this.worker);

      console.log("Audio processor worker initialized");
    } catch (error) {
      console.error("Error initializing audio processor worker:", error);
      this.onError("Failed to initialize audio processor");
    }
  }

  // Initialize the audio context and worklet
  private async initAudioContext(): Promise<boolean> {
    try {
      // Create audio context if it doesn't exist
      if (!this.audioContext) {
        this.audioContext = new AudioContext({
          latencyHint: 'interactive',
          sampleRate: 48000
        });
      }

      // Resume audio context if suspended
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // Load the worklet if not already loaded
      if (!this.workletNode) {
        await this.audioContext.audioWorklet.addModule('/syncRecorder.worklet.js');

        // Create worklet node
        this.workletNode = new AudioWorkletNode(this.audioContext, 'sync-recorder-processor', {
          numberOfInputs: 1,
          numberOfOutputs: 1,
          processorOptions: {
            mimeType: this.mimeType
          }
        });

        // Set up message handling
        this.workletNode.port.onmessage = this.handleWorkletMessage.bind(this);

        // Create gain node
        this.gainNode = this.audioContext.createGain();
        this.gainNode.gain.value = this.gainValue;

        // Create analyser node
        this.analyserNode = this.audioContext.createAnalyser();
        this.analyserNode.fftSize = 2048;

        // Connect nodes for recording (but not to output)
        this.gainNode.connect(this.workletNode);
        this.workletNode.connect(this.analyserNode);

        // Do NOT connect to destination to prevent feedback
        // this.analyserNode.connect(this.audioContext.destination);
      }

      return true;
    } catch (error) {
      console.error("Error initializing audio context:", error);
      this.onError("Failed to initialize audio system");
      this.setState('error');
      return false;
    }
  }

  // Handle messages from the worklet
  private handleWorkletMessage(event: MessageEvent) {
    const { data } = event;

    switch (data.type) {
      case 'ready':
        console.log("Worklet is ready");
        break;

      case 'started':
        console.log("Recording started at:", data.timestamp);
        this._recordingStartTime = data.timestamp;
        this.syncTimestamp = data.syncTimestamp;
        this.setState('recording');
        break;

      case 'stopped':
        console.log("Recording stopped, duration:", data.duration);
        this.chunks = data.buffer;
        this.processRecording();
        break;

      case 'level':
        this.onLevelChange(data.value);
        break;

      default:
        console.log("Unknown message from worklet:", data);
    }
  }

  // Set recorder state
  private setState(state: RecorderState) {
    this.state = state;
    this.onStateChange(state);
  }

  // Process the recorded audio
  private async processRecording() {
    try {
      this.setState('stopping');

      // Convert Float32Array chunks to a single buffer
      const context = this.audioContext!;
      const recordingLength = this.chunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const recordingBuffer = context.createBuffer(1, recordingLength, context.sampleRate);
      const channelData = recordingBuffer.getChannelData(0);

      let offset = 0;
      for (const chunk of this.chunks) {
        channelData.set(chunk, offset);
        offset += chunk.length;
      }

      // Convert to WAV using the worker
      const arrayBuffer = await this.exportBuffer(recordingBuffer);
      const blob = new Blob([arrayBuffer], { type: this.mimeType });
      const url = URL.createObjectURL(blob);

      // Create result
      const result: RecordingResult = {
        blob,
        url,
        duration: recordingBuffer.duration,
        mimeType: this.mimeType,
        syncTimestamp: this.syncTimestamp
      };

      // Update state
      this.setState('stopped');

      return result;
    } catch (error) {
      console.error("Error processing recording:", error);
      this.onError("Failed to process recording");
      this.setState('error');
      return null;
    }
  }

  // Export buffer to ArrayBuffer using Web Worker
  private async exportBuffer(buffer: AudioBuffer): Promise<ArrayBuffer> {
    try {
      if (!this._workerApi) {
        throw new Error("Worker not initialized");
      }

      // Extract the raw audio data from the buffer
      // AudioBuffer objects can't be passed directly to workers
      const channelData: Float32Array[] = [];
      for (let i = 0; i < buffer.numberOfChannels; i++) {
        channelData.push(buffer.getChannelData(i).slice(0));
      }

      // Create a transferable object with the buffer's properties
      const audioData = {
        channelData,
        sampleRate: buffer.sampleRate,
        length: buffer.length,
        numberOfChannels: buffer.numberOfChannels,
        duration: buffer.duration
      };

      console.log("Sending audio data to worker:", {
        sampleRate: audioData.sampleRate,
        length: audioData.length,
        numberOfChannels: audioData.numberOfChannels,
        duration: audioData.duration
      });

      // Use the worker to convert the buffer to WAV
      const wavBlob = await this._workerApi.convertToWav(audioData);

      // Convert the blob to an ArrayBuffer
      return await wavBlob.arrayBuffer();
    } catch (error) {
      console.error("Error exporting buffer with worker:", error);

      // Fallback to local processing if worker fails
      console.warn("Falling back to local processing for audio export");

      // Convert to WAV format
      const numChannels = buffer.numberOfChannels;
      const sampleRate = buffer.sampleRate;
      const bytesPerSample = 2; // 16-bit
      const length = buffer.length * numChannels * bytesPerSample;

      // Create buffer with WAV header
      const arrayBuffer = new ArrayBuffer(44 + length);
      const view = new DataView(arrayBuffer);

      // Write WAV header
      // "RIFF" chunk
      writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + length, true);
      writeString(view, 8, 'WAVE');

      // "fmt " chunk
      writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true); // PCM format
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * bytesPerSample, true);
      view.setUint16(32, numChannels * bytesPerSample, true);
      view.setUint16(34, 8 * bytesPerSample, true);

      // "data" chunk
      writeString(view, 36, 'data');
      view.setUint32(40, length, true);

      // Write audio data
      let offset = 44;
      for (let i = 0; i < buffer.length; i++) {
        for (let channel = 0; channel < numChannels; channel++) {
          const sample = buffer.getChannelData(channel)[i];
          const value = Math.max(-1, Math.min(1, sample));
          const int16 = value < 0 ? value * 32768 : value * 32767;
          view.setInt16(offset, int16, true);
          offset += bytesPerSample;
        }
      }

      return arrayBuffer;
    }
  }

  // Start recording
  public async start(syncWithTimestamp?: number): Promise<void> {
    try {
      if (this.state !== 'idle' && this.state !== 'stopped' && this.state !== 'error') {
        console.warn("Cannot start recording in current state:", this.state);
        return;
      }

      this.setState('initializing');

      // Initialize audio context
      const initialized = await this.initAudioContext();
      if (!initialized) return;

      // Get media stream
      try {
        this.mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            deviceId: this.deviceId ? { exact: this.deviceId } : undefined,
            // Enable echo cancellation to prevent feedback
            echoCancellation: true,
            // Enable noise suppression to reduce background noise
            noiseSuppression: true,
            // Disable auto gain control to allow manual control
            autoGainControl: false
          }
        });
      } catch (err: any) {
        if (err.name === 'NotAllowedError') {
          this.onError("Microphone permission denied");
        } else if (err.name === 'NotFoundError') {
          this.onError("No microphone found");
        } else {
          this.onError(`Microphone error: ${err.message}`);
        }
        this.setState('error');
        return;
      }

      // Create source node
      this.sourceNode = this.audioContext!.createMediaStreamSource(this.mediaStream);

      // Connect source to gain node
      this.sourceNode.connect(this.gainNode!);

      // Set sync timestamp
      this.syncTimestamp = syncWithTimestamp || this.audioContext!.currentTime;

      // Start recording
      if (this.workletNode) {
        // Set parameters
        const isRecordingParam = this.workletNode.parameters.get('isRecording');
        const syncTimestampParam = this.workletNode.parameters.get('syncTimestamp');

        if (isRecordingParam) isRecordingParam.setValueAtTime(1, this.audioContext!.currentTime);
        if (syncTimestampParam) syncTimestampParam.setValueAtTime(this.syncTimestamp, this.audioContext!.currentTime);

        // Send message to worklet
        this.workletNode.port.postMessage({
          type: 'start',
          syncTimestamp: this.syncTimestamp
        });
      }
    } catch (error) {
      console.error("Error starting recording:", error);
      this.onError("Failed to start recording");
      this.setState('error');
    }
  }

  // Stop recording
  public async stop(): Promise<RecordingResult | null> {
    try {
      if (this.state !== 'recording') {
        console.warn("Cannot stop recording in current state:", this.state);
        return null;
      }

      // Stop recording in worklet
      if (this.workletNode) {
        // Set parameter
        const isRecordingParam = this.workletNode.parameters.get('isRecording');
        if (isRecordingParam) isRecordingParam.setValueAtTime(0, this.audioContext!.currentTime);

        // Send message to worklet
        this.workletNode.port.postMessage({
          type: 'stop'
        });
      }

      // Stop media stream tracks
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => {
          track.stop();
        });
      }

      // Wait for processing to complete
      return new Promise((resolve) => {
        const checkState = () => {
          if (this.state === 'stopped') {
            resolve(this.processRecording());
          } else if (this.state === 'error') {
            resolve(null);
          } else {
            setTimeout(checkState, 100);
          }
        };

        checkState();
      });
    } catch (error) {
      console.error("Error stopping recording:", error);
      this.onError("Failed to stop recording");
      this.setState('error');
      return null;
    }
  }

  // Set gain value
  public setGain(value: number): void {
    this.gainValue = Math.max(0, Math.min(2, value));
    if (this.gainNode) {
      this.gainNode.gain.value = this.gainValue;
    }
  }

  // Set input device
  public setInputDevice(deviceId: string): void {
    this.deviceId = deviceId;

    // If already recording, restart with new device
    if (this.state === 'recording') {
      this.stop().then(() => this.start());
    }
  }

  // Get current state
  public getState(): RecorderState {
    return this.state;
  }

  // Clean up resources
  public dispose(): void {
    // Stop recording if active
    if (this.state === 'recording') {
      this.stop();
    }

    // Disconnect nodes
    if (this.sourceNode) {
      this.sourceNode.disconnect();
    }

    if (this.gainNode) {
      this.gainNode.disconnect();
    }

    if (this.workletNode) {
      this.workletNode.disconnect();
    }

    if (this.analyserNode) {
      this.analyserNode.disconnect();
    }

    // Close audio context
    if (this.audioContext) {
      this.audioContext.close();
    }

    // Terminate worker
    if (this.worker) {
      this.worker.terminate();
    }

    // Reset state
    this.setState('idle');
  }
}

// Helper function to write a string to a DataView
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}
