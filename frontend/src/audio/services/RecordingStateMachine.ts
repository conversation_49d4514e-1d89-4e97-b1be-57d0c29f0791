/**
 * Recording State Machine
 *
 * A robust state machine to manage recording states and prevent race conditions.
 * This ensures that only valid state transitions can occur and provides
 * proper error handling and recovery mechanisms.
 */

import React from 'react';

export type RecordingState = 
  | 'idle'
  | 'initializing' 
  | 'recording'
  | 'stopping'
  | 'processing'
  | 'stopped'
  | 'error';

export type RecordingEvent = 
  | 'START_RECORDING'
  | 'RECORDING_STARTED'
  | 'STOP_RECORDING'
  | 'RECORDING_STOPPED'
  | 'PROCESSING_STARTED'
  | 'PROCESSING_COMPLETED'
  | 'ERROR_OCCURRED'
  | 'RESET';

export interface RecordingContext {
  error?: string;
  recordingBlob?: Blob;
  recordingUrl?: string;
  duration?: number;
  beatUrl?: string;
  startTime?: number;
}

export interface StateTransition {
  from: RecordingState;
  event: RecordingEvent;
  to: RecordingState;
  guard?: (context: RecordingContext) => boolean;
  action?: (context: RecordingContext) => void;
}

export class RecordingStateMachine {
  private currentState: RecordingState = 'idle';
  private context: RecordingContext = {};
  private listeners: Array<(state: RecordingState, context: RecordingContext) => void> = [];
  private transitionLock = false;

  private transitions: StateTransition[] = [
    // From idle
    { from: 'idle', event: 'START_RECORDING', to: 'initializing' },
    { from: 'idle', event: 'ERROR_OCCURRED', to: 'error' },
    
    // From initializing
    { from: 'initializing', event: 'RECORDING_STARTED', to: 'recording' },
    { from: 'initializing', event: 'ERROR_OCCURRED', to: 'error' },
    { from: 'initializing', event: 'RESET', to: 'idle' },
    
    // From recording
    { from: 'recording', event: 'STOP_RECORDING', to: 'stopping' },
    { from: 'recording', event: 'ERROR_OCCURRED', to: 'error' },
    
    // From stopping
    { from: 'stopping', event: 'RECORDING_STOPPED', to: 'processing' },
    { from: 'stopping', event: 'ERROR_OCCURRED', to: 'error' },
    
    // From processing
    { from: 'processing', event: 'PROCESSING_COMPLETED', to: 'stopped' },
    { from: 'processing', event: 'ERROR_OCCURRED', to: 'error' },
    
    // From stopped
    { from: 'stopped', event: 'START_RECORDING', to: 'initializing' },
    { from: 'stopped', event: 'RESET', to: 'idle' },
    
    // From error
    { from: 'error', event: 'RESET', to: 'idle' },
    { from: 'error', event: 'START_RECORDING', to: 'initializing' },
  ];

  constructor(initialContext: Partial<RecordingContext> = {}) {
    this.context = { ...initialContext };
  }

  /**
   * Get current state
   */
  getState(): RecordingState {
    return this.currentState;
  }

  /**
   * Get current context
   */
  getContext(): RecordingContext {
    return { ...this.context };
  }

  /**
   * Check if a transition is valid
   */
  canTransition(event: RecordingEvent): boolean {
    const transition = this.transitions.find(
      t => t.from === this.currentState && t.event === event
    );
    
    if (!transition) return false;
    
    // Check guard condition if present
    if (transition.guard && !transition.guard(this.context)) {
      return false;
    }
    
    return true;
  }

  /**
   * Send an event to trigger a state transition
   */
  async send(event: RecordingEvent, payload?: Partial<RecordingContext>): Promise<boolean> {
    // Prevent concurrent transitions
    if (this.transitionLock) {
      console.warn(`Transition blocked: ${event} while transition in progress`);
      return false;
    }

    const transition = this.transitions.find(
      t => t.from === this.currentState && t.event === event
    );

    if (!transition) {
      console.warn(`Invalid transition: ${event} from ${this.currentState}`);
      return false;
    }

    // Check guard condition
    if (transition.guard && !transition.guard(this.context)) {
      console.warn(`Transition guard failed: ${event} from ${this.currentState}`);
      return false;
    }

    // Lock transitions during execution
    this.transitionLock = true;

    try {
      // Update context with payload
      if (payload) {
        this.context = { ...this.context, ...payload };
      }

      // Execute transition action
      if (transition.action) {
        transition.action(this.context);
      }

      // Update state
      const previousState = this.currentState;
      this.currentState = transition.to;

      console.log(`State transition: ${previousState} -> ${this.currentState} (${event})`);

      // Notify listeners
      this.notifyListeners();

      return true;
    } catch (error) {
      console.error(`Error during state transition:`, error);
      
      // Transition to error state
      this.currentState = 'error';
      this.context.error = error instanceof Error ? error.message : String(error);
      this.notifyListeners();
      
      return false;
    } finally {
      this.transitionLock = false;
    }
  }

  /**
   * Add a state change listener
   */
  onStateChange(listener: (state: RecordingState, context: RecordingContext) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Reset to idle state
   */
  reset(): void {
    this.currentState = 'idle';
    this.context = {};
    this.transitionLock = false;
    this.notifyListeners();
  }

  /**
   * Update context without changing state
   */
  updateContext(updates: Partial<RecordingContext>): void {
    this.context = { ...this.context, ...updates };
    this.notifyListeners();
  }

  /**
   * Check if currently in a "busy" state
   */
  isBusy(): boolean {
    return ['initializing', 'recording', 'stopping', 'processing'].includes(this.currentState);
  }

  /**
   * Check if can start recording
   */
  canStartRecording(): boolean {
    return this.canTransition('START_RECORDING');
  }

  /**
   * Check if can stop recording
   */
  canStopRecording(): boolean {
    return this.canTransition('STOP_RECORDING');
  }

  /**
   * Get valid next events for current state
   */
  getValidEvents(): RecordingEvent[] {
    return this.transitions
      .filter(t => t.from === this.currentState)
      .filter(t => !t.guard || t.guard(this.context))
      .map(t => t.event);
  }

  /**
   * Notify all listeners of state change
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.currentState, this.context);
      } catch (error) {
        console.error('Error in state change listener:', error);
      }
    });
  }

  /**
   * Get state machine status for debugging
   */
  getStatus() {
    return {
      currentState: this.currentState,
      context: this.context,
      validEvents: this.getValidEvents(),
      isBusy: this.isBusy(),
      isLocked: this.transitionLock
    };
  }
}

// Singleton instance for global use
export const recordingStateMachine = new RecordingStateMachine();

// React hook for using the state machine
export function useRecordingStateMachine() {
  const [state, setState] = React.useState(recordingStateMachine.getState());
  const [context, setContext] = React.useState(recordingStateMachine.getContext());

  React.useEffect(() => {
    const unsubscribe = recordingStateMachine.onStateChange((newState, newContext) => {
      setState(newState);
      setContext(newContext);
    });

    return unsubscribe;
  }, []);

  return {
    state,
    context,
    send: recordingStateMachine.send.bind(recordingStateMachine),
    canTransition: recordingStateMachine.canTransition.bind(recordingStateMachine),
    reset: recordingStateMachine.reset.bind(recordingStateMachine),
    isBusy: recordingStateMachine.isBusy.bind(recordingStateMachine),
    canStartRecording: recordingStateMachine.canStartRecording.bind(recordingStateMachine),
    canStopRecording: recordingStateMachine.canStopRecording.bind(recordingStateMachine),
    getStatus: recordingStateMachine.getStatus.bind(recordingStateMachine)
  };
}
