/**
 * Audio Context Manager
 * 
 * Singleton manager for AudioContext to prevent conflicts and ensure
 * proper resource management. Handles context creation, suspension,
 * resumption, and cleanup.
 */

import { performanceMonitor } from '../../utils/performance';

export interface AudioContextConfig {
  sampleRate?: number;
  latencyHint?: AudioContextLatencyCategory;
  maxChannelCount?: number;
}

export interface AudioContextMetrics {
  state: AudioContextState;
  sampleRate: number;
  currentTime: number;
  baseLatency: number;
  outputLatency: number;
  activeSourceNodes: number;
  memoryUsage: number;
}

class AudioContextManager {
  private static instance: AudioContextManager;
  private audioContext: AudioContext | null = null;
  private config: AudioContextConfig = {
    sampleRate: 48000,
    latencyHint: 'interactive'
  };
  private activeNodes: Set<AudioNode> = new Set();
  private cleanupCallbacks: Array<() => void> = [];
  private isInitialized = false;
  private initializationPromise: Promise<AudioContext> | null = null;

  private constructor() {
    // Private constructor for singleton
    this.setupEventListeners();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): AudioContextManager {
    if (!AudioContextManager.instance) {
      AudioContextManager.instance = new AudioContextManager();
    }
    return AudioContextManager.instance;
  }

  /**
   * Initialize audio context with configuration
   */
  async initialize(config?: AudioContextConfig): Promise<AudioContext> {
    // Return existing initialization promise if already initializing
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Return existing context if already initialized
    if (this.audioContext && this.isInitialized) {
      return this.audioContext;
    }

    this.initializationPromise = this.createAudioContext(config);
    
    try {
      const context = await this.initializationPromise;
      this.isInitialized = true;
      return context;
    } catch (error) {
      this.initializationPromise = null;
      throw error;
    }
  }

  /**
   * Create and configure audio context
   */
  private async createAudioContext(config?: AudioContextConfig): Promise<AudioContext> {
    return performanceMonitor.measureAsync('audio_context_init', async () => {
      try {
        // Update configuration
        if (config) {
          this.config = { ...this.config, ...config };
        }

        // Create audio context
        this.audioContext = new AudioContext({
          sampleRate: this.config.sampleRate,
          latencyHint: this.config.latencyHint
        });

        console.log('AudioContext created:', {
          sampleRate: this.audioContext.sampleRate,
          state: this.audioContext.state,
          baseLatency: this.audioContext.baseLatency,
          outputLatency: this.audioContext.outputLatency
        });

        // Resume if suspended (required for user interaction)
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
          console.log('AudioContext resumed');
        }

        // Record successful initialization
        performanceMonitor.recordMetric({
          name: 'audio_context_init_success',
          value: 1,
          timestamp: Date.now(),
          type: 'audio',
          metadata: {
            sampleRate: this.audioContext.sampleRate,
            baseLatency: this.audioContext.baseLatency
          }
        });

        return this.audioContext;
      } catch (error) {
        console.error('Failed to create AudioContext:', error);
        
        performanceMonitor.recordMetric({
          name: 'audio_context_init_failure',
          value: 1,
          timestamp: Date.now(),
          type: 'audio',
          metadata: { error: error instanceof Error ? error.message : String(error) }
        });

        throw new Error(`Failed to initialize audio context: ${error}`);
      }
    });
  }

  /**
   * Get current audio context
   */
  getContext(): AudioContext | null {
    return this.audioContext;
  }

  /**
   * Ensure audio context is ready for use
   */
  async ensureReady(): Promise<AudioContext> {
    if (!this.audioContext || !this.isInitialized) {
      return this.initialize();
    }

    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    return this.audioContext;
  }

  /**
   * Register an audio node for tracking
   */
  registerNode(node: AudioNode): void {
    this.activeNodes.add(node);
  }

  /**
   * Unregister an audio node
   */
  unregisterNode(node: AudioNode): void {
    this.activeNodes.delete(node);
  }

  /**
   * Add cleanup callback
   */
  addCleanupCallback(callback: () => void): void {
    this.cleanupCallbacks.push(callback);
  }

  /**
   * Remove cleanup callback
   */
  removeCleanupCallback(callback: () => void): void {
    const index = this.cleanupCallbacks.indexOf(callback);
    if (index > -1) {
      this.cleanupCallbacks.splice(index, 1);
    }
  }

  /**
   * Suspend audio context
   */
  async suspend(): Promise<void> {
    if (this.audioContext && this.audioContext.state === 'running') {
      await this.audioContext.suspend();
      console.log('AudioContext suspended');
    }
  }

  /**
   * Resume audio context
   */
  async resume(): Promise<void> {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
      console.log('AudioContext resumed');
    }
  }

  /**
   * Get audio context metrics
   */
  getMetrics(): AudioContextMetrics | null {
    if (!this.audioContext) return null;

    return {
      state: this.audioContext.state,
      sampleRate: this.audioContext.sampleRate,
      currentTime: this.audioContext.currentTime,
      baseLatency: this.audioContext.baseLatency,
      outputLatency: this.audioContext.outputLatency,
      activeSourceNodes: this.activeNodes.size,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  /**
   * Estimate memory usage
   */
  private estimateMemoryUsage(): number {
    // Rough estimation based on active nodes
    return this.activeNodes.size * 1024; // 1KB per node estimate
  }

  /**
   * Check if audio context is healthy
   */
  isHealthy(): boolean {
    if (!this.audioContext) return false;
    
    return (
      this.audioContext.state !== 'closed' &&
      this.isInitialized &&
      this.activeNodes.size < 100 // Arbitrary limit
    );
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    console.log('Cleaning up AudioContext...');

    // Execute cleanup callbacks
    this.cleanupCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in cleanup callback:', error);
      }
    });
    this.cleanupCallbacks = [];

    // Disconnect all active nodes
    this.activeNodes.forEach(node => {
      try {
        node.disconnect();
      } catch (error) {
        console.error('Error disconnecting node:', error);
      }
    });
    this.activeNodes.clear();

    // Close audio context
    if (this.audioContext) {
      try {
        await this.audioContext.close();
        console.log('AudioContext closed');
      } catch (error) {
        console.error('Error closing AudioContext:', error);
      }
      this.audioContext = null;
    }

    this.isInitialized = false;
    this.initializationPromise = null;
  }

  /**
   * Setup event listeners for page lifecycle
   */
  private setupEventListeners(): void {
    // Handle page visibility changes
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.suspend().catch(console.error);
        } else {
          this.resume().catch(console.error);
        }
      });
    }

    // Handle page unload
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup().catch(console.error);
      });

      // Handle memory pressure
      if ('memory' in performance) {
        setInterval(() => {
          const memory = (performance as any).memory;
          if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
            console.warn('High memory usage detected, cleaning up audio resources');
            this.performMemoryCleanup();
          }
        }, 30000); // Check every 30 seconds
      }
    }
  }

  /**
   * Perform memory cleanup
   */
  private performMemoryCleanup(): void {
    // Disconnect unused nodes
    const nodesToRemove: AudioNode[] = [];
    this.activeNodes.forEach(node => {
      // Check if node is still connected (simplified check)
      try {
        // If we can't access the node, it's probably garbage collected
        node.numberOfInputs; // This will throw if node is invalid
      } catch {
        nodesToRemove.push(node);
      }
    });

    nodesToRemove.forEach(node => {
      this.activeNodes.delete(node);
    });

    console.log(`Cleaned up ${nodesToRemove.length} disconnected audio nodes`);
  }

  /**
   * Reset the manager (for testing)
   */
  static reset(): void {
    if (AudioContextManager.instance) {
      AudioContextManager.instance.cleanup();
      AudioContextManager.instance = null as any;
    }
  }
}

// Export singleton instance
export const audioContextManager = AudioContextManager.getInstance();

// React hook for using audio context
export function useAudioContext(config?: AudioContextConfig) {
  const [context, setContext] = React.useState<AudioContext | null>(null);
  const [isReady, setIsReady] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    let mounted = true;

    const initializeContext = async () => {
      try {
        const ctx = await audioContextManager.initialize(config);
        if (mounted) {
          setContext(ctx);
          setIsReady(true);
          setError(null);
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : String(err));
          setIsReady(false);
        }
      }
    };

    initializeContext();

    return () => {
      mounted = false;
    };
  }, []);

  return {
    context,
    isReady,
    error,
    ensureReady: audioContextManager.ensureReady.bind(audioContextManager),
    getMetrics: audioContextManager.getMetrics.bind(audioContextManager),
    isHealthy: audioContextManager.isHealthy.bind(audioContextManager)
  };
}

// Import React for the hook
import React from 'react';
