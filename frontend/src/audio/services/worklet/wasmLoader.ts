// wasmLoader.ts
// Utility to load a WASM module for use in AudioWorkletProcessor

export async function loadWasmModule(url: string): Promise<WebAssembly.Instance> {
  const response = await fetch(url);
  const buffer = await response.arrayBuffer();
  const module = await WebAssembly.compile(buffer);
  // For now, no imports; update as needed
  const instance = await WebAssembly.instantiate(module, {});
  return instance;
} 