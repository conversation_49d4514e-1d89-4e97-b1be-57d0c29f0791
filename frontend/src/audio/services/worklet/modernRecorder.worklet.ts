// modernRecorder.worklet.ts
// AudioWorkletProcessor for advanced audio processing (future WASM integration)
// Registers as 'modern-recorder-processor'

let wasmInstance: WebAssembly.Instance | null = null;

// @ts-ignore: AudioWorkletProcessor is available in the worklet global scope
class ModernRecorderProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    // @ts-ignore: 'port' exists on AudioWorkletProcessor in browser
    this.port.onmessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'wasm') {
        wasmInstance = event.data.instance;
        // Optionally call an init function if exported
        if (wasmInstance && wasmInstance.exports && wasmInstance.exports.init) {
          (wasmInstance.exports.init as Function)();
        }
      }
    };
  }

  process(inputs: Float32Array[][], outputs: Float32Array[][], _parameters: Record<string, Float32Array>) {
    const input = inputs[0];
    const output = outputs[0];
    if (input && output) {
      for (let channel = 0; channel < input.length; ++channel) {
        // If WASM is loaded and has a 'process' export, use it
        if (wasmInstance && wasmInstance.exports && wasmInstance.exports.process) {
          // Example: call WASM process (mocked, real implementation will differ)
          // (wasmInstance.exports.process as Function)(input[channel], output[channel], input[channel].length);
          // For now, just pass-through
          output[channel].set(input[channel]);
        } else {
          output[channel].set(input[channel]);
        }
      }
    }
    return true;
  }
}

// @ts-ignore: registerProcessor is available in the worklet global scope
declare function registerProcessor(name: string, processorCtor: typeof AudioWorkletProcessor): void;
registerProcessor('modern-recorder-processor', ModernRecorderProcessor);