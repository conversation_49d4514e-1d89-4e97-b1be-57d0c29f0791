import { useEffect, useState } from 'react';

export function useModernRecorderNode(audioContext: AudioContext | null) {
  const [node, setNode] = useState<AudioWorkletNode | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let cancelled = false;
    if (!audioContext) return;
    setLoading(true);
    setError(null);
    (async () => {
      try {
        await audioContext.audioWorklet.addModule('/lib/audioWorklet/modernRecorder.worklet.js');
        if (cancelled) return;
        const workletNode = new AudioWorkletNode(audioContext, 'modern-recorder-processor');
        setNode(workletNode);
      } catch (e: any) {
        setError(e?.message || 'Failed to load AudioWorklet. Make sure /public/lib/audioWorklet/modernRecorder.worklet.js exists.');
      } finally {
        setLoading(false);
      }
    })();
    return () => {
      cancelled = true;
      setNode(null);
    };
  }, [audioContext]);

  return { node, loading, error };
} 