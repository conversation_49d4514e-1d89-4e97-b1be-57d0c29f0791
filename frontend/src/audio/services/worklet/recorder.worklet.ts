// @ts-expect-error - AudioWorkletProcessor is available in worklet global scope
class RecorderProcessor extends AudioWorkletProcessor {
  // @ts-expect-error - port is available in AudioWorkletProcessor
  readonly port: MessagePort;
  constructor() {
    super();
  }

  process(inputs: <PERSON>loat32<PERSON>rray[][]) {
    const input = inputs[0];
    if (input && input[0]) {
      // Post the raw audio buffer to the main thread
      this.port.postMessage(input[0]);
    }
    return true;
  }
}

// registerProcessor is available in worklet global scope
// @ts-ignore
globalThis.registerProcessor("recorder-processor", RecorderProcessor);
