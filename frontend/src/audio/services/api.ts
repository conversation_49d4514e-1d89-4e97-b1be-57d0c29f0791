// Define UserType locally since we can't import it
type UserType = {
  id: string;
  email: string;
  username: string;
  premium: boolean;
  avatar_url?: string;
};

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";

// Interface not currently used but kept for future reference
// interface User {
//   id: string;
//   email: string;
//   username: string;
//   premium: boolean;
// }

interface Beat {
  id: number;
  title: string;
  genre: string;
  key: string;
  bpm: number;
  producer_name: string;
  is_free: boolean;
  audio_url: string;
}

interface FreestyleSession {
  id: string;
  beat_id: string;
  recording_url: string;
  duration: number;
  created_at: string;
}

// AI Suggestion interface removed

export interface ApiResponse<T> {
  data: T;
  message?: string;
  error?: string;
}

export interface Subscription {
  id: number;
  plan_type: string;
  status: string;
  starts_at: string;
  ends_at: string | null;
}

interface RequestOptions extends RequestInit {
  token?: string;
}

async function fetchWithAuth<T>(
  endpoint: string,
  options: RequestOptions = {},
): Promise<T> {
  let token = localStorage.getItem("token");
  let headers = {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  // Always include credentials for Rails/Devise compatibility
  let fetchOptions = {
    ...options,
    headers,
    credentials: "include" as const,
  };

  let response = await fetch(`${API_URL}${endpoint}`, fetchOptions);

  // If unauthorized, clear token and retry once unauthenticated
  if ((response.status === 401 || response.status === 403) && token) {
    localStorage.removeItem("token");
    headers = {
      ...headers,
    };
    delete headers["Authorization"];
    fetchOptions = {
      ...fetchOptions,
      headers,
    };
    response = await fetch(`${API_URL}${endpoint}`, fetchOptions);
  }

  if (!response.ok) {
    let error;
    try {
      error = await response.json();
    } catch (e) {
      throw new Error("API request failed and response is not JSON");
    }
    const message =
      error.message ||
      error.error ||
      error.detail ||
      JSON.stringify(error) ||
      "API request failed";
    throw new Error(message);
  }

  return response.json();
}

export const api = {
  async login(
    email: string,
    password: string,
  ): Promise<ApiResponse<{ token: string; user: UserType }>> {
    return fetchWithAuth("/auth/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });
  },

  async register(
    username: string,
    email: string,
    password: string,
  ): Promise<ApiResponse<{ token: string; user: UserType }>> {
    const response = await fetch(`${API_URL}/users`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        user: { username, email, password },
      }),
    });
    return response.json();
  },

  async logout(): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_URL}/users/sign_out`, {
      method: "DELETE",
      credentials: "include",
    });
    return response.json();
  },

  async getProfile(): Promise<ApiResponse<UserType>> {
    const response = await fetch(`${API_URL}/api/profile`, {
      credentials: "include",
    });
    return response.json();
  },

  async getBeats(queryParams?: string): Promise<ApiResponse<Beat[]>> {
    const cleanParams = queryParams ? queryParams.replace(/^[?]+/, "") : "";
    const url = cleanParams ? `/api/beats?${cleanParams}` : "/api/beats";
    const response = await fetchWithAuth<ApiResponse<any>>(url);
    // Flatten beats: move attributes to top level
    const flatData = (response.data || []).map((beat: any) => ({
      id: Number(beat.id),
      ...(beat.attributes || {}),
    }));
    return { ...response, data: flatData };
  },

  async getBeat(id: number): Promise<ApiResponse<Beat>> {
    return fetchWithAuth<ApiResponse<Beat>>(`/api/beats/${id}`);
  },

  async createFreestyleSession(
    beatId: number,
  ): Promise<ApiResponse<FreestyleSession>> {
    const response = await fetch(`${API_URL}/api/freestyle_sessions`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        beat_id: beatId
      }),
    });
    return response.json();
  },

  // AI Suggestion methods removed

  async getSubscription(): Promise<ApiResponse<Subscription>> {
    const response = await fetch(`${API_URL}/api/subscription`, {
      credentials: "include",
    });
    return response.json();
  },

  async createSubscription(data: {
    plan_type: string;
  }): Promise<ApiResponse<Subscription>> {
    const response = await fetch(`${API_URL}/api/subscription`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        subscription: data,
      }),
    });
    return response.json();
  },

  async cancelSubscription(id: number): Promise<ApiResponse<Subscription>> {
    const response = await fetch(`${API_URL}/api/subscription/${id}`, {
      method: "DELETE",
      credentials: "include",
    });
    return response.json();
  },

  // Push notification endpoints
  getPushPublicKey(): Promise<ApiResponse<string>> {
    return fetchWithAuth<ApiResponse<string>>("/api/push/vapid-public-key", {
      method: "GET",
    });
  },

  subscribePush(subscription: PushSubscription): Promise<ApiResponse<void>> {
    return fetchWithAuth<ApiResponse<void>>("/api/push/subscribe", {
      method: "POST",
      body: JSON.stringify(subscription),
    });
  },

  unsubscribePush(subscription: PushSubscription): Promise<ApiResponse<void>> {
    return fetchWithAuth<ApiResponse<void>>("/api/push/unsubscribe", {
      method: "POST",
      body: JSON.stringify(subscription),
    });
  },

  async updateFreestyleSession(
    sessionId: number,
    data: { beat_id: number },
  ): Promise<ApiResponse<FreestyleSession>> {
    const response = await fetch(
      `${API_URL}/api/freestyle_sessions/${sessionId}`,
      {
        method: "PATCH",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ freestyle_session: data }),
      },
    );
    return response.json();
  },

  async uploadSessionRecording(
    sessionId: number,
    audioBlob: Blob,
  ): Promise<ApiResponse<FreestyleSession>> {
    const formData = new FormData();
    formData.append(
      "freestyle_session[recording]",
      audioBlob,
      "recording.webm",
    );
    const response = await fetch(
      `${API_URL}/api/freestyle_sessions/${sessionId}`,
      {
        method: "PUT",
        credentials: "include",
        body: formData,
      },
    );
    return response.json();
  },

  async getFreestyleSessions(): Promise<ApiResponse<FreestyleSession[]>> {
    const response = await fetchWithAuth<ApiResponse<FreestyleSession[]>>(
      "/api/freestyle_sessions",
    );
    return response;
  },

  async exportRecording(
    sessionId: number,
    format: string,
    audioBlob: Blob,
  ): Promise<ApiResponse<{ downloadUrl: string }>> {
    const formData = new FormData();
    formData.append(
      "freestyle_session[recording]",
      audioBlob,
      `recording.${format}`,
    );
    formData.append("format", format);

    const response = await fetch(
      `${API_URL}/api/freestyle_sessions/${sessionId}/export`,
      {
        method: "POST",
        credentials: "include",
        body: formData,
      },
    );
    return response.json();
  },
};

export async function fetchApi<T>(
  endpoint: string,
  options?: RequestInit,
): Promise<ApiResponse<T>> {
  const response = await fetch(`/api${endpoint}`, {
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "API request failed");
  }

  return response.json();
}

// Example usage:
// const response = await fetchApi<Beat[]>('/beats');
// const beats = response.data;
