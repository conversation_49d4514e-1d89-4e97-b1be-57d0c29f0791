/**
 * Audio Services
 *
 * This module exports audio services for the freestyle app.
 * All audio processing is now handled by Web Workers for improved performance.
 */

// Web Worker-based services (primary implementation)
export {
  initAudioWorker,
  getAudioWorker,
  terminateAudioWorker,
  processRecordingForPreview,
  exportRecording
} from './audioWorkerService';
export type { AudioProcessorWorker } from './audioWorkerService';

// Recording services
export { SyncAudioRecorder } from './syncAudioRecorder';
export type { RecordingResult, RecorderState, SyncAudioRecorderOptions } from './syncAudioRecorder';
