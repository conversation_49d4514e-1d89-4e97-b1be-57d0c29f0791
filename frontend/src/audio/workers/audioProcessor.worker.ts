/**
 * Audio Processor Web Worker
 * 
 * This worker handles CPU-intensive audio processing tasks off the main thread:
 * - Decoding audio files
 * - Mixing audio buffers
 * - Converting to WAV format
 * - Applying audio effects
 * 
 * Using a Web Worker prevents UI jank during heavy audio processing.
 */

// Import audio processing utilities
import * as Comlink from 'comlink';

// Define the worker interface
interface AudioProcessorWorker {
  decodeAudio(arrayBuffer: ArrayBuffer): Promise<AudioBuffer>;
  mixAudioBuffers(recordingBuffer: AudioBuffer, beatBuffer: AudioBuffer, recordingGain: number, beatGain: number): Promise<AudioBuffer>;
  applyCompression(buffer: AudioBuffer, threshold: number, ratio: number): Promise<AudioBuffer>;
  applyEQ(buffer: AudioBuffer, lowGain: number, midGain: number, highGain: number): Promise<AudioBuffer>;
  audioBufferToWav(buffer: AudioBuffer): Promise<Blob>;
  processRecordingForPreview(recordingArrayBuffer: ArrayBuffer, beatArrayBuffer: ArrayBuffer, recordingGain: number, beatGain: number): Promise<{
    mixedBuffer: AudioBuffer;
    mixedBlob: Blob;
    duration: number;
  }>;
}

// Create an AudioContext for processing
let audioContext: AudioContext | null = null;

// Helper function to get or create AudioContext
function getAudioContext(): AudioContext {
  if (!audioContext) {
    audioContext = new AudioContext({
      latencyHint: 'interactive',
      sampleRate: 48000
    });
  }
  return audioContext;
}

// Helper function to write a string to a DataView
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

// Implementation of the worker
const audioProcessorWorker: AudioProcessorWorker = {
  // Decode audio data from an ArrayBuffer
  async decodeAudio(arrayBuffer: ArrayBuffer): Promise<AudioBuffer> {
    try {
      const context = getAudioContext();
      const audioBuffer = await context.decodeAudioData(arrayBuffer.slice(0));
      
      // Log buffer details
      console.log("[Worker] Decoded audio buffer:", {
        duration: audioBuffer.duration,
        numberOfChannels: audioBuffer.numberOfChannels,
        sampleRate: audioBuffer.sampleRate,
        length: audioBuffer.length
      });
      
      return audioBuffer;
    } catch (e) {
      console.error("[Worker] Error decoding audio:", e);
      throw e;
    }
  },
  
  // Mix two audio buffers with gain control
  async mixAudioBuffers(recordingBuffer: AudioBuffer, beatBuffer: AudioBuffer, recordingGain: number, beatGain: number): Promise<AudioBuffer> {
    try {
      console.log("[Worker] Mixing audio buffers:", {
        recordingDuration: recordingBuffer.duration,
        beatDuration: beatBuffer.duration,
        recordingChannels: recordingBuffer.numberOfChannels,
        beatChannels: beatBuffer.numberOfChannels
      });
      
      const context = getAudioContext();
      
      // Create a new buffer with the length of the longer buffer
      const mixedBuffer = context.createBuffer(
        Math.max(recordingBuffer.numberOfChannels, beatBuffer.numberOfChannels),
        Math.max(recordingBuffer.length, beatBuffer.length),
        recordingBuffer.sampleRate
      );
      
      // Copy recording data with gain
      for (let channel = 0; channel < recordingBuffer.numberOfChannels; channel++) {
        const mixedChannelData = mixedBuffer.getChannelData(channel);
        const recordingChannelData = recordingBuffer.getChannelData(channel);
        
        // Add recording data with gain
        for (let i = 0; i < recordingChannelData.length; i++) {
          mixedChannelData[i] = recordingChannelData[i] * recordingGain;
        }
      }
      
      // Mix the beat
      for (let channel = 0; channel < beatBuffer.numberOfChannels; channel++) {
        const mixedChannelData = mixedBuffer.getChannelData(channel);
        const beatChannelData = beatBuffer.getChannelData(channel);
        
        // Add beat data with gain
        for (let i = 0; i < beatChannelData.length; i++) {
          if (i < mixedChannelData.length) {
            mixedChannelData[i] += beatChannelData[i] * beatGain;
          }
        }
      }
      
      // Normalize to prevent clipping
      let maxPeak = 0;
      
      // Find the maximum peak
      for (let channel = 0; channel < mixedBuffer.numberOfChannels; channel++) {
        const channelData = mixedBuffer.getChannelData(channel);
        for (let i = 0; i < channelData.length; i++) {
          maxPeak = Math.max(maxPeak, Math.abs(channelData[i]));
        }
      }
      
      // Only normalize if the peak is too high
      if (maxPeak > 0.9) {
        const gainFactor = 0.9 / maxPeak;
        console.log("[Worker] Normalizing with gain factor:", gainFactor);
        
        for (let channel = 0; channel < mixedBuffer.numberOfChannels; channel++) {
          const channelData = mixedBuffer.getChannelData(channel);
          for (let i = 0; i < channelData.length; i++) {
            channelData[i] *= gainFactor;
          }
        }
      } else {
        console.log("[Worker] Peak amplitude", maxPeak, "is already below target 0.9");
      }
      
      console.log("[Worker] Created mixed buffer:", {
        duration: mixedBuffer.duration,
        numberOfChannels: mixedBuffer.numberOfChannels,
        sampleRate: mixedBuffer.sampleRate,
        length: mixedBuffer.length
      });
      
      return mixedBuffer;
    } catch (e) {
      console.error("[Worker] Error mixing audio buffers:", e);
      throw e;
    }
  },
  
  // Apply compression to an audio buffer
  async applyCompression(buffer: AudioBuffer, threshold: number, ratio: number): Promise<AudioBuffer> {
    try {
      const context = getAudioContext();
      const outputBuffer = context.createBuffer(
        buffer.numberOfChannels,
        buffer.length,
        buffer.sampleRate
      );
      
      // Process each channel
      for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
        const inputData = buffer.getChannelData(channel);
        const outputData = outputBuffer.getChannelData(channel);
        
        // Apply compression
        for (let i = 0; i < inputData.length; i++) {
          const input = inputData[i];
          const inputAbs = Math.abs(input);
          
          if (inputAbs > threshold) {
            // Compressed part
            const gain = threshold + (inputAbs - threshold) / ratio;
            outputData[i] = input > 0 ? gain : -gain;
          } else {
            // Uncompressed part
            outputData[i] = input;
          }
        }
      }
      
      return outputBuffer;
    } catch (e) {
      console.error("[Worker] Error applying compression:", e);
      throw e;
    }
  },
  
  // Apply simple 3-band EQ
  async applyEQ(buffer: AudioBuffer, lowGain: number, midGain: number, highGain: number): Promise<AudioBuffer> {
    // Simple implementation - in a real app, this would use proper filters
    // This is a placeholder that just adjusts the gain
    try {
      const context = getAudioContext();
      const outputBuffer = context.createBuffer(
        buffer.numberOfChannels,
        buffer.length,
        buffer.sampleRate
      );
      
      // Just apply overall gain for now (simplified)
      const overallGain = (lowGain + midGain + highGain) / 3;
      
      // Process each channel
      for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
        const inputData = buffer.getChannelData(channel);
        const outputData = outputBuffer.getChannelData(channel);
        
        for (let i = 0; i < inputData.length; i++) {
          outputData[i] = inputData[i] * overallGain;
        }
      }
      
      return outputBuffer;
    } catch (e) {
      console.error("[Worker] Error applying EQ:", e);
      throw e;
    }
  },
  
  // Convert AudioBuffer to WAV Blob
  async audioBufferToWav(buffer: AudioBuffer): Promise<Blob> {
    try {
      console.log("[Worker] Converting AudioBuffer to WAV");
      
      const numChannels = buffer.numberOfChannels;
      const sampleRate = buffer.sampleRate;
      const length = buffer.length * numChannels * 2; // 16-bit samples
      
      // Create WAV header
      const view = new DataView(new ArrayBuffer(44 + length));
      
      // "RIFF" chunk descriptor
      writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + length, true);
      writeString(view, 8, 'WAVE');
      
      // "fmt " sub-chunk
      writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true); // subchunk size
      view.setUint16(20, 1, true); // PCM format
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * 2, true); // byte rate
      view.setUint16(32, numChannels * 2, true); // block align
      view.setUint16(34, 16, true); // bits per sample
      
      // "data" sub-chunk
      writeString(view, 36, 'data');
      view.setUint32(40, length, true);
      
      // Write audio data
      let offset = 44;
      for (let i = 0; i < buffer.length; i++) {
        for (let channel = 0; channel < numChannels; channel++) {
          const sample = buffer.getChannelData(channel)[i];
          const value = Math.max(-1, Math.min(1, sample)); // Clamp
          const int16 = value < 0 ? value * 32768 : value * 32767; // Convert to 16-bit
          view.setInt16(offset, int16, true);
          offset += 2;
        }
      }
      
      // Create blob
      const blob = new Blob([view], { type: 'audio/wav' });
      
      console.log("[Worker] Created WAV blob:", { size: blob.size });
      
      return blob;
    } catch (e) {
      console.error("[Worker] Error converting AudioBuffer to WAV:", e);
      throw e;
    }
  },
  
  // Process recording for preview
  async processRecordingForPreview(
    recordingArrayBuffer: ArrayBuffer,
    beatArrayBuffer: ArrayBuffer,
    recordingGain: number = 1.0,
    beatGain: number = 1.0
  ): Promise<{
    mixedBuffer: AudioBuffer;
    mixedBlob: Blob;
    duration: number;
  }> {
    try {
      console.log("[Worker] Processing recording for preview with gains:", {
        recordingGain,
        beatGain,
        recordingBufferSize: recordingArrayBuffer.byteLength,
        beatBufferSize: beatArrayBuffer.byteLength
      });
      
      // Decode recording
      const recordingBuffer = await this.decodeAudio(recordingArrayBuffer);
      
      // Decode beat
      const beatBuffer = await this.decodeAudio(beatArrayBuffer);
      
      // Mix audio with the specified gain values
      const mixedBuffer = await this.mixAudioBuffers(recordingBuffer, beatBuffer, recordingGain, beatGain);
      
      // Apply compression for better sound
      const compressedBuffer = await this.applyCompression(mixedBuffer, 0.5, 4);
      
      // Convert to WAV
      const mixedBlob = await this.audioBufferToWav(compressedBuffer);
      
      return {
        mixedBuffer: compressedBuffer,
        mixedBlob,
        duration: compressedBuffer.duration
      };
    } catch (e) {
      console.error("[Worker] Error processing recording for preview:", e);
      throw e;
    }
  }
};

// Export the worker with Comlink
Comlink.expose(audioProcessorWorker);

// Notify that the worker is ready
console.log("[Worker] Audio processor worker initialized");
