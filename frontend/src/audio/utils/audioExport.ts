/**
 * Audio Export Utilities
 * 
 * This module provides functions for exporting audio data:
 * - Exporting blobs as downloadable files
 * - Generating filenames
 * - Handling different audio formats
 * 
 * These utilities are used by the PreviewPlayer component.
 */

/**
 * Export a blob as a downloadable file
 * @param blob The blob to export
 * @param filename The filename to use
 */
export function exportBlob(blob: Blob, filename: string): void {
  try {
    // Create a URL for the blob
    const url = URL.createObjectURL(blob);
    
    // Create a download link
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    
    // Add to document and click
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);
    
    console.log(`Exported blob as ${filename}`);
  } catch (e) {
    console.error("Error exporting blob:", e);
    alert(`Error exporting audio: ${e}`);
  }
}

/**
 * Generate a filename for an exported recording
 * @param prefix Optional prefix for the filename
 * @param extension File extension (default: 'wav')
 * @returns A filename with the current date and time
 */
export function generateFilename(prefix: string = 'freestyle', extension: string = 'wav'): string {
  const now = new Date();
  
  // Format date as YYYY-MM-DD_HH-MM-SS
  const date = now.toISOString()
    .replace(/T/, '_')
    .replace(/\..+/, '')
    .replace(/:/g, '-');
  
  return `${prefix}_${date}.${extension}`;
}

/**
 * Get the appropriate file extension for a MIME type
 * @param mimeType The MIME type
 * @returns The file extension
 */
export function getExtensionForMimeType(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    'audio/wav': 'wav',
    'audio/x-wav': 'wav',
    'audio/wave': 'wav',
    'audio/webm': 'webm',
    'audio/ogg': 'ogg',
    'audio/mp4': 'm4a',
    'audio/mpeg': 'mp3',
    'audio/mp3': 'mp3'
  };
  
  return mimeToExt[mimeType] || 'wav';
}

/**
 * Export a recording with appropriate filename
 * @param blob The recording blob
 * @param beatTitle Optional beat title to include in the filename
 */
export function exportRecording(blob: Blob, beatTitle?: string): void {
  try {
    // Generate a filename
    const prefix = beatTitle ? `freestyle_${beatTitle.replace(/\s+/g, '_')}` : 'freestyle';
    const extension = getExtensionForMimeType(blob.type);
    const filename = generateFilename(prefix, extension);
    
    // Export the blob
    exportBlob(blob, filename);
  } catch (e) {
    console.error("Error exporting recording:", e);
    alert(`Error exporting recording: ${e}`);
  }
}
