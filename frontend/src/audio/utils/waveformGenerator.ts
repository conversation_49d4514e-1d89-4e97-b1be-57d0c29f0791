/**
 * Waveform Generator Utilities
 *
 * This module provides functions for generating waveform data from various sources:
 * - AudioBuffer objects
 * - Audio URLs (string or Blob)
 * - Dummy waveforms for fallback
 *
 * These utilities are the single source of truth for waveform generation in the app.
 * They are used by the useWaveform hook and all waveform visualization components.
 *
 * The functions handle:
 * - Extracting peak data from audio sources
 * - Normalizing peaks to ensure consistent visualization
 * - Enhancing peaks for better visual appearance
 * - Providing fallbacks for error cases
 */

/**
 * Generate peaks from an AudioBuffer
 * @param buffer The AudioBuffer to process
 * @param numPoints The number of points to generate
 * @returns Float32Array of peak values
 */
/**
 * Generate peaks from an AudioBuffer with normalization and enhancement
 * @param buffer The AudioBuffer to process
 * @param numPoints The number of points to generate
 * @param options Optional configuration for peak generation
 * @returns Float32Array of peak values
 */
export function generatePeaksFromBuffer(
  buffer: AudioBuffer,
  numPoints: number,
  options: {
    enhanceFactor?: number; // Power curve exponent (0.9 = mild, 0.5 = aggressive)
    prioritizeVocals?: boolean; // Whether to enhance for vocal frequencies
  } = {}
): Float32Array {
  // Default options
  const {
    enhanceFactor = 0.9, // Default to mild enhancement
    prioritizeVocals = false
  } = options;

  // Validate the buffer before processing
  if (!buffer || !buffer.length || buffer.length <= 0 || !buffer.numberOfChannels || buffer.numberOfChannels <= 0) {
    console.warn("Invalid buffer provided for waveform generation:", {
      hasBuffer: !!buffer,
      length: buffer?.length,
      channels: buffer?.numberOfChannels,
      duration: buffer?.duration
    });
    return generateDummyPeaks(numPoints);
  }

  // Check if the buffer contains any non-zero data
  let hasNonZeroData = false;
  let totalSamples = 0;
  let nonZeroSamples = 0;

  for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
    const channelData = buffer.getChannelData(channel);
    // Check a sample of points for non-zero values
    for (let i = 0; i < channelData.length; i += Math.max(1, Math.floor(channelData.length / 1000))) {
      totalSamples++;
      if (Math.abs(channelData[i]) > 0.0001) {
        nonZeroSamples++;
        if (nonZeroSamples > 10) { // Only need a few non-zero samples to confirm
          hasNonZeroData = true;
          break;
        }
      }
    }
    if (hasNonZeroData) break;
  }

  // Log detailed information about the buffer
  console.log("Audio buffer analysis:", {
    totalSamples,
    nonZeroSamples,
    hasNonZeroData,
    nonZeroRatio: nonZeroSamples / totalSamples,
    channels: buffer.numberOfChannels,
    length: buffer.length,
    duration: buffer.duration
  });

  if (!hasNonZeroData) {
    console.warn("Generated all-zero peaks, creating dummy waveform");

    // Create dummy peaks with a nice waveform pattern
    const dummyPeaks = new Float32Array(numPoints);

    // Create a more interesting waveform pattern
    for (let i = 0; i < numPoints; i++) {
      const progress = i / numPoints;

      // Combine multiple sine waves for a more natural look
      dummyPeaks[i] =
        Math.sin(progress * Math.PI * 10) * 0.5 +
        Math.sin(progress * Math.PI * 20) * 0.25 +
        Math.sin(progress * Math.PI * 5) * 0.15;
    }

    return dummyPeaks;
  }

  console.log("Generating peaks from AudioBuffer", {
    bufferLength: buffer.length,
    numChannels: buffer.numberOfChannels,
    sampleRate: buffer.sampleRate,
    duration: buffer.duration,
    numPoints,
    enhanceFactor,
    prioritizeVocals
  });

  try {
    // Get the first channel data (left channel)
    const channelData = buffer.getChannelData(0);

    // Validate channel data
    if (!channelData || channelData.length <= 0) {
      console.warn("Invalid channel data in buffer");
      return generateDummyPeaks(numPoints);
    }

    const peaks = new Float32Array(numPoints);

    // Ensure we have a valid blockSize
    const blockSize = Math.max(1, Math.floor(channelData.length / numPoints));

    // First pass: extract raw peaks
    for (let i = 0; i < numPoints; i++) {
      const start = i * blockSize;
      const end = Math.min(start + blockSize, channelData.length);

      // Skip if we have an invalid range
      if (start >= channelData.length || end <= start) {
        peaks[i] = 0;
        continue;
      }

      // Find the min and max values in this block
      let min = 1.0;
      let max = -1.0;

      for (let j = start; j < end; j++) {
        const value = channelData[j];
        if (value < min) min = value;
        if (value > max) max = value;
      }

      // Use the larger of |min| or |max| for a more DAW-like look
      peaks[i] = Math.abs(min) > Math.abs(max) ? min : max;
    }

    // Validate the generated peaks
    let hasNonZero = false;
    for (let i = 0; i < peaks.length; i++) {
      if (peaks[i] !== 0) {
        hasNonZero = true;
        break;
      }
    }

    // If all peaks are zero, generate dummy peaks instead
    if (!hasNonZero) {
      console.warn("Generated all-zero peaks, falling back to dummy peaks");
      return generateDummyPeaks(numPoints);
    }

    // Second pass: find the global maximum to normalize
    let maxPeak = 0;
    for (let i = 0; i < numPoints; i++) {
      maxPeak = Math.max(maxPeak, Math.abs(peaks[i]));
    }

    // Third pass: normalize and enhance
    if (maxPeak > 0) {
      for (let i = 0; i < numPoints; i++) {
        const sign = Math.sign(peaks[i]);
        const normalized = Math.abs(peaks[i]) / maxPeak;

        // Apply a power curve to enhance smaller values
        // Using the provided enhanceFactor (closer to 1.0 = more natural)
        const enhanced = Math.pow(normalized, enhanceFactor);

        // Store the normalized value
        peaks[i] = sign * enhanced;

        // Additional enhancement for vocals if requested
        if (prioritizeVocals) {
          // Boost the amplitude slightly for a fuller appearance
          peaks[i] *= 1.15;
        }
      }
    }

    return peaks;
  } catch (error) {
    console.error("Error processing buffer:", error);
    // Return a fallback waveform
    return generateDummyPeaks(numPoints);
  }
}

/**
 * Generate peaks from an audio URL (string or Blob)
 * @param audioUrl The URL or Blob to process
 * @param numPoints The number of points to generate
 * @param options Optional configuration for peak generation
 * @returns Float32Array of peak values
 */
export async function generatePeaksFromUrl(
  audioUrl: string | Blob | null,
  numPoints: number,
  options: {
    enhanceFactor?: number; // Power curve exponent (0.9 = mild, 0.5 = aggressive)
    prioritizeVocals?: boolean; // Whether to enhance for vocal frequencies
    cacheKey?: string; // Optional key for caching results
  } = {}
): Promise<Float32Array> {
  // Check for server-side rendering
  if (typeof window === 'undefined') {
    console.warn("Running in server environment, returning dummy waveform");
    return generateDummyPeaks(numPoints);
  }

  if (!audioUrl) {
    console.warn("No audioUrl provided to waveform, returning dummy waveform");
    return generateDummyPeaks(numPoints);
  }

  // Check cache if a cacheKey is provided
  if (options.cacheKey && typeof window !== 'undefined' && (window as any).__waveformCache) {
    const cachedPeaks = (window as any).__waveformCache[options.cacheKey];
    if (cachedPeaks) {
      console.log("Using cached waveform data for:", options.cacheKey);
      return cachedPeaks;
    }
  }

  try {
    const ctx = new (window.AudioContext || (window as any).webkitAudioContext)();

    // Fetch the audio data
    const fetchAudio = typeof audioUrl === "string"
      ? fetch(audioUrl).then((r) => {
          if (!r.ok)
            throw new Error(`Failed to fetch audio: ${r.status} ${r.statusText}`);
          return r.arrayBuffer();
        })
      : (audioUrl as Blob).arrayBuffer();

    // Decode the audio data
    const arrayBuffer = await fetchAudio;
    const audioBuffer = await ctx.decodeAudioData(arrayBuffer);

    // Generate peaks from the buffer with the provided options
    const peaks = generatePeaksFromBuffer(audioBuffer, numPoints, options);

    // Validate the peaks - ensure they're not all zeros
    let hasNonZeroPeaks = false;
    for (let i = 0; i < peaks.length; i++) {
      if (Math.abs(peaks[i]) > 0.0001) {
        hasNonZeroPeaks = true;
        break;
      }
    }

    // If peaks are all zeros, generate dummy peaks
    const finalPeaks = hasNonZeroPeaks ? peaks : generateDummyPeaks(numPoints, { enhanceFactor: 0.8 });

    // Cache the result if a cacheKey is provided
    if (options.cacheKey && typeof window !== 'undefined') {
      if (!(window as any).__waveformCache) {
        (window as any).__waveformCache = {};
      }
      (window as any).__waveformCache[options.cacheKey] = finalPeaks;
    }

    // Close the audio context
    ctx.close();

    return finalPeaks;
  } catch (error) {
    console.error("Error generating peaks from URL:", error);
    return generateDummyPeaks(numPoints);
  }
}

/**
 * Generate dummy waveform data for fallback
 * @param numPoints The number of points to generate
 * @param options Optional configuration for dummy peak generation
 * @returns Float32Array of dummy peak values
 */
export function generateDummyPeaks(
  numPoints: number,
  options: {
    enhanceFactor?: number; // Power curve exponent (0.9 = mild, 0.5 = aggressive)
    addNoise?: boolean; // Whether to add random noise for a more realistic look
  } = {}
): Float32Array {
  // Default options - always use high-quality settings
  const {
    enhanceFactor = 0.7, // More aggressive enhancement for better visibility
    addNoise = true
  } = options;

  console.log("Generating high-quality dummy waveform with", numPoints, "points");

  // Ensure we have enough points for a high-quality waveform
  const actualNumPoints = Math.max(numPoints, 2000);
  const peaks = new Float32Array(actualNumPoints);

  // Create a more natural-looking waveform with some variation
  for (let i = 0; i < actualNumPoints; i++) {
    const progress = i / actualNumPoints;

    // Combine multiple sine waves with different frequencies
    // This creates a more natural-looking waveform
    const value =
      Math.sin(progress * Math.PI * 10) * 0.6 +
      Math.sin(progress * Math.PI * 20) * 0.3 +
      Math.sin(progress * Math.PI * 5) * 0.2 +
      Math.sin(progress * Math.PI * 30) * 0.15;

    // Add some randomness if requested
    const noise = addNoise ? (Math.random() * 2 - 1) * 0.08 : 0;

    // Ensure we have at least one peak that reaches close to 1.0
    // by adding a single high peak near the middle
    const peakBoost = Math.abs(progress - 0.5) < 0.05 ?
      (1 - Math.abs(progress - 0.5) * 20) * 0.6 : 0;

    // Add some additional peaks throughout
    const additionalPeaks =
      (Math.abs(progress - 0.25) < 0.03 ? (1 - Math.abs(progress - 0.25) * 30) * 0.4 : 0) +
      (Math.abs(progress - 0.75) < 0.03 ? (1 - Math.abs(progress - 0.75) * 30) * 0.4 : 0);

    // Combine all components
    peaks[i] = value + noise + peakBoost + additionalPeaks;

    // Add some envelope shaping - quieter at start and end
    const envelope = Math.sin(progress * Math.PI);
    peaks[i] *= envelope;
  }

  // Normalize the peaks to ensure maximum values are exactly 1.0
  let maxPeak = 0;
  for (let i = 0; i < actualNumPoints; i++) {
    maxPeak = Math.max(maxPeak, Math.abs(peaks[i]));
  }

  if (maxPeak > 0) {
    for (let i = 0; i < actualNumPoints; i++) {
      const sign = Math.sign(peaks[i]);
      const normalized = Math.abs(peaks[i]) / maxPeak;

      // Apply enhancement if requested
      const enhanced = enhanceFactor !== 1.0 ?
        Math.pow(normalized, enhanceFactor) : normalized;

      peaks[i] = sign * enhanced;
    }
  }

  return peaks;
}
