/**
 * AudioEncoder
 * 
 * A utility class for encoding audio data to various formats.
 * This is used as a fallback when the Web Worker audio processing fails.
 */

export class AudioEncoder {
  /**
   * Encode an AudioBuffer to WAV format
   * @param buffer The AudioBuffer to encode
   * @returns A Blob containing the WAV data
   */
  async encodeWAV(buffer: AudioBuffer): Promise<Blob> {
    try {
      // Get buffer properties
      const numChannels = buffer.numberOfChannels;
      const sampleRate = buffer.sampleRate;
      const bytesPerSample = 2; // 16-bit
      const length = buffer.length * numChannels * bytesPerSample;

      // Create buffer with WAV header
      const arrayBuffer = new ArrayBuffer(44 + length);
      const view = new DataView(arrayBuffer);

      // Write WAV header
      // "RIFF" chunk
      this.writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + length, true);
      this.writeString(view, 8, 'WAVE');

      // "fmt " chunk
      this.writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true); // PCM format
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * bytesPerSample, true);
      view.setUint16(32, numChannels * bytesPerSample, true);
      view.setUint16(34, 8 * bytesPerSample, true);

      // "data" chunk
      this.writeString(view, 36, 'data');
      view.setUint32(40, length, true);

      // Write audio data
      let offset = 44;
      for (let i = 0; i < buffer.length; i++) {
        for (let channel = 0; channel < numChannels; channel++) {
          const sample = buffer.getChannelData(channel)[i];
          const value = Math.max(-1, Math.min(1, sample));
          const int16 = value < 0 ? value * 32768 : value * 32767;
          view.setInt16(offset, int16, true);
          offset += bytesPerSample;
        }
      }

      // Create blob
      return new Blob([view], { type: 'audio/wav' });
    } catch (error) {
      console.error("Error encoding WAV:", error);
      // Return an empty blob as fallback
      return new Blob([], { type: 'audio/wav' });
    }
  }

  /**
   * Helper function to write a string to a DataView
   */
  private writeString(view: DataView, offset: number, string: string): void {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }
}

// Add to window for global access
if (typeof window !== 'undefined') {
  (window as any).AudioEncoder = AudioEncoder;
}

export default AudioEncoder;
