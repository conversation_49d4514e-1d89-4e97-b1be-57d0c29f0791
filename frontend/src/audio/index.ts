/**
 * Audio Module Index
 *
 * This file exports all the components, hooks, services, and utilities
 * from the audio module for easy importing elsewhere in the application.
 */

// Components
export * from './components';

// Additional components not in the index
export { RecordingSystem } from './components/RecordingSystem';
export { TransportControls } from './components/TransportControls';

// Hooks
export * from './hooks';

// Additional hooks not in the index
export { useAudioPlayback } from './hooks/useAudioPlayback';
// useAudioRecording has been replaced by useSyncAudioRecording
export { useSyncAudioRecording } from './hooks/useSyncAudioRecording';

// Services
export {
  initAudioWorker,
  getAudioWorker,
  terminateAudioWorker,
  processRecordingForPreview,
  exportRecording
} from './services/audioWorkerService';

// AudioRecorder has been replaced by SyncAudioRecorder
export { SyncAudioRecorder } from './services/syncAudioRecorder';

// Utils
export * from './utils';

// Additional utils not in the index
export {
  exportBlob,
  generateFilename,
  getExtensionForMimeType,
  exportRecording
} from './utils/audioExport';
