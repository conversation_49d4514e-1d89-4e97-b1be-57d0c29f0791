/**
 * Audio Worker Service Tests
 * 
 * Comprehensive test suite for the audio worker service including:
 * - Worker initialization
 * - Audio processing operations
 * - Error handling
 * - Performance monitoring
 * - Memory management
 */

import { jest } from '@jest/globals';
import {
  initAudioWorker,
  getAudioWorker,
  terminateAudioWorker,
  processRecordingForPreview,
  exportRecording
} from '../services/audioWorkerService';

// Mock Comlink
jest.mock('comlink', () => ({
  wrap: jest.fn(),
  expose: jest.fn()
}));

// Mock performance monitor
jest.mock('../../utils/performance', () => ({
  performanceMonitor: {
    measureAsync: jest.fn((name, fn) => fn()),
    recordMetric: jest.fn()
  }
}));

// Mock Worker
global.Worker = jest.fn().mockImplementation(() => ({
  postMessage: jest.fn(),
  terminate: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn()
}));

describe('Audio Worker Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset worker instance
    terminateAudioWorker();
  });

  afterEach(() => {
    terminateAudioWorker();
  });

  describe('initAudioWorker', () => {
    it('should initialize worker successfully', async () => {
      const mockWorkerApi = {
        decodeAudio: jest.fn(),
        mixAudio: jest.fn(),
        generateWaveform: jest.fn()
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const worker = await initAudioWorker();
      
      expect(Worker).toHaveBeenCalledWith('/audioProcessor.worker.js');
      expect(wrap).toHaveBeenCalled();
      expect(worker).toBe(mockWorkerApi);
    });

    it('should return existing worker if already initialized', async () => {
      const mockWorkerApi = {
        decodeAudio: jest.fn(),
        mixAudio: jest.fn(),
        generateWaveform: jest.fn()
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const worker1 = await initAudioWorker();
      const worker2 = await initAudioWorker();
      
      expect(worker1).toBe(worker2);
      expect(Worker).toHaveBeenCalledTimes(1);
    });

    it('should handle initialization errors', async () => {
      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockImplementation(() => {
        throw new Error('Worker initialization failed');
      });

      await expect(initAudioWorker()).rejects.toThrow('Failed to initialize audio processing system');
    });
  });

  describe('getAudioWorker', () => {
    it('should return existing worker', async () => {
      const mockWorkerApi = {
        decodeAudio: jest.fn(),
        mixAudio: jest.fn(),
        generateWaveform: jest.fn()
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      await initAudioWorker();
      const worker = await getAudioWorker();
      
      expect(worker).toBe(mockWorkerApi);
    });

    it('should initialize worker if not exists', async () => {
      const mockWorkerApi = {
        decodeAudio: jest.fn(),
        mixAudio: jest.fn(),
        generateWaveform: jest.fn()
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const worker = await getAudioWorker();
      
      expect(worker).toBe(mockWorkerApi);
      expect(Worker).toHaveBeenCalledWith('/audioProcessor.worker.js');
    });
  });

  describe('terminateAudioWorker', () => {
    it('should terminate worker properly', async () => {
      const mockWorker = {
        terminate: jest.fn(),
        postMessage: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
      };

      (Worker as jest.Mock).mockReturnValue(mockWorker);

      const mockWorkerApi = {
        decodeAudio: jest.fn(),
        mixAudio: jest.fn(),
        generateWaveform: jest.fn()
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      await initAudioWorker();
      terminateAudioWorker();
      
      expect(mockWorker.terminate).toHaveBeenCalled();
    });

    it('should handle termination when no worker exists', () => {
      expect(() => terminateAudioWorker()).not.toThrow();
    });
  });

  describe('processRecordingForPreview', () => {
    it('should process recording successfully', async () => {
      const mockWorkerApi = {
        processRecordingForPreview: jest.fn().mockResolvedValue({
          mixedBuffer: new ArrayBuffer(1024),
          mixedBlob: new Blob(['test']),
          duration: 10,
          waveformData: [[0.1, 0.2], [0.3, 0.4]],
          levels: { rms: 0.5, db: -6, peak: 0.8 }
        })
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const recordingBlob = new Blob(['recording'], { type: 'audio/wav' });
      const beatUrl = 'http://example.com/beat.mp3';

      const result = await processRecordingForPreview(recordingBlob, beatUrl);
      
      expect(result).toBeDefined();
      expect(result.duration).toBe(10);
      expect(result.waveformData).toEqual([[0.1, 0.2], [0.3, 0.4]]);
    });

    it('should handle processing errors', async () => {
      const mockWorkerApi = {
        processRecordingForPreview: jest.fn().mockRejectedValue(new Error('Processing failed'))
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const recordingBlob = new Blob(['recording'], { type: 'audio/wav' });
      const beatUrl = 'http://example.com/beat.mp3';

      await expect(processRecordingForPreview(recordingBlob, beatUrl))
        .rejects.toThrow('Processing failed');
    });

    it('should handle network errors when fetching beat', async () => {
      // Mock fetch to fail
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const recordingBlob = new Blob(['recording'], { type: 'audio/wav' });
      const beatUrl = 'http://example.com/beat.mp3';

      await expect(processRecordingForPreview(recordingBlob, beatUrl))
        .rejects.toThrow();
    });
  });

  describe('exportRecording', () => {
    it('should export recording successfully', async () => {
      const mockWorkerApi = {
        exportRecording: jest.fn().mockResolvedValue({
          blob: new Blob(['exported'], { type: 'audio/wav' }),
          url: 'blob:exported-url',
          metadata: {
            title: 'Test Recording',
            duration: 30,
            format: 'wav'
          }
        })
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const mixedBuffer = new ArrayBuffer(1024);
      const metadata = {
        title: 'Test Recording',
        artist: 'Test Artist',
        beat: 'Test Beat'
      };

      const result = await exportRecording(mixedBuffer, metadata);
      
      expect(result).toBeDefined();
      expect(result.metadata.title).toBe('Test Recording');
    });

    it('should handle export errors', async () => {
      const mockWorkerApi = {
        exportRecording: jest.fn().mockRejectedValue(new Error('Export failed'))
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      const mixedBuffer = new ArrayBuffer(1024);
      const metadata = { title: 'Test' };

      await expect(exportRecording(mixedBuffer, metadata))
        .rejects.toThrow('Export failed');
    });
  });

  describe('Performance Monitoring', () => {
    it('should record performance metrics', async () => {
      const { performanceMonitor } = await import('../../utils/performance');
      
      const mockWorkerApi = {
        decodeAudio: jest.fn().mockResolvedValue(new ArrayBuffer(1024))
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      await initAudioWorker();
      
      expect(performanceMonitor.measureAsync).toHaveBeenCalledWith(
        'audio_worker_init',
        expect.any(Function)
      );
      expect(performanceMonitor.recordMetric).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'audio_worker_init_success',
          type: 'audio'
        })
      );
    });

    it('should record error metrics on failure', async () => {
      const { performanceMonitor } = await import('../../utils/performance');
      
      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockImplementation(() => {
        throw new Error('Worker failed');
      });

      try {
        await initAudioWorker();
      } catch (e) {
        // Expected to fail
      }
      
      expect(performanceMonitor.recordMetric).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'audio_worker_init_failure',
          type: 'audio'
        })
      );
    });
  });

  describe('Memory Management', () => {
    it('should clean up resources on termination', async () => {
      const mockWorker = {
        terminate: jest.fn(),
        postMessage: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
      };

      (Worker as jest.Mock).mockReturnValue(mockWorker);

      const mockWorkerApi = {
        cleanup: jest.fn(),
        decodeAudio: jest.fn()
      };

      const { wrap } = await import('comlink');
      (wrap as jest.Mock).mockReturnValue(mockWorkerApi);

      await initAudioWorker();
      terminateAudioWorker();
      
      expect(mockWorker.terminate).toHaveBeenCalled();
    });
  });
});
