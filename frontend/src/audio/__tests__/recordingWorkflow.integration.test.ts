/**
 * Recording Workflow Integration Tests
 * 
 * Tests the complete recording workflow from start to finish,
 * ensuring all components work together cohesively.
 */

import { jest } from '@jest/globals';
import { recordingStateMachine } from '../services/RecordingStateMachine';
import { audioContextManager } from '../services/AudioContextManager';
import { SyncAudioRecorder } from '../services/syncAudioRecorder';

// Mock the audio services
jest.mock('../services/syncAudioRecorder');
jest.mock('../services/audioWorkerService');

describe('Recording Workflow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    recordingStateMachine.reset();
    // Reset audio context manager
    (audioContextManager as any).audioContext = null;
    (audioContextManager as any).isInitialized = false;
  });

  afterEach(() => {
    recordingStateMachine.reset();
  });

  describe('Complete Recording Flow', () => {
    it('should handle full recording workflow successfully', async () => {
      // Mock successful recorder
      const mockRecorder = {
        initialize: jest.fn().mockResolvedValue(undefined),
        startRecording: jest.fn().mockResolvedValue(undefined),
        stopRecording: jest.fn().mockResolvedValue({
          blob: new Blob(['test'], { type: 'audio/wav' }),
          url: 'blob:test-url',
          duration: 10
        }),
        getState: jest.fn().mockReturnValue('recording'),
        getCurrentLevel: jest.fn().mockReturnValue(0.5),
        cleanup: jest.fn()
      };

      (SyncAudioRecorder as jest.Mock).mockImplementation(() => mockRecorder);

      // Test state transitions
      expect(recordingStateMachine.getState()).toBe('idle');
      expect(recordingStateMachine.canStartRecording()).toBe(true);

      // Start recording
      const startResult = await recordingStateMachine.send('START_RECORDING');
      expect(startResult).toBe(true);
      expect(recordingStateMachine.getState()).toBe('initializing');

      // Recording started
      const recordingStartedResult = await recordingStateMachine.send('RECORDING_STARTED');
      expect(recordingStartedResult).toBe(true);
      expect(recordingStateMachine.getState()).toBe('recording');

      // Stop recording
      const stopResult = await recordingStateMachine.send('STOP_RECORDING');
      expect(stopResult).toBe(true);
      expect(recordingStateMachine.getState()).toBe('stopping');

      // Recording stopped
      const stoppedResult = await recordingStateMachine.send('RECORDING_STOPPED');
      expect(stoppedResult).toBe(true);
      expect(recordingStateMachine.getState()).toBe('processing');

      // Processing completed
      const completedResult = await recordingStateMachine.send('PROCESSING_COMPLETED', {
        recordingBlob: new Blob(['test'], { type: 'audio/wav' }),
        duration: 10
      });
      expect(completedResult).toBe(true);
      expect(recordingStateMachine.getState()).toBe('stopped');
    });

    it('should handle recording errors gracefully', async () => {
      // Mock failing recorder
      const mockRecorder = {
        initialize: jest.fn().mockRejectedValue(new Error('Initialization failed')),
        startRecording: jest.fn(),
        stopRecording: jest.fn(),
        getState: jest.fn().mockReturnValue('idle'),
        getCurrentLevel: jest.fn().mockReturnValue(0),
        cleanup: jest.fn()
      };

      (SyncAudioRecorder as jest.Mock).mockImplementation(() => mockRecorder);

      // Start recording
      await recordingStateMachine.send('START_RECORDING');
      expect(recordingStateMachine.getState()).toBe('initializing');

      // Simulate error
      const errorResult = await recordingStateMachine.send('ERROR_OCCURRED', {
        error: 'Initialization failed'
      });
      expect(errorResult).toBe(true);
      expect(recordingStateMachine.getState()).toBe('error');

      // Should be able to reset and try again
      recordingStateMachine.reset();
      expect(recordingStateMachine.getState()).toBe('idle');
      expect(recordingStateMachine.canStartRecording()).toBe(true);
    });

    it('should prevent invalid state transitions', async () => {
      // Try to stop recording when not recording
      const invalidResult = await recordingStateMachine.send('STOP_RECORDING');
      expect(invalidResult).toBe(false);
      expect(recordingStateMachine.getState()).toBe('idle');

      // Try to start recording twice
      await recordingStateMachine.send('START_RECORDING');
      expect(recordingStateMachine.getState()).toBe('initializing');

      const duplicateStartResult = await recordingStateMachine.send('START_RECORDING');
      expect(duplicateStartResult).toBe(false);
      expect(recordingStateMachine.getState()).toBe('initializing');
    });
  });

  describe('Audio Context Management', () => {
    it('should initialize audio context properly', async () => {
      const context = await audioContextManager.initialize();
      expect(context).toBeDefined();
      expect(audioContextManager.isHealthy()).toBe(true);
    });

    it('should handle audio context errors', async () => {
      // Mock AudioContext to fail
      const originalAudioContext = global.AudioContext;
      global.AudioContext = jest.fn().mockImplementation(() => {
        throw new Error('AudioContext creation failed');
      });

      try {
        await audioContextManager.initialize();
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).toContain('Failed to initialize audio context');
      }

      // Restore
      global.AudioContext = originalAudioContext;
    });

    it('should manage audio context lifecycle', async () => {
      const context = await audioContextManager.initialize();
      expect(context.state).toBe('running');

      await audioContextManager.suspend();
      expect(context.suspend).toHaveBeenCalled();

      await audioContextManager.resume();
      expect(context.resume).toHaveBeenCalled();

      await audioContextManager.cleanup();
      expect(context.close).toHaveBeenCalled();
    });
  });

  describe('Error Recovery', () => {
    it('should recover from microphone permission denied', async () => {
      // Mock getUserMedia to fail initially
      const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
      let callCount = 0;
      
      navigator.mediaDevices.getUserMedia = jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          return Promise.reject(new Error('Permission denied'));
        }
        return Promise.resolve({
          getTracks: () => [{
            stop: jest.fn(),
            kind: 'audio',
            enabled: true
          }]
        });
      });

      // First attempt should fail
      await recordingStateMachine.send('START_RECORDING');
      await recordingStateMachine.send('ERROR_OCCURRED', {
        error: 'Permission denied'
      });
      expect(recordingStateMachine.getState()).toBe('error');

      // Reset and try again
      recordingStateMachine.reset();
      await recordingStateMachine.send('START_RECORDING');
      
      // Should work on second attempt
      expect(recordingStateMachine.canTransition('RECORDING_STARTED')).toBe(true);

      // Restore
      navigator.mediaDevices.getUserMedia = originalGetUserMedia;
    });

    it('should handle network errors during beat loading', async () => {
      // Mock fetch to fail
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      await recordingStateMachine.send('START_RECORDING');
      await recordingStateMachine.send('ERROR_OCCURRED', {
        error: 'Failed to load beat'
      });
      
      expect(recordingStateMachine.getState()).toBe('error');
      expect(recordingStateMachine.getContext().error).toBe('Failed to load beat');

      // Restore
      global.fetch = originalFetch;
    });
  });

  describe('Performance Monitoring', () => {
    it('should track recording performance metrics', async () => {
      const { performanceMonitor } = await import('../../utils/performance');
      
      // Start recording workflow
      await recordingStateMachine.send('START_RECORDING');
      await recordingStateMachine.send('RECORDING_STARTED');
      
      // Simulate some recording time
      await new Promise(resolve => setTimeout(resolve, 100));
      
      await recordingStateMachine.send('STOP_RECORDING');
      await recordingStateMachine.send('RECORDING_STOPPED');
      await recordingStateMachine.send('PROCESSING_COMPLETED');

      // Check that metrics were recorded
      expect(performanceMonitor.recordMetric).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'audio'
        })
      );
    });

    it('should detect memory leaks during long recordings', async () => {
      const { performanceMonitor } = await import('../../utils/performance');
      
      // Simulate long recording session
      await recordingStateMachine.send('START_RECORDING');
      await recordingStateMachine.send('RECORDING_STARTED');
      
      // Simulate memory usage increase
      const mockMemory = {
        usedJSHeapSize: 100 * 1024 * 1024, // 100MB
        totalJSHeapSize: 200 * 1024 * 1024,
        jsHeapSizeLimit: 500 * 1024 * 1024
      };
      
      (performance as any).memory = mockMemory;
      
      const summary = performanceMonitor.getSummary();
      expect(summary).toBeDefined();
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle rapid start/stop cycles', async () => {
      // Rapid start/stop should be handled gracefully
      const promises = [];
      
      for (let i = 0; i < 5; i++) {
        promises.push(recordingStateMachine.send('START_RECORDING'));
        promises.push(recordingStateMachine.send('STOP_RECORDING'));
      }
      
      const results = await Promise.all(promises);
      
      // Only valid transitions should succeed
      const successCount = results.filter(r => r === true).length;
      expect(successCount).toBeGreaterThan(0);
      expect(successCount).toBeLessThan(results.length); // Some should fail due to invalid transitions
    });

    it('should prevent multiple recording instances', async () => {
      // Start first recording
      await recordingStateMachine.send('START_RECORDING');
      await recordingStateMachine.send('RECORDING_STARTED');
      
      expect(recordingStateMachine.getState()).toBe('recording');
      expect(recordingStateMachine.isBusy()).toBe(true);
      
      // Try to start another recording
      const secondStart = await recordingStateMachine.send('START_RECORDING');
      expect(secondStart).toBe(false);
      expect(recordingStateMachine.getState()).toBe('recording'); // Should remain in recording state
    });
  });

  describe('Beat Synchronization', () => {
    it('should handle beat changes during recording', async () => {
      // Start recording
      await recordingStateMachine.send('START_RECORDING');
      await recordingStateMachine.send('RECORDING_STARTED');
      
      // Simulate beat change attempt during recording
      const context = recordingStateMachine.getContext();
      expect(recordingStateMachine.isBusy()).toBe(true);
      
      // Beat changes should be prevented during recording
      expect(recordingStateMachine.getState()).toBe('recording');
    });

    it('should sync recording with beat timing', async () => {
      const mockBeat = {
        url: 'test-beat.mp3',
        duration: 120, // 2 minutes
        bpm: 120
      };

      await recordingStateMachine.send('START_RECORDING', {
        beatUrl: mockBeat.url
      });

      const context = recordingStateMachine.getContext();
      expect(context.beatUrl).toBe(mockBeat.url);
    });
  });
});
