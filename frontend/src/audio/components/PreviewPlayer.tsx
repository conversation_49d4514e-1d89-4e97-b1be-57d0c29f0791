/**
 * PreviewPlayer Component
 *
 * A focused component for playing back recorded freestyles with beats.
 * This component handles:
 * - Audio playback of the mixed recording with precise synchronization
 * - High-quality waveform visualization
 * - Play/pause controls with accurate timing
 * - Download functionality with proper metadata
 * - Time display and seeking with frame-accurate positioning
 *
 * It uses the WaveformVisualizer component for visualization and
 * the useEnhancedAudioPlayback hook for precise audio control.
 */

import React, { useCallback, useEffect, useState } from 'react';
import { useAudioStore } from '../../stores/audioStore';
import { useBeatStore } from '../../stores/beatStore';
import { WaveformVisualizer } from '../../ui/visualizers';
import { exportRecording } from '../utils/audioExport';
import { useEnhancedAudioPlayback } from '../hooks/useEnhancedAudioPlayback';
import { useAudioWorker } from '../hooks/useAudioWorker';

interface PreviewPlayerProps {
  onRecordAgain?: () => void;
  autoPlay?: boolean;
}

export const PreviewPlayer: React.FC<PreviewPlayerProps> = ({
  onRecordAgain,
  autoPlay = false
}) => {
  // Local state
  const [loadingState, setLoadingState] = useState<'initial' | 'loading' | 'ready' | 'error'>('initial');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [waveformData, setWaveformData] = useState<number[][]>([]);

  // Initialize audio worker for processing
  const {
    worker,
    isInitialized: isWorkerInitialized,
    generateWaveform
  } = useAudioWorker();

  // Use our enhanced hook for audio playback
  const {
    isPlaying,
    isLoading,
    currentTime,
    duration,
    error,
    readyState,
    togglePlayPause,
    handleSeek,
    setVolume
  } = useEnhancedAudioPlayback({
    autoPlay,
    onEnded: () => console.log("Playback ended"),
    onTimeUpdate: () => {
      // Additional time update handling if needed
    },
    onDurationChange: (newDuration) => {
      console.log("Duration changed:", newDuration);
    },
    onError: (errorMsg) => {
      console.error("Audio playback error:", errorMsg);
      setLoadingState('error');
    }
  });

  // Get data from stores
  const { mixedBlob, mixedBuffer, mixedUrl, recordingBlob } = useAudioStore();
  const { currentBeat } = useBeatStore();

  // Update loading state based on readyState
  useEffect(() => {
    if (isLoading) {
      setLoadingState('loading');

      // Simulate loading progress
      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          const newProgress = prev + (Math.random() * 10);
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 200);

      return () => clearInterval(interval);
    } else if (error) {
      setLoadingState('error');
      setLoadingProgress(0);
    } else if (readyState >= 3) {
      setLoadingState('ready');
      setLoadingProgress(100);
    }
  }, [isLoading, error, readyState]);

  // Log available audio sources for debugging
  useEffect(() => {
    console.log("PreviewPlayer audio sources:", {
      mixedBlob: mixedBlob ? `${mixedBlob.size} bytes` : 'missing',
      mixedBuffer: mixedBuffer ? 'available' : 'missing',
      mixedUrl: mixedUrl || 'missing',
      recordingBlob: recordingBlob ? `${recordingBlob.size} bytes` : 'missing'
    });
  }, [mixedBlob, mixedBuffer, mixedUrl, recordingBlob]);

  // Generate waveform data using the Web Worker
  useEffect(() => {
    if (isWorkerInitialized && mixedBuffer && worker) {
      const generateWaveformData = async () => {
        try {
          console.log("Generating waveform data with Web Worker");
          const data = await generateWaveform(mixedBuffer, 1000);
          setWaveformData(data);
          console.log("Waveform data generated:", data.length, "points");
        } catch (error) {
          console.error("Error generating waveform data:", error);
        }
      };

      generateWaveformData();
    }
  }, [isWorkerInitialized, mixedBuffer, worker, generateWaveform]);

  // Handle download
  const handleDownload = useCallback(() => {
    if (mixedBlob) {
      const beatTitle = currentBeat?.title ||
                       (currentBeat?.attributes && currentBeat.attributes.title) ||
                       undefined;

      exportRecording(mixedBlob, beatTitle);
    } else {
      console.error("No recording available to download");
    }
  }, [mixedBlob, currentBeat]);

  // Format time (mm:ss)
  const formatTime = (seconds: number) => {
    if (!isFinite(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get beat info
  const beatTitle = currentBeat?.title ||
                   (currentBeat?.attributes && currentBeat.attributes.title) ||
                   'Unknown Beat';

  // Handle volume change
  const handleVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const volume = parseFloat(e.target.value) / 100;
    setVolume(volume);
  }, [setVolume]);

  return (
    <div className="preview-player w-full max-w-3xl">
      <div className="w-full px-4">
        {/* Beat info */}
        <div className="text-center mb-4">
          <h3 className="text-xl font-medium text-[#E6C770]">{beatTitle}</h3>
          <p className="text-sm text-gray-400">Mixed Freestyle Recording</p>
        </div>

        {/* Waveform */}
        <div className="waveform-container w-full h-32 mb-2 border border-[rgba(230,199,112,0.3)] rounded-md bg-[rgba(10,26,47,0.3)] relative">
          {/* Loading overlay */}
          {loadingState === 'loading' && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 border-2 border-[#E6C770] border-t-transparent rounded-full animate-spin mb-2"></div>
                <div className="text-sm text-[#E6C770]">Loading audio {Math.round(loadingProgress)}%</div>
              </div>
            </div>
          )}

          {/* Error overlay */}
          {loadingState === 'error' && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
              <div className="text-red-400 text-center p-4">
                <div className="text-xl mb-2">⚠️</div>
                <div>{error || "Error loading audio"}</div>
              </div>
            </div>
          )}

          {/* Always show the waveform, even when loading */}
          <WaveformVisualizer
            audioBuffer={mixedBuffer}
            audioUrl={mixedBlob || mixedUrl || recordingBlob}  /* Try all available sources */
            currentTime={currentTime}
            duration={duration || 180}
            isPlaying={isPlaying}
            onSeek={handleSeek}
            height={128}
            gradientColors={["#5f6fff", "#7a6fff", "#b367ff", "#d45fff", "#ff5f7e", "#ff7a5f"]}
            waveformKey={`preview-waveform-${mixedUrl || (mixedBlob ? mixedBlob.size : 'default')}`}  /* Use a stable key */
            precomputedPeaks={waveformData.length > 0 ? waveformData : undefined}  /* Use worker-generated waveform data if available */
          />
        </div>

        {/* Time display */}
        <div className="flex justify-between text-sm text-white/70 mb-4">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>

        {/* Volume slider */}
        <div className="flex items-center mb-4">
          <span className="text-sm text-white/70 mr-2">Volume:</span>
          <input
            type="range"
            min="0"
            max="100"
            defaultValue="100"
            onChange={handleVolumeChange}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
          />
        </div>

        {/* Controls */}
        <div className="flex justify-center gap-4 mb-6">
          {/* Play/Pause button */}
          <button
            onClick={togglePlayPause}
            disabled={isLoading || loadingState === 'error'}
            className="px-8 py-2 bg-[#E6C770] text-[#0A1A2F] font-medium rounded-full disabled:opacity-50 transition-all hover:bg-[#f5d78a] focus:outline-none focus:ring-2 focus:ring-[#E6C770] focus:ring-opacity-50"
          >
            {isPlaying ? 'Pause' : 'Play'}
          </button>

          {/* Download button */}
          <button
            onClick={handleDownload}
            disabled={loadingState !== 'ready'}
            className="px-8 py-2 bg-transparent border border-[#E6C770] text-[#E6C770] font-medium rounded-full hover:bg-[#E6C770]/10 transition-colors disabled:opacity-50 disabled:hover:bg-transparent focus:outline-none focus:ring-2 focus:ring-[#E6C770] focus:ring-opacity-50"
          >
            Download
          </button>

          {/* Record Again button */}
          <button
            onClick={onRecordAgain}
            className="px-8 py-2 bg-transparent border border-white/50 text-white font-medium rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-25"
          >
            Record Again
          </button>
        </div>

        {/* Error message */}
        {error && loadingState !== 'error' && (
          <div className="text-red-400 text-center mt-4 p-2 bg-red-900/20 rounded">
            {error}
          </div>
        )}

        {/* Audio quality info */}
        <div className="text-xs text-gray-500 text-center mt-4">
          <p>High-quality 48kHz 16-bit audio with synchronized vocals and beat</p>
        </div>
      </div>
    </div>
  );
};
