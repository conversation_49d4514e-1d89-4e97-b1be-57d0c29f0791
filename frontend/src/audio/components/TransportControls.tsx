/**
 * TransportControls Component
 * 
 * A focused component for audio playback controls.
 * This component handles:
 * - Play/pause controls
 * - Time display
 * - Seeking
 * 
 * It's designed to be reusable across both recording and preview modes.
 */

import React from 'react';

interface TransportControlsProps {
  isPlaying: boolean;
  isLoading?: boolean;
  currentTime: number;
  duration: number;
  onPlayPause: () => void;
  onSeek?: (time: number) => void;
  onRecordAgain?: () => void;
  showRecordAgain?: boolean;
  className?: string;
}

export const TransportControls: React.FC<TransportControlsProps> = ({
  isPlaying,
  isLoading = false,
  currentTime,
  duration,
  onPlayPause,
  onSeek,
  onRecordAgain,
  showRecordAgain = false,
  className = ''
}) => {
  // Format time (mm:ss)
  const formatTime = (seconds: number) => {
    if (!isFinite(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Handle seek bar change
  const handleSeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSeek) {
      onSeek(parseFloat(e.target.value));
    }
  };
  
  return (
    <div className={`transport-controls w-full ${className}`}>
      {/* Time display */}
      <div className="flex justify-between text-sm text-white/70 mb-2">
        <span>{formatTime(currentTime)}</span>
        <span>{formatTime(duration)}</span>
      </div>
      
      {/* Seek bar */}
      {onSeek && (
        <div className="mb-4">
          <input
            type="range"
            min={0}
            max={duration || 100}
            value={currentTime}
            onChange={handleSeekChange}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, #E6C770 ${(currentTime / (duration || 100)) * 100}%, #4B5563 ${(currentTime / (duration || 100)) * 100}%)`
            }}
          />
        </div>
      )}
      
      {/* Controls */}
      <div className="flex justify-center gap-4">
        {/* Play/Pause button */}
        <button 
          onClick={onPlayPause}
          disabled={isLoading}
          className="px-8 py-2 bg-[#E6C770] text-[#0A1A2F] font-medium rounded-full disabled:opacity-50"
        >
          {isLoading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-[#0A1A2F]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading
            </span>
          ) : isPlaying ? 'Pause' : 'Play'}
        </button>
        
        {/* Record Again button */}
        {showRecordAgain && onRecordAgain && (
          <button 
            onClick={onRecordAgain}
            className="px-8 py-2 bg-transparent border border-white/50 text-white font-medium rounded-full hover:bg-white/10 transition-colors"
          >
            Record Again
          </button>
        )}
      </div>
    </div>
  );
};
