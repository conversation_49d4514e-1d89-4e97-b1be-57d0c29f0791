/**
 * RecordingSystem Component
 *
 * A focused component for recording freestyles with beats.
 * This component handles:
 * - Microphone access and recording with precise synchronization
 * - Beat playback with accurate timing
 * - Recording state management with detailed feedback
 * - Navigation to preview mode with complete recording data
 * - Audio level visualization
 *
 * It uses the useSyncAudioRecording hook for enhanced recording functionality.
 */

import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { MicVinylIntegrated } from '../../ui/visualizers/MicVinylIntegrated';
import { useAudioStore } from '../../stores/audioStore';
import { useBeatStore } from '../../stores/beatStore';
import { usePreviewStore } from '../../stores/previewStore';
import { useSyncAudioRecording } from '../hooks/useSyncAudioRecording';
import { useAudioFeedback, useOperationFeedback, useFeedback } from '../../ui/common/UserFeedback';
import { useRecordingStateMachine } from '../services/RecordingStateMachine';
import { performanceMonitor } from '../../utils/performance';

interface RecordingSystemProps {
  onRecordingComplete?: (result: any) => void;
}

export const RecordingSystem: React.FC<RecordingSystemProps> = ({
  onRecordingComplete
}) => {
  // Initialize router for navigation
  const router = useRouter();

  // Get state from stores
  const recordingState = useAudioStore(state => state.recordingState);
  const setPreviewMode = usePreviewStore(state => state.setPreviewMode);
  const currentBeat = useBeatStore(state => state.currentBeat);

  // Feedback systems
  const audioFeedback = useAudioFeedback();
  const operationFeedback = useOperationFeedback();
  const { showWarning } = useFeedback();

  // State for audio level
  const [audioLevel, setAudioLevel] = useState(0);

  // Use our enhanced hook for synchronized recording
  const {
    error,
    transportRef,
    handleStartRecording,
    handleStopRecording,
    level
  } = useSyncAudioRecording({
    onRecordingComplete: (result: any) => {
      console.log("Recording complete with synchronized audio:", result);

      try {
        // Call the callback if provided
        if (onRecordingComplete) {
          onRecordingComplete(result);
        }

        // Store recording data for the preview page
        storeRecordingData();

        // Set preview mode and navigate to preview page
        setPreviewMode(true);

        // Navigate to the preview page with a slight delay to ensure data is stored
        console.log("Navigating to preview page...");

        // Use setTimeout to ensure navigation happens after the current execution context
        setTimeout(() => {
          try {
            console.log("Executing navigation to preview page");
            router.push('/session/preview');
          } catch (navError) {
            console.error("Error during navigation:", navError);
            // Force reload as last resort
            window.location.href = '/session/preview';
          }
        }, 300); // Increased delay for more reliability
      } catch (error) {
        console.error("Error in recording complete handler:", error);
        // Try direct navigation as fallback
        window.location.href = '/session/preview';
      }
    }
  });

  // Update audio level with smoothing
  useEffect(() => {
    // Apply smoothing to the level
    setAudioLevel(prevLevel => {
      // If new level is higher, respond quickly
      if (level > prevLevel) {
        return level * 0.8 + prevLevel * 0.2;
      }
      // If new level is lower, decay more slowly
      return level * 0.3 + prevLevel * 0.7;
    });
  }, [level]);

  // Store recording data in localStorage for the preview page
  const storeRecordingData = () => {
    try {
      const { mixedBlob, mixedBuffer, recordingBlob, mixedUrl, recordingUrl } = useAudioStore.getState();

      // Ensure we have at least one URL to use
      const finalMixedUrl = mixedUrl || recordingUrl || (window as any).__mixedRecordingUrl || (window as any).__currentRecordingUrl;
      const finalRecordingUrl = recordingUrl || mixedUrl || (window as any).__currentRecordingUrl || (window as any).__mixedRecordingUrl;

      // Log what we're storing
      console.log("Storing recording data with URLs:", {
        mixedUrl: finalMixedUrl,
        recordingUrl: finalRecordingUrl,
        hasMixedBlob: !!mixedBlob,
        hasRecordingBlob: !!recordingBlob
      });

      // Verify we have valid URLs
      if (!finalMixedUrl && !finalRecordingUrl) {
        console.warn("No valid URLs found for recording data");
      }

      // Create the data object
      const recordingData = {
        timestamp: Date.now(),
        recordingUrl: finalRecordingUrl,
        mixedUrl: finalMixedUrl,
        beatId: currentBeat?.id || null,
        beatTitle: currentBeat?.title || currentBeat?.attributes?.title || 'Unknown Beat',
        beatUrl: currentBeat?.audio_url || (currentBeat?.attributes && currentBeat.attributes.audio_url) || null,
        hasMixedBlob: !!mixedBlob,
        hasRecordingBlob: !!recordingBlob,
        hasMixedBuffer: !!mixedBuffer
      };

      // Store the data in localStorage
      try {
        localStorage.setItem('recordingData', JSON.stringify(recordingData));
      } catch (localStorageError) {
        console.error("Error storing in localStorage:", localStorageError);
      }

      // Also store in window for redundancy
      try {
        (window as any).__currentRecordingData = {
          recordingUrl: finalRecordingUrl,
          mixedUrl: finalMixedUrl,
          beatUrl: currentBeat?.audio_url || (currentBeat?.attributes && currentBeat.attributes.audio_url) || null,
          timestamp: Date.now()
        };
      } catch (windowError) {
        console.error("Error storing in window:", windowError);
      }

      // Store blobs in window as additional backup
      if (mixedBlob) {
        (window as any).__mixedBlob = mixedBlob;
      }
      if (recordingBlob) {
        (window as any).__recordingBlob = recordingBlob;
      }

      console.log("Stored recording data for preview page");
    } catch (e) {
      console.error("Error storing recording data:", e);

      // Last resort fallback - store minimal data
      try {
        const fallbackData = {
          timestamp: Date.now(),
          recordingUrl: (window as any).__currentRecordingUrl,
          mixedUrl: (window as any).__mixedRecordingUrl,
          beatUrl: currentBeat?.audio_url
        };

        localStorage.setItem('recordingData', JSON.stringify(fallbackData));
        console.log("Stored fallback recording data");
      } catch (fallbackError) {
        console.error("Failed to store even fallback data:", fallbackError);
      }
    }
  };

  // Handle microphone button click with feedback
  const handleMicClick = useCallback(async () => {
    console.log("Mic button clicked, current state:", recordingState);

    try {
      if (recordingState === 'idle' || recordingState === 'stopped' || recordingState === 'error') {
        // Start recording with feedback
        operationFeedback.startOperation(
          'start-recording',
          'Starting Recording',
          'Initializing microphone and beat synchronization...'
        );

        await handleStartRecording();

        operationFeedback.completeOperation(
          'start-recording',
          'Recording Started',
          'Your freestyle session is now being recorded!'
        );

        // Record performance metric
        performanceMonitor.recordMetric({
          name: 'recording_start_success',
          value: 1,
          timestamp: Date.now(),
          type: 'audio'
        });

      } else if (recordingState === 'recording') {
        // Stop recording with feedback
        operationFeedback.startOperation(
          'stop-recording',
          'Stopping Recording',
          'Processing your recording...'
        );

        await handleStopRecording();

        operationFeedback.completeOperation(
          'stop-recording',
          'Recording Complete',
          'Your recording has been saved and is ready for preview!'
        );

        // Record performance metric
        performanceMonitor.recordMetric({
          name: 'recording_stop_success',
          value: 1,
          timestamp: Date.now(),
          type: 'audio'
        });

      } else {
        console.log("Ignoring mic click in current state:", recordingState);
        showWarning(
          'Recording Busy',
          'Please wait for the current operation to complete.'
        );
      }
    } catch (error) {
      console.error('Error in mic click handler:', error);

      // Show error feedback
      if (recordingState === 'idle' || recordingState === 'stopped' || recordingState === 'error') {
        operationFeedback.failOperation(
          'start-recording',
          'Recording Failed',
          'Unable to start recording. Please check your microphone permissions.',
          [
            {
              label: 'Retry',
              action: () => handleMicClick(),
              style: 'primary'
            }
          ]
        );
      } else {
        operationFeedback.failOperation(
          'stop-recording',
          'Stop Recording Failed',
          'Unable to stop recording properly. Your recording may not be saved.',
          [
            {
              label: 'Try Again',
              action: () => handleMicClick(),
              style: 'primary'
            }
          ]
        );
      }

      // Record error metric
      performanceMonitor.recordMetric({
        name: 'recording_operation_error',
        value: 1,
        timestamp: Date.now(),
        type: 'audio',
        metadata: {
          error: error instanceof Error ? error.message : String(error),
          state: recordingState
        }
      });
    }
  }, [recordingState, handleStartRecording, handleStopRecording, audioFeedback, operationFeedback]);

  // Handle preview button click
  const handlePreviewClick = useCallback(() => {
    // Start beat playback
    if (transportRef.current && transportRef.current.startBeat) {
      transportRef.current.startBeat();
    }
  }, [transportRef]);

  return (
    <div className="recording-system w-full flex flex-col items-center justify-center">
      {/* Microphone component */}
      <MicVinylIntegrated
        onClick={handleMicClick}
        onPreviewClick={handlePreviewClick}
        isRecording={recordingState === 'recording'}
        isInitializing={recordingState === 'initializing'}
        isStopping={recordingState === 'stopping'}
        hasError={!!error}
        errorMessage={error || ''}
        beatTitle={currentBeat?.title || (currentBeat?.attributes && currentBeat.attributes.title) || 'No Beat Selected'}
        level={audioLevel} // Use smoothed audio level
      />

      {/* Headphones recommendation */}
      {recordingState === 'recording' && (
        <div className="text-yellow-400 text-center mt-4 text-sm animate-pulse">
          For best results, use headphones to prevent feedback
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="text-red-400 text-center mt-4">
          {error}
        </div>
      )}
    </div>
  );
};
