/**
 * RecordingSystem Component
 *
 * A focused component for recording freestyles with beats.
 * This component handles:
 * - Microphone access and recording with precise synchronization
 * - Beat playback with accurate timing
 * - Recording state management with detailed feedback
 * - Navigation to preview mode with complete recording data
 * - Audio level visualization
 *
 * It uses the useSyncAudioRecording hook for enhanced recording functionality.
 */

import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { MicVinylIntegrated } from '../../ui/visualizers/MicVinylIntegrated';
import { useAudioStore } from '../../stores/audioStore';
import { useBeatStore } from '../../stores/beatStore';
import { usePreviewStore } from '../../stores/previewStore';
import { useSyncAudioRecording } from '../hooks/useSyncAudioRecording';

interface RecordingSystemProps {
  onRecordingComplete?: (result: any) => void;
}

export const RecordingSystem: React.FC<RecordingSystemProps> = ({
  onRecordingComplete
}) => {
  // Initialize router for navigation
  const router = useRouter();

  // Get state from stores
  const recordingState = useAudioStore(state => state.recordingState);
  const setPreviewMode = usePreviewStore(state => state.setPreviewMode);
  const currentBeat = useBeatStore(state => state.currentBeat);

  // State for audio level
  const [audioLevel, setAudioLevel] = useState(0);

  // Use our enhanced hook for synchronized recording
  const {
    error,
    transportRef,
    handleStartRecording,
    handleStopRecording,
    level
  } = useSyncAudioRecording({
    onRecordingComplete: (result: any) => {
      console.log("Recording complete with synchronized audio:", result);

      try {
        // Call the callback if provided
        if (onRecordingComplete) {
          onRecordingComplete(result);
        }

        // Store recording data for the preview page
        storeRecordingData();

        // Set preview mode and navigate to preview page
        setPreviewMode(true);

        // Navigate to the preview page with a slight delay to ensure data is stored
        console.log("Navigating to preview page...");

        // Use setTimeout to ensure navigation happens after the current execution context
        setTimeout(() => {
          try {
            console.log("Executing navigation to preview page");
            router.push('/session/preview');
          } catch (navError) {
            console.error("Error during navigation:", navError);
            // Force reload as last resort
            window.location.href = '/session/preview';
          }
        }, 300); // Increased delay for more reliability
      } catch (error) {
        console.error("Error in recording complete handler:", error);
        // Try direct navigation as fallback
        window.location.href = '/session/preview';
      }
    }
  });

  // Update audio level with smoothing
  useEffect(() => {
    // Apply smoothing to the level
    setAudioLevel(prevLevel => {
      // If new level is higher, respond quickly
      if (level > prevLevel) {
        return level * 0.8 + prevLevel * 0.2;
      }
      // If new level is lower, decay more slowly
      return level * 0.3 + prevLevel * 0.7;
    });
  }, [level]);

  // Store recording data in localStorage for the preview page
  const storeRecordingData = () => {
    try {
      const { mixedBlob, mixedBuffer, recordingBlob, mixedUrl, recordingUrl } = useAudioStore.getState();

      // Ensure we have at least one URL to use
      const finalMixedUrl = mixedUrl || recordingUrl || (window as any).__mixedRecordingUrl || (window as any).__currentRecordingUrl;
      const finalRecordingUrl = recordingUrl || mixedUrl || (window as any).__currentRecordingUrl || (window as any).__mixedRecordingUrl;

      // Log what we're storing
      console.log("Storing recording data with URLs:", {
        mixedUrl: finalMixedUrl,
        recordingUrl: finalRecordingUrl,
        hasMixedBlob: !!mixedBlob,
        hasRecordingBlob: !!recordingBlob
      });

      // Verify we have valid URLs
      if (!finalMixedUrl && !finalRecordingUrl) {
        console.warn("No valid URLs found for recording data");
      }

      // Create the data object
      const recordingData = {
        timestamp: Date.now(),
        recordingUrl: finalRecordingUrl,
        mixedUrl: finalMixedUrl,
        beatId: currentBeat?.id || null,
        beatTitle: currentBeat?.title || currentBeat?.attributes?.title || 'Unknown Beat',
        beatUrl: currentBeat?.audio_url || (currentBeat?.attributes && currentBeat.attributes.audio_url) || null,
        hasMixedBlob: !!mixedBlob,
        hasRecordingBlob: !!recordingBlob,
        hasMixedBuffer: !!mixedBuffer
      };

      // Store the data in localStorage
      try {
        localStorage.setItem('recordingData', JSON.stringify(recordingData));
      } catch (localStorageError) {
        console.error("Error storing in localStorage:", localStorageError);
      }

      // Also store in window for redundancy
      try {
        (window as any).__currentRecordingData = {
          recordingUrl: finalRecordingUrl,
          mixedUrl: finalMixedUrl,
          beatUrl: currentBeat?.audio_url || (currentBeat?.attributes && currentBeat.attributes.audio_url) || null,
          timestamp: Date.now()
        };
      } catch (windowError) {
        console.error("Error storing in window:", windowError);
      }

      // Store blobs in window as additional backup
      if (mixedBlob) {
        (window as any).__mixedBlob = mixedBlob;
      }
      if (recordingBlob) {
        (window as any).__recordingBlob = recordingBlob;
      }

      console.log("Stored recording data for preview page");
    } catch (e) {
      console.error("Error storing recording data:", e);

      // Last resort fallback - store minimal data
      try {
        const fallbackData = {
          timestamp: Date.now(),
          recordingUrl: (window as any).__currentRecordingUrl,
          mixedUrl: (window as any).__mixedRecordingUrl,
          beatUrl: currentBeat?.audio_url
        };

        localStorage.setItem('recordingData', JSON.stringify(fallbackData));
        console.log("Stored fallback recording data");
      } catch (fallbackError) {
        console.error("Failed to store even fallback data:", fallbackError);
      }
    }
  };

  // Handle microphone button click
  const handleMicClick = useCallback(async () => {
    console.log("Mic button clicked, current state:", recordingState);

    if (recordingState === 'idle' || recordingState === 'stopped' || recordingState === 'error') {
      // Start recording
      await handleStartRecording();
    } else if (recordingState === 'recording') {
      // Stop recording
      await handleStopRecording();
    } else {
      console.log("Ignoring mic click in current state:", recordingState);
    }
  }, [recordingState, handleStartRecording, handleStopRecording]);

  // Handle preview button click
  const handlePreviewClick = useCallback(() => {
    // Start beat playback
    if (transportRef.current && transportRef.current.startBeat) {
      transportRef.current.startBeat();
    }
  }, [transportRef]);

  return (
    <div className="recording-system w-full flex flex-col items-center justify-center">
      {/* Microphone component */}
      <MicVinylIntegrated
        onClick={handleMicClick}
        onPreviewClick={handlePreviewClick}
        isRecording={recordingState === 'recording'}
        isInitializing={recordingState === 'initializing'}
        isStopping={recordingState === 'stopping'}
        hasError={!!error}
        errorMessage={error || ''}
        beatTitle={currentBeat?.title || (currentBeat?.attributes && currentBeat.attributes.title) || 'No Beat Selected'}
        level={audioLevel} // Use smoothed audio level
      />

      {/* Headphones recommendation */}
      {recordingState === 'recording' && (
        <div className="text-yellow-400 text-center mt-4 text-sm animate-pulse">
          For best results, use headphones to prevent feedback
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="text-red-400 text-center mt-4">
          {error}
        </div>
      )}
    </div>
  );
};
