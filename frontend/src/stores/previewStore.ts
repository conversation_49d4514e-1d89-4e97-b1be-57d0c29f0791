import { create } from 'zustand';

/**
 * Preview Store
 *
 * Manages the state for the preview mode in the freestyle app.
 * Controls when the app enters and exits preview mode, which affects
 * the visibility of various UI components.
 *
 * Used by:
 * - RecordingPreviewPlayer: Sets preview mode when mounted/unmounted
 * - FreestyleRecorder: Sets preview mode when stopping recording
 * - session/page.tsx: Uses preview mode to hide other UI elements
 */
interface PreviewStore {
  /**
   * Boolean flag indicating whether the app is in preview mode
   * When true, the RecordingPreviewPlayer is shown and other UI elements are hidden
   */
  isPreviewMode: boolean;

  /**
   * Function to set the preview mode state
   * @param isActive Whether preview mode should be active
   */
  setPreviewMode: (isActive: boolean) => void;

  /**
   * URL of the recording being previewed
   */
  previewRecordingUrl: string | null;

  /**
   * URL of the beat being previewed
   */
  previewBeatUrl: string | null;

  /**
   * Whether to automatically play the preview when entering preview mode
   */
  autoPlay: boolean;

  /**
   * Function to set preview data and enter preview mode
   * @param recordingUrl URL of the recording to preview
   * @param beatUrl URL of the beat to preview
   */
  setPreviewData: (recordingUrl: string | null, beatUrl: string | null) => void;

  /**
   * Function to clear preview data and exit preview mode
   */
  clearPreviewData: () => void;
}

// Create the store
export const usePreviewStore = create<PreviewStore>((set) => ({
  // Default values
  isPreviewMode: false,
  previewRecordingUrl: null,
  previewBeatUrl: null,
  autoPlay: false, // Default to false to prevent automatic playback

  // Setters
  setPreviewMode: (isActive) => set({ isPreviewMode: isActive }),

  setPreviewData: (recordingUrl, beatUrl) => set({
    previewRecordingUrl: recordingUrl,
    previewBeatUrl: beatUrl,
    isPreviewMode: true,
    autoPlay: false, // Default to false when setting preview data
  }),

  clearPreviewData: () => {
    // Clear preview data and exit preview mode
    set({
      previewRecordingUrl: null,
      previewBeatUrl: null,
      isPreviewMode: false,
      autoPlay: false,
    });

    // Also clear recording data from the audio store
    // This ensures the preview player doesn't reappear when switching back to preview mode
    const { useAudioStore } = require('./audioStore');
    useAudioStore.setState({
      recordingUrl: null,
      recordingBlob: null,
      mixedUrl: null,
      duration: 0
    });
  },
}));
