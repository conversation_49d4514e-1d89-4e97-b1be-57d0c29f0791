import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Beat {
  id: string | number;
  title: string;
  audio_url: string;
  genre?: string;
  bpm?: number;
  key?: string;
  premium?: boolean;
  attributes?: {
    audio_url?: string;
    title?: string;
    genre?: string;
    bpm?: number;
    key?: string;
    premium?: boolean;
  };
}

interface BeatStore {
  // List of beats
  beats: Beat[];
  isLoading: boolean;
  error: string | null;

  // Selected beat
  currentBeat: Beat | null;
  beatAudioBuffer: AudioBuffer | null;

  // Playback state
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  beatVolume: number; // Volume for beat playback (0-1)

  // Recently used beats
  recentBeats: Beat[];
  favoriteBeats: Beat[];

  // Actions
  loadBeats: () => Promise<void>;
  selectBeat: (beatId: string | number) => void;
  selectBeatByObject: (beat: Beat) => void;
  togglePlayPause: () => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  addToFavorites: (beatId: string | number) => void;
  removeFromFavorites: (beatId: string | number) => void;
  setBeatAudioBuffer: (buffer: AudioBuffer) => void;
  setBeatVolume: (volume: number) => void;
}

export const useBeatStore = create<BeatStore>()(
  persist(
    (set, get) => ({
      // Default values
      beats: [],
      isLoading: false,
      error: null,
      currentBeat: null,
      beatAudioBuffer: null,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      beatVolume: (() => {
        // Load beatVolume from localStorage or default to 0.8 (80%)
        try {
          if (typeof window !== 'undefined') {
            const savedVolume = localStorage.getItem('beatVolume');
            if (savedVolume !== null) {
              const volume = parseFloat(savedVolume);
              return isNaN(volume) ? 0.8 : volume;
            }
          }
        } catch (e) {
          console.warn("Error reading beat volume from localStorage:", e);
        }
        return 0.8;
      })(),
      recentBeats: [],
      favoriteBeats: [],

      // Load beats from API
      loadBeats: async () => {
        set({ isLoading: true, error: null });

        // Default beats to use if API fails
        const defaultBeats = [
          {
            id: "1",
            title: "Hands Up",
            audio_url: "/beats/hands-up.mp3",
            genre: "Hip Hop",
            bpm: 90,
            key: "C Minor"
          },
          {
            id: "2",
            title: "Supreme",
            audio_url: "/beats/supreme.mp3",
            genre: "Trap",
            bpm: 140,
            key: "G Minor"
          }
        ];

        try {
          console.log("Loading beats from API...");
          const response = await fetch('/api/beats');
          if (!response.ok) {
            throw new Error(`Failed to load beats: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          const beats = data.data || [];
          console.log(`Loaded ${beats.length} beats from API`);

          // If we got empty beats from API, use defaults
          if (beats.length === 0) {
            console.log('No beats returned from API, using defaults');
            set({ beats: defaultBeats, isLoading: false });
          } else {
            set({ beats, isLoading: false });
          }

          // Use the persisted currentBeat if available, otherwise select the first beat
          const beatsToUse = beats.length > 0 ? beats : defaultBeats;
          const currentBeat = get().currentBeat;

          console.log("Current state:", {
            beatsCount: beatsToUse.length,
            hasCurrentBeat: !!currentBeat,
            currentBeatId: currentBeat?.id
          });

          if (currentBeat) {
            console.log("Found persisted beat:", currentBeat.id);
            // If we have a persisted beat, make sure it's still in the loaded beats
            // This ensures the persisted beat is fully functional
            if (beatsToUse.length > 0) {
              // Find the beat with the same ID in the loaded beats
              const matchingBeat = beatsToUse.find((b: Beat) =>
                String(b.id) === String(currentBeat.id)
              );

              if (matchingBeat) {
                console.log("Found matching beat in loaded beats:", matchingBeat.id);
                // Update the persisted beat with the fresh data
                get().selectBeatByObject(matchingBeat);
              } else {
                console.log("Persisted beat not found in loaded beats, selecting first beat");
                // If we can't find the persisted beat, select the first beat
                get().selectBeatByObject(beatsToUse[0]);
              }
            } else {
              console.log("No beats available, checking if persisted beat is valid");
              // If the persisted beat has an audio_url, make sure it's still valid
              // by checking if it exists in the loaded beats
              const matchingBeat = beatsToUse.find((b: Beat) =>
                String(b.id) === String(currentBeat.id)
              );

              if (matchingBeat) {
                console.log("Found matching beat in default beats:", matchingBeat.id);
                // Update the persisted beat with the fresh data to ensure it's fully loaded
                get().selectBeatByObject(matchingBeat);
              } else if (beatsToUse.length > 0) {
                console.log("Persisted beat not found in default beats, selecting first beat");
                // If we can't find the persisted beat, select the first beat
                get().selectBeatByObject(beatsToUse[0]);
              }
            }
          } else if (beatsToUse.length > 0) {
            console.log("No persisted beat, selecting first beat:", beatsToUse[0].id);
            // If no persisted beat, select the first beat from the loaded beats
            get().selectBeatByObject(beatsToUse[0]);
          }
        } catch (err: any) {
          console.error('Failed to load beats:', err);
          // Use default beats when API fails
          set({
            beats: defaultBeats,
            error: err?.message || 'Failed to load beats from API, using default beats',
            isLoading: false
          });

          // Use the persisted currentBeat if available, otherwise select the first beat
          const currentBeat = get().currentBeat;

          console.log("Error handler - Current state:", {
            defaultBeatsCount: defaultBeats.length,
            hasCurrentBeat: !!currentBeat,
            currentBeatId: currentBeat?.id
          });

          if (currentBeat) {
            console.log("Error handler - Found persisted beat:", currentBeat.id);
            // If we have a persisted beat, make sure it's still in the default beats
            if (defaultBeats.length > 0) {
              // Find the beat with the same ID in the default beats
              const matchingBeat = defaultBeats.find((b: Beat) =>
                String(b.id) === String(currentBeat.id)
              );

              if (matchingBeat) {
                console.log("Error handler - Found matching beat in default beats:", matchingBeat.id);
                // Update the persisted beat with the fresh data
                get().selectBeatByObject(matchingBeat);
              } else {
                console.log("Error handler - Persisted beat not found in default beats, selecting first beat");
                // If we can't find the persisted beat, select the first beat
                get().selectBeatByObject(defaultBeats[0]);
              }
            } else {
              console.log("Error handler - No default beats available, checking if persisted beat is valid");
              // If the persisted beat has an audio_url, make sure it's still valid
              // by checking if it exists in the default beats
              const matchingBeat = defaultBeats.find((b: Beat) =>
                String(b.id) === String(currentBeat.id)
              );

              if (matchingBeat) {
                console.log("Error handler - Found matching beat in default beats:", matchingBeat.id);
                // Update the persisted beat with the fresh data to ensure it's fully loaded
                get().selectBeatByObject(matchingBeat);
              } else if (defaultBeats.length > 0) {
                console.log("Error handler - Persisted beat not found in default beats, selecting first beat");
                // If we can't find the persisted beat, select the first beat
                get().selectBeatByObject(defaultBeats[0]);
              }
            }
          } else if (defaultBeats.length > 0) {
            console.log("Error handler - No persisted beat, selecting first default beat:", defaultBeats[0].id);
            // If no persisted beat, select the first beat
            get().selectBeatByObject(defaultBeats[0]);
          }
        }
      },

      // Select beat by ID
      selectBeat: (beatId) => {
        const { beats } = get();
        const beat = beats.find(b => String(b.id) === String(beatId));

        if (beat) {
          get().selectBeatByObject(beat);
        }
      },

      // Select beat by object
      selectBeatByObject: (beat) => {
        if (!beat) {
          console.error("Attempted to select a null or undefined beat");
          return;
        }

        console.log("Selecting beat:", beat.id, beat.title || beat.attributes?.title);

        const { recentBeats } = get();

        // Update recent beats (keep unique, max 5)
        const updatedRecentBeats = [
          beat,
          ...recentBeats.filter(b => String(b.id) !== String(beat.id))
        ].slice(0, 5);

        // Ensure we're setting a complete beat object
        set({
          currentBeat: beat,
          recentBeats: updatedRecentBeats,
          isPlaying: false,
          currentTime: 0,
          duration: 0
        });

        // Log the updated state for debugging
        setTimeout(() => {
          const currentState = get();
          console.log("Updated beat state:", {
            currentBeatId: currentState.currentBeat?.id,
            currentBeatTitle: currentState.currentBeat?.title || currentState.currentBeat?.attributes?.title
          });
        }, 0);
      },

      // Playback controls
      togglePlayPause: () => {
        set(state => ({ isPlaying: !state.isPlaying }));
      },

      setIsPlaying: (isPlaying) => {
        set({ isPlaying });
      },

      setCurrentTime: (currentTime) => {
        set({ currentTime });
      },

      setDuration: (duration) => {
        set({ duration });
      },

      // Favorites management
      addToFavorites: (beatId) => {
        const { beats, favoriteBeats } = get();
        const beat = beats.find(b => String(b.id) === String(beatId));

        if (beat && !favoriteBeats.find(b => String(b.id) === String(beatId))) {
          set({
            favoriteBeats: [...favoriteBeats, beat]
          });
        }
      },

      removeFromFavorites: (beatId) => {
        const { favoriteBeats } = get();
        set({
          favoriteBeats: favoriteBeats.filter(b => String(b.id) !== String(beatId))
        });
      },

      // Set beat audio buffer for waveform visualization
      setBeatAudioBuffer: (buffer) => {
        const currentBuffer = get().beatAudioBuffer;

        // Only update if the buffer is different
        if (!currentBuffer ||
            currentBuffer.length !== buffer.length ||
            currentBuffer.sampleRate !== buffer.sampleRate) {
          set({ beatAudioBuffer: buffer });
        }
      },

      // Set beat volume
      setBeatVolume: (volume: number) => {
        // Clamp volume between 0 and 1
        const clampedVolume = Math.max(0, Math.min(1, volume));
        set({ beatVolume: clampedVolume });

        // Save to localStorage
        try {
          if (typeof window !== 'undefined') {
            localStorage.setItem('beatVolume', clampedVolume.toString());
            console.log(`Saved beat volume to localStorage: ${clampedVolume}`);
          }
        } catch (e) {
          console.warn("Error saving beat volume to localStorage:", e);
        }
      }
    }),
    {
      name: 'freestyle-beat-storage',
      partialize: (state) => ({
        // Only persist these values
        favoriteBeats: state.favoriteBeats,
        recentBeats: state.recentBeats,
        currentBeat: state.currentBeat,
        beatVolume: state.beatVolume,
      }),
    }
  )
);