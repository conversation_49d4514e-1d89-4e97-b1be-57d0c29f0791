import { create } from 'zustand';
import { SyncAudioRecorder } from '../audio/services/syncAudioRecorder';
import { exportBlob } from '../audio/utils/audioExport';
import type { RecordingResult, RecordingState } from '../types/audio';

interface InputDevice {
  value: string;
  label: string;
}

interface AudioStore {
  // Recording state
  recordingState: RecordingState;
  setRecordingState: (state: RecordingState) => void;

  // Recording URLs and data
  recordingUrl: string | null;
  recordingBlob: Blob | null;
  mixedUrl: string | null;
  mixedBuffer: AudioBuffer | null; // Store the mixed AudioBuffer for waveform generation
  mixedBlob: Blob | null; // Store the mixed Blob for download
  duration: number;

  // Input device management
  inputOptions: InputDevice[];
  selectedInput: string;
  setSelectedInput: (deviceId: string) => void;
  loadInputDevices: () => Promise<void>;

  // Audio processing
  level: number;
  setLevel: (level: number) => void;
  gainPercent: number;
  setGainPercent: (percent: number) => void;

  // Process state
  error: string | null;
  setError: (error: string | null) => void;
  mixing: boolean;

  // Beat reference
  beatReady: boolean;
  setBeatReady: (ready: boolean) => void;

  // Actions
  startRecording: (beatUrl?: string | null) => Promise<void>;
  stopRecording: () => Promise<RecordingResult | null>;
  resetRecording: () => void;
  exportRecording: () => void;

  // New fields
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
}

// Helper function to safely access localStorage
const safeGetItem = (key: string, defaultValue: string): string => {
  if (typeof window !== 'undefined') {
    try {
      const value = localStorage.getItem(key);
      return value !== null ? value : defaultValue;
    } catch (e) {
      console.warn(`Error reading ${key} from localStorage:`, e);
      return defaultValue;
    }
  }
  return defaultValue;
};

// Create the store
export const useAudioStore = create<AudioStore>((set, get) => ({
  // Default values
  recordingState: 'idle',
  recordingUrl: null,
  recordingBlob: null,
  mixedUrl: null,
  mixedBuffer: null,
  mixedBlob: null,
  duration: 0,
  inputOptions: [{ value: 'default', label: 'Default Mic' }],
  selectedInput: safeGetItem('selectedInputDevice', 'default'),
  level: 0,
  error: null,
  mixing: false,
  beatReady: false,
  // Load gainPercent from localStorage (micGain) or default to 100%
  gainPercent: (() => {
    const micGain = safeGetItem('micGain', '1.0');
    return parseFloat(micGain) * 100;
  })(),
  isPlaying: false,

  // Setters
  setRecordingState: (state) => set({ recordingState: state }),
  setLevel: (level) => set({ level }),
  setError: (error) => set({ error }),
  setBeatReady: (ready) => set({ beatReady: ready }),
  setGainPercent: (percent) => {
    set({ gainPercent: percent });

    // Save to localStorage as a decimal value (1.0 = 100%)
    try {
      if (typeof window !== 'undefined') {
        const decimalValue = percent / 100;
        localStorage.setItem('micGain', decimalValue.toString());
        console.log(`Saved mic gain to localStorage: ${decimalValue} (${percent}%)`);
      }
    } catch (e) {
      console.warn("Error saving mic gain to localStorage:", e);
    }
  },
  setSelectedInput: (deviceId) => {
    set({ selectedInput: deviceId });
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem('selectedInputDevice', deviceId);
      }
    } catch (e) {
      console.warn("Error saving selected input device to localStorage:", e);
    }
  },
  setIsPlaying: (playing) => set({ isPlaying: playing }),

  // Load input devices
  loadInputDevices: async () => {
    if (!navigator.mediaDevices?.enumerateDevices) return;
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter((d) => d.kind === 'audioinput');
      const options = [
        { value: 'default', label: 'Default Mic' },
        ...audioInputs.map((d) => ({
          value: d.deviceId,
          label: d.label || `Mic ${d.deviceId.slice(-4)}`,
        })),
      ];
      set({ inputOptions: options });
    } catch (err) {
      console.error('Failed to enumerate devices:', err);
    }
  },

  // Start recording
  startRecording: async (beatUrl) => {
    const state = get();
    if (state.recordingState !== 'idle' &&
        state.recordingState !== 'stopped' &&
        state.recordingState !== 'error' &&
        state.recordingState !== 'initializing') {
      console.log("Cannot start recording in current state:", state.recordingState);
      return;
    }

    console.log("AudioStore: Starting recording with beat URL:", beatUrl);

    // First set state to initializing if not already
    if (state.recordingState !== 'initializing') {
      set({
        error: null,
        recordingUrl: null,
        recordingBlob: null,
        duration: 0,
        mixedUrl: null,
        mixing: false,
        recordingState: 'initializing',
      });

      // Give the UI a moment to update
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    // Store the beat URL for mixing later
    if (beatUrl) {
      (window as any).__currentBeatUrl = beatUrl;
      console.log("Stored beat URL in window:", beatUrl);
    }

    try {
      // Create a recorder with a custom error handler
      const recorder = new SyncAudioRecorder({
        deviceId: get().selectedInput,
        onError: (err: string) => {
          console.error("SyncAudioRecorder error:", err);
          set({
            error: err,
            recordingState: 'error'
          });
        }
      });

      console.log("Created AudioRecorder instance");

      // Start recording - this will handle permissions internally
      await recorder.start();
      console.log("AudioRecorder started successfully");

      // Now set state to recording
      set({
        recordingState: 'recording'
      });

      // Store recorder reference in a closure that our stop function can access
      (window as any).__recorderRef = recorder;
      console.log("Stored recorder reference in window");
    } catch (err: any) {
      console.error("Error starting recording:", err);
      set({
        error: err?.message || 'Failed to start recording',
        recordingState: 'error'
      });

      // After a short delay, reset to idle to allow trying again
      setTimeout(() => {
        set({
          recordingState: 'idle',
          error: null
        });
      }, 2000);
    }
  },

  // Stop recording
  stopRecording: async () => {
    console.log("AudioStore: Stopping recording");

    // First set state to stopping
    set({
      recordingState: 'stopping'
    });

    const recorder = (window as any).__recorderRef as SyncAudioRecorder | null;

    if (!recorder) {
      console.error("No active recorder found when trying to stop recording");
      set({
        error: 'No active recorder found',
        recordingState: 'error'
      });

      // After a short delay, reset to idle
      setTimeout(() => {
        set({ recordingState: 'idle', error: null });
      }, 2000);

      return null; // CRITICAL FIX: Return null instead of undefined
    }

    try {
      console.log("Stopping all active media streams before stopping recorder");

      // Ensure we stop all active media streams before stopping the recorder
      // This helps prevent issues when previewing the beat after recording
      if (navigator.mediaDevices) {
        try {
          // First, try to stop any existing streams that might be active
          // SyncAudioRecorder handles this internally, so we don't need to do it here
          // Just call stop() which will clean up the media stream

          // Then get a new stream and immediately stop it to force microphone release
          const streams = await navigator.mediaDevices.getUserMedia({ audio: true })
            .then(stream => {
              // Stop all tracks in the newly acquired stream to release the microphone
              stream.getTracks().forEach(track => {
                track.stop();
                track.enabled = false;
                console.log(`Explicitly stopped additional audio track: ${track.kind}`);
              });
              return true;
            })
            .catch(err => {
              console.warn("Could not get additional media stream to stop:", err);
              return false;
            });

          console.log("Explicitly stopped all additional media streams:", streams);
        } catch (streamError) {
          console.warn("Error stopping additional media streams:", streamError);
        }
      }

      console.log("Calling recorder.stop()");
      const result: RecordingResult | null = await recorder.stop();

      if (result) {
        console.log("Recording stopped successfully:", {
          url: result.url,
          mimeType: result.mimeType,
          duration: result.duration,
          blobSize: result.blob.size
        });

        // Don't test the audio URL - it can cause errors
        // Just log the URL for debugging
        console.log("Recording URL created:", result.url);

        // CRITICAL: Set the recording URL and state
        // This is a critical step to ensure the recording URL is available for preview
        set({
          recordingUrl: result.url,
          recordingBlob: result.blob,
          duration: result.duration,
          recordingState: 'stopped',
        });

        console.log("Updated store with recording URL and blob");

        // Also set the recording URL in the window object for redundancy
        // This ensures it's available even if the store state is lost
        (window as any).__currentRecordingUrl = result.url;
        console.log("Stored recording URL in window:", result.url);

        // Store the mixed URL for preview
        (window as any).__mixedRecordingUrl = result.url;
      }

      // Clean up recorder reference
      (window as any).__recorderRef = null;

      return result;
    } catch (err: any) {
      console.error("Error stopping recording:", err);
      set({
        recordingState: 'error',
        error: err?.message || 'Error stopping recording'
      });

      // Clean up recorder reference
      (window as any).__recorderRef = null;

      return null;
    }
  },

  // Reset recording
  resetRecording: () => {
    set({
      recordingState: 'idle',
      recordingUrl: null,
      recordingBlob: null,
      mixedUrl: null,
      mixedBuffer: null,
      mixedBlob: null,
      duration: 0,
      error: null
    });

    // Also clear global references
    (window as any).__mixedRecordingUrl = null;
    (window as any).__mixedRecordingBuffer = null;
    (window as any).__mixedDuration = null;
    (window as any).__mixedBlob = null;
    (window as any).__currentBeatUrl = null;
  },

  // Export recording
  exportRecording: () => {
    const { recordingBlob, mixedBlob } = get();

    // Prefer the mixed blob if available
    if (mixedBlob) {
      exportBlob(mixedBlob, 'freestyle-recording.wav');
    } else if (recordingBlob) {
      exportBlob(recordingBlob, 'freestyle-recording.webm');
    }
  }
}));

// Legacy export removed