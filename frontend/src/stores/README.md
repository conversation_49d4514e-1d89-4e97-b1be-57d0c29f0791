# Freestyle App State Management

This directory contains Zustand stores for managing application state.

## Stores Overview

### `audioStore.ts`
Manages audio recording state, microphone input, and audio processing.

### `beatStore.ts`
Manages beat selection, playback, and synchronization.

### `userStore.ts`
Manages user authentication, premium status, and user information.

### `aiStore.ts`
Manages AI suggestion state, connection status, and usage limits.

### `previewStore.ts`
Manages preview mode state for the RecordingPreviewPlayer component.

## Preview Store

The `previewStore.ts` file manages the state for the preview mode in the freestyle app. It controls when the app enters and exits preview mode, which affects the visibility of various UI components.

### State Structure
- `isPreviewMode`: Boolean flag indicating whether the app is in preview mode
- `previewRecordingUrl`: URL of the recording being previewed
- `previewBeatUrl`: URL of the beat being previewed
- `setPreviewMode`: Function to set the preview mode state
- `setPreviewData`: Function to set preview data and enter preview mode
- `clearPreviewData`: Function to clear preview data and exit preview mode

### Usage Example

```tsx
import { usePreviewStore } from '../../stores/previewStore';

// Get preview mode state
const isPreviewMode = usePreviewStore(state => state.isPreviewMode);

// Set preview mode
const setPreviewMode = usePreviewStore(state => state.setPreviewMode);
setPreviewMode(true);

// Set preview data
const setPreviewData = usePreviewStore(state => state.setPreviewData);
setPreviewData(recordingUrl, beatUrl);

// Clear preview data
const clearPreviewData = usePreviewStore(state => state.clearPreviewData);
clearPreviewData();
```

### Integration
The preview store is used by:
- `RecordingPreviewPlayer`: Sets preview mode when mounted/unmounted
- `RecordingSystem`: Sets preview mode when stopping recording
- `session/page.tsx`: Uses preview mode to hide other UI elements

## Best Practices
- Use selectors to access only the state you need
- Use actions to update state
- Keep state normalized and minimal
- Use TypeScript interfaces for type safety
