# Web Worker API Documentation

## Overview

The Web Worker API provides a set of functions for audio processing that run in a separate thread from the main UI thread. This ensures smooth UI performance even during CPU-intensive audio processing tasks.

## API Reference

### Core Audio Processing

#### `decodeAudio(arrayBuffer: ArrayBuffer): Promise<AudioBuffer>`

Decodes an audio file from an ArrayBuffer into an AudioBuffer.

**Parameters:**
- `arrayBuffer`: The raw audio data as an ArrayBuffer

**Returns:**
- Promise resolving to an AudioBuffer containing the decoded audio

**Example:**
```javascript
const response = await fetch('beat.mp3');
const arrayBuffer = await response.arrayBuffer();
const audioBuffer = await worker.decodeAudio(arrayBuffer);
```

#### `mixAudioBuffers(recordingBuffer: AudioBuffer, beatBuffer: AudioBuffer, recordingGain: number, beatGain: number): Promise<AudioBuffer>`

Mixes two audio buffers with specified gain values.

**Parameters:**
- `recordingBuffer`: The recording (vocals) as an AudioBuffer
- `beatBuffer`: The beat (instrumental) as an AudioBuffer
- `recordingGain`: Gain to apply to the recording (0-2, where 1.0 is 100%)
- `beatGain`: Gain to apply to the beat (0-2, where 1.0 is 100%)

**Returns:**
- Promise resolving to an AudioBuffer containing the mixed audio

**Example:**
```javascript
const mixedBuffer = await worker.mixAudioBuffers(
  recordingBuffer,
  beatBuffer,
  1.0,  // 100% recording volume
  0.8   // 80% beat volume
);
```

#### `audioBufferToWav(buffer: AudioBuffer): Promise<Blob>`

Converts an AudioBuffer to a WAV Blob.

**Parameters:**
- `buffer`: The audio data as an AudioBuffer

**Returns:**
- Promise resolving to a Blob containing the WAV data

**Example:**
```javascript
const wavBlob = await worker.audioBufferToWav(mixedBuffer);
const url = URL.createObjectURL(wavBlob);
```

### Audio Effects

#### `applyCompression(buffer: AudioBuffer, threshold: number, ratio: number): Promise<AudioBuffer>`

Applies dynamic range compression to an audio buffer.

**Parameters:**
- `buffer`: The audio data as an AudioBuffer
- `threshold`: Compression threshold (0-1, where lower values compress more)
- `ratio`: Compression ratio (higher values compress more)

**Returns:**
- Promise resolving to an AudioBuffer with compression applied

**Example:**
```javascript
const compressedBuffer = await worker.applyCompression(
  mixedBuffer,
  0.5,  // Threshold
  4     // Ratio
);
```

#### `applyReverb(buffer: AudioBuffer, mix: number, decay: number): Promise<AudioBuffer>`

Applies reverb effect to an audio buffer.

**Parameters:**
- `buffer`: The audio data as an AudioBuffer
- `mix`: Wet/dry mix (0-1, where 0 is dry and 1 is wet)
- `decay`: Reverb decay time in seconds

**Returns:**
- Promise resolving to an AudioBuffer with reverb applied

**Example:**
```javascript
const reverbBuffer = await worker.applyReverb(
  mixedBuffer,
  0.3,  // 30% wet
  2.0   // 2 seconds decay
);
```

#### `applyStereoWidening(buffer: AudioBuffer, width: number): Promise<AudioBuffer>`

Applies stereo widening effect to an audio buffer.

**Parameters:**
- `buffer`: The audio data as an AudioBuffer
- `width`: Stereo width (0-1, where higher values create wider stereo image)

**Returns:**
- Promise resolving to an AudioBuffer with stereo widening applied

**Example:**
```javascript
const widenedBuffer = await worker.applyStereoWidening(
  mixedBuffer,
  0.5  // 50% widening
);
```

### Analysis and Visualization

#### `generateWaveform(buffer: AudioBuffer, numPoints: number): Promise<number[][]>`

Generates waveform data for visualization.

**Parameters:**
- `buffer`: The audio data as an AudioBuffer
- `numPoints`: Number of data points to generate

**Returns:**
- Promise resolving to an array of [min, max] pairs representing the waveform

**Example:**
```javascript
const waveformData = await worker.generateWaveform(
  mixedBuffer,
  1000  // 1000 data points
);
```

#### `analyzeAudioLevel(buffer: AudioBuffer): Promise<{rms: number, db: number, peak: number}>`

Analyzes audio levels in a buffer.

**Parameters:**
- `buffer`: The audio data as an AudioBuffer

**Returns:**
- Promise resolving to an object containing RMS level, dB level, and peak level

**Example:**
```javascript
const levels = await worker.analyzeAudioLevel(mixedBuffer);
console.log(`RMS: ${levels.rms}, dB: ${levels.db}, Peak: ${levels.peak}`);
```

### High-Level Operations

#### `processRecordingForPreview(recordingArrayBuffer: ArrayBuffer, beatArrayBuffer: ArrayBuffer, recordingGain: number, beatGain: number, options?: object): Promise<object>`

Processes a recording for preview with a beat.

**Parameters:**
- `recordingArrayBuffer`: The recording data as an ArrayBuffer
- `beatArrayBuffer`: The beat data as an ArrayBuffer
- `recordingGain`: Gain to apply to the recording (0-2)
- `beatGain`: Gain to apply to the beat (0-2)
- `options`: Optional processing options:
  - `applyCompression`: Whether to apply compression (default: true)
  - `compressionThreshold`: Compression threshold (default: 0.5)
  - `compressionRatio`: Compression ratio (default: 4)
  - `applyReverb`: Whether to apply reverb (default: false)
  - `reverbMix`: Reverb mix (default: 0.2)
  - `applyStereoWidening`: Whether to apply stereo widening (default: false)
  - `stereoWidth`: Stereo width (default: 0.3)

**Returns:**
- Promise resolving to an object containing:
  - `mixedBuffer`: The mixed audio as an AudioBuffer
  - `mixedBlob`: The mixed audio as a WAV Blob
  - `duration`: The duration of the mixed audio in seconds
  - `waveformData`: Waveform data for visualization
  - `levels`: Audio level analysis

**Example:**
```javascript
const result = await worker.processRecordingForPreview(
  recordingArrayBuffer,
  beatArrayBuffer,
  1.0,  // 100% recording volume
  0.8,  // 80% beat volume
  {
    applyCompression: true,
    compressionThreshold: 0.5,
    compressionRatio: 4,
    applyReverb: true,
    reverbMix: 0.2
  }
);
```

#### `exportRecording(recordingArrayBuffer: ArrayBuffer, beatArrayBuffer: ArrayBuffer, metadata?: object, options?: object): Promise<object>`

Exports a recording with metadata.

**Parameters:**
- `recordingArrayBuffer`: The recording data as an ArrayBuffer
- `beatArrayBuffer`: The beat data as an ArrayBuffer
- `metadata`: Optional metadata:
  - `title`: Recording title (default: "Freestyle Recording")
  - `artist`: Artist name (default: "Freestyle App User")
  - `beatTitle`: Beat title (default: "Unknown Beat")
  - `beatProducer`: Beat producer (default: "Unknown Producer")
  - `date`: Recording date (default: current date)
- `options`: Optional processing options (same as `processRecordingForPreview`)

**Returns:**
- Promise resolving to an object containing:
  - `blob`: The exported audio as a WAV Blob
  - `duration`: The duration of the audio in seconds
  - `metadata`: The metadata applied to the export

**Example:**
```javascript
const result = await worker.exportRecording(
  recordingArrayBuffer,
  beatArrayBuffer,
  {
    title: "My Freestyle",
    artist: "MC Example",
    beatTitle: "Summer Vibes",
    beatProducer: "Beat Master"
  },
  {
    recordingGain: 1.0,
    beatGain: 0.8,
    applyCompression: true
  }
);
```
