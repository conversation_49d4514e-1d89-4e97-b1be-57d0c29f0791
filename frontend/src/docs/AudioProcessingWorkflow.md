# Audio Processing Workflow

This document outlines the audio processing workflow in the Freestyle App, including component responsibilities and data flow.

## Component Responsibilities

### RecordingSystem
- **Primary Role**: Central coordinator component
- **Responsibilities**:
  - State management for the recording workflow
  - Coordination between UI and audio components
  - State machine for recording transitions (idle → recording → stopping → preview)
  - Backend API integration (sessions, uploads)
  - Mode switching between recording and preview
- **Integration Points**:
  - Uses MicVinylIntegrated for visual interface
  - Uses FreestyleRecorder for audio processing
  - Renders FreestylePlaybackPlayer when in preview mode
  - Integrates with multiple stores (audio, beat, preview)

### FreestyleRecorder
- **Primary Role**: Core audio processing component
- **Responsibilities**:
  - Beat audio loading and decoding
  - Audio buffer creation for visualization
  - Beat playback via transport controls
  - Time synchronization for beat and recording
- **Integration Points**:
  - Exposes transport controls via transportRef
  - Provides audio buffers via onAudioBufferLoaded callback
  - Provides time updates via onTimeUpdate callback
- **Note**: This is a "headless" component that provides audio functionality without any UI elements

### FreestylePlaybackPlayer
- **Primary Role**: Audio playback component for freestyle recordings
- **Responsibilities**:
  - Playback of mixed recording (vocals + beat)
  - Waveform visualization with seeking
  - Download functionality for the mixed recording
  - Record again button to exit preview mode
- **Integration Points**:
  - Uses CustomWaveform for visualization
  - Uses AudioPlayer utility functions for playback
  - Integrates with audio and preview stores

### MicVinylIntegrated
- **Primary Role**: Main visual interface for the recording system
- **Responsibilities**:
  - Providing the primary user interface for the recording experience
  - Handling microphone button clicks to start/stop recording
  - Handling vinyl record clicks to open beat selection
  - Displaying visual feedback for recording state (idle, recording, error)
  - Visualizing audio input level during recording
- **Integration Points**:
  - Receives recordingState from RecordingSystem
  - Calls onMicClick callback when microphone is clicked
  - Calls onBeatSelect callback when a beat is selected
  - Integrates with TonearmComponent for visual feedback
  - Uses useBeatStore for beat information

### CustomWaveform
- **Primary Role**: Audio visualization component
- **Responsibilities**:
  - Renders a gradient-filled waveform with animated effects
  - Supports seeking through the audio by clicking on the waveform
  - Displays a playhead to indicate the current position
  - Adapts to container size for responsive layout
  - Uses separate canvases for waveform and playhead for better performance
- **Integration Points**:
  - Used in FreestylePlaybackPlayer for recording visualization
  - Receives audio data and playback state from parent components
  - Calls onSeek when the user clicks to seek in the audio
  - Provides CSS classes for external playhead manipulation

## Data Flow

### Recording Process
1. User clicks the microphone button in the MicVinylIntegrated component
2. RecordingSystem handles the click and updates the recording state
3. RecordingSystem calls startRecording in the audioStore
4. FreestyleRecorder starts recording and playing the beat
5. Audio input level is visualized in the MicVinylIntegrated component
6. User clicks the microphone button again to stop recording
7. RecordingSystem handles the click and updates the recording state
8. RecordingSystem calls stopRecording in the audioStore
9. RecordingSystem creates a mixed buffer of the recording and beat
10. RecordingSystem transitions to preview mode

### Preview Process
1. RecordingSystem renders the FreestylePlaybackPlayer component
2. FreestylePlaybackPlayer initializes with the mixed recording URL
3. FreestylePlaybackPlayer renders the CustomWaveform component
4. User can play/pause the recording, seek through it, and download it
5. User can click the "Record Again" button to exit preview mode
6. RecordingSystem transitions back to recording mode

### Audio Stores
- **audioStore**: Manages recording state, audio URLs, and blobs
- **beatStore**: Manages beat selection and playback state
- **previewStore**: Manages preview mode state and URLs

## Utility Functions

### AudioPlayer.ts
- **playAudioWithMixedHandling**: Plays audio with proper handling of mixed recordings
- **getCurrentTime**: Gets the current time from the most reliable source
- **getEffectiveDuration**: Gets the effective duration from the most reliable source
- **updatePlayhead**: Updates the playhead position based on the current time

### audioMixer.ts
- **createMixedBuffer**: Creates a mixed buffer of the recording and beat
- **mixAudioBuffers**: Mixes two audio buffers together

## Best Practices

1. **Audio Element Initialization**:
   - Always create audio elements in useEffect
   - Add event listeners for better debugging and state management
   - Clean up event listeners on unmount
   - Use retry mechanisms for audio playback operations
   - Handle play interruptions gracefully

2. **Animation Frame Management**:
   - Start animation frames in useEffect
   - Cancel animation frames on unmount
   - Use requestAnimationFrame for smooth animations
   - Separate animation frame logic from audio playback logic
   - Use separate canvases for static and dynamic content

3. **Error Handling**:
   - Add try/catch blocks around audio operations
   - Provide user-friendly error messages
   - Fall back to default values when necessary
   - Log detailed error information for debugging
   - Implement non-fatal error recovery

4. **State Management**:
   - Use stores for global state
   - Use local state for component-specific state
   - Update state in useEffect when necessary
   - Ensure state updates don't cause unnecessary re-renders
   - Use window object for critical state that needs to persist

5. **Performance Optimization**:
   - Use memoization for expensive calculations
   - Use useCallback for event handlers
   - Use useMemo for derived values
   - Separate rendering concerns (e.g., waveform vs. playhead)
   - Minimize DOM updates during animations

6. **Audio Playback Robustness**:
   - Implement retry mechanisms for play operations
   - Handle browser autoplay restrictions
   - Ensure proper cleanup of audio resources
   - Use appropriate delays for synchronization
   - Validate audio sources before playback
