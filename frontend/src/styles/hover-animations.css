/**
 * Hover Animations
 *
 * A collection of hover animations for interactive elements
 */

/* Keyframe Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 0.7; transform: scale(0.98); }
  100% { opacity: 1; transform: scale(1.02); }
}

@keyframes wave {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

/* Base hover transition for all interactive elements */
.hover-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Subtle scale effect */
.hover-scale {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hover-scale:hover {
  transform: scale(1.03);
}

.hover-scale:active {
  transform: scale(0.98);
}

/* Lift effect */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(95, 111, 255, 0.2);
}

.hover-lift:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(95, 111, 255, 0.15);
}

/* Glow effect */
.hover-glow {
  transition: box-shadow 0.3s ease, border-color 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 12px rgba(95, 111, 255, 0.4);
  border-color: rgba(95, 111, 255, 0.8);
}

/* Brightness effect */
.hover-bright {
  transition: filter 0.3s ease;
}

.hover-bright:hover {
  filter: brightness(1.1);
}

.hover-bright:active {
  filter: brightness(0.95);
}

/* Button specific hover */
.button-hover {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.button-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(95, 111, 255, 0.25);
}

.button-hover:active {
  transform: translateY(0);
  box-shadow: 0 3px 8px rgba(95, 111, 255, 0.15);
}

.button-hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.button-hover:hover::after {
  transform: translateX(100%);
}

/* Dropdown hover */
.dropdown-hover {
  transition: all 0.3s ease;
}

.dropdown-hover:hover {
  background-color: rgba(95, 111, 255, 0.1);
  border-color: rgba(95, 111, 255, 0.8);
}

/* Slider hover */
.slider-hover::-webkit-slider-thumb {
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.slider-hover::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  background-color: #7a6fff;
}

.slider-hover::-moz-range-thumb {
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.slider-hover::-moz-range-thumb:hover {
  transform: scale(1.2);
  background-color: #7a6fff;
}

/* Card hover */
.card-hover {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(95, 111, 255, 0.2);
}

/* Icon hover */
.icon-hover {
  transition: transform 0.3s ease, color 0.3s ease;
}

.icon-hover:hover {
  transform: scale(1.15);
  color: #7a6fff;
}

.icon-hover:active {
  transform: scale(0.95);
}

/* Beat card hover */
.beat-card-hover {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.beat-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(95, 111, 255, 0.3);
}

.beat-card-hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transform: translateX(-100%);
  transition: transform 0.8s ease;
}

.beat-card-hover:hover::after {
  transform: translateX(100%);
}

/* Vinyl record spin animation */
@keyframes vinylSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vinyl-spin {
  animation: vinylSpin 4s linear infinite;
  animation-play-state: paused;
}

.vinyl-spin.playing {
  animation-play-state: running;
}

/* Beat pill hover */
.beat-pill-hover {
  transition: all 0.2s ease;
}

.beat-pill-hover:hover {
  transform: translateY(-2px);
  filter: brightness(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
