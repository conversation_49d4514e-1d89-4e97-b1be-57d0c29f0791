/* Global styles for the application */

/* Import the hover animations */
@import './hover-animations.css';

/* Base styles */
html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: #0F1A30; /* Deep Midnight Background */
  background: linear-gradient(to bottom, #0F1A30, #070D19);
  color: #fff;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spinRecord {
  0% { transform: rotate(0deg) translateZ(0); }
  100% { transform: rotate(360deg) translateZ(0); }
}

/* Vinyl idle animation - subtle breathing and very slow rotation */
@keyframes vinylIdle {
  0% { transform: scale(1.00) rotate(0deg) translateZ(0); }
  50% { transform: scale(1.02) rotate(180deg) translateZ(0); }
  100% { transform: scale(1.00) rotate(360deg) translateZ(0); }
}

/* Define a CSS variable for level color to use in the CSS classes */
:root {
  --level-color: #8A8AFF;
}

@keyframes pulse {
  0% { opacity: 0.7; transform: scale(0.98) translateY(0); }
  50% { opacity: 1; transform: scale(1.02) translateY(-2px); }
  100% { opacity: 0.7; transform: scale(0.98) translateY(0); }
}

@keyframes wave {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

@keyframes recordingPulse {
  0% { transform: scale(1) translateZ(0); opacity: 0.8; }
  70% { transform: scale(1.15) translateZ(0); opacity: 0.2; }
  100% { transform: scale(1.3) translateZ(0); opacity: 0; }
}

/* Background animation */
@keyframes bgFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 50% 100%; }
  50% { background-position: 100% 50%; }
  75% { background-position: 50% 0%; }
  100% { background-position: 0% 50%; }
}

/* Loading animation */
@keyframes loadingPulse {
  0% { transform: scale(0.8); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0.7; }
}

/* Simple fade-in animation for background */
@keyframes fadeInBackground {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* Elegant shimmer effect for microphone */
@keyframes micShimmer {
  0% { filter: drop-shadow(0 0 2px rgba(229, 228, 226, 0.2)); }
  50% { filter: drop-shadow(0 0 5px rgba(229, 228, 226, 0.5)); }
  100% { filter: drop-shadow(0 0 2px rgba(229, 228, 226, 0.2)); }
}

/* Main container fade-in - synchronized with no delays */
.fade-in-container {
  opacity: 0;
  animation: fadeIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  will-change: opacity, transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Child elements fade-in with NO delay - synchronized with parent */
.fade-in-container > * {
  opacity: 0;
  animation: fadeIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  /* Removed delay to ensure synchronous loading */
  will-change: opacity, transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* AI controls fade-in */
.fade-in-controls {
  opacity: 0;
  animation: fadeIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  animation-delay: 0.1s;
  will-change: opacity, transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Background fade-in */
.fade-in-background {
  opacity: 0;
  animation: fadeInBackground 1.5s ease-in forwards;
  will-change: opacity;
}

/* Waveform container fade-in */
.waveform-container {
  opacity: 0;
  animation: fadeIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  animation-delay: 0.1s;
  will-change: opacity, transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  width: 100% !important; /* Force 100% width */
  /* Removed min-width and max-width to allow proper scaling in different contexts */
}

/* AI suggestion container fade-in */
.ai-suggestion-container {
  opacity: 0;
  animation: fadeIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  animation-delay: 0.1s;
  will-change: opacity, transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Vinyl and Mic component styles */
.vinyl-button-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(95, 111, 255, 0.5);
  border-radius: 50%;
}
.mic-button-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(138, 138, 255, 0.7);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(1.05) !important;
}

.vinyl-component-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.beat-pill-hover {
  transition: all 0.2s ease;
}

.beat-pill-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Freestyle recorder styles */
.freestyle-recorder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  margin: -20px auto 0; /* Negative top margin to move component up */
  position: relative;
}

/* Integrated Mic and Vinyl styles */
.mic-vinyl-integrated {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
}

/* Optimized animations with hardware acceleration */
.vinyl-spinning {
  animation: spinRecord 2.5s linear infinite;
  transform: translateZ(0); /* Force hardware acceleration */
}

.vinyl-idle {
  animation: vinylIdle 8s ease-in-out infinite;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Vinyl transition animation for state changes */
.vinyl-transition {
  animation: vinylTransition 0.8s ease-out;
  transform: translateZ(0); /* Force hardware acceleration */
}

@keyframes vinylTransition {
  0% { filter: brightness(1); }
  30% { filter: brightness(1.5); }
  100% { filter: brightness(1); }
}

.recording-pulse {
  animation: recordingPulse 2s infinite;
  transform: translateZ(0); /* Force hardware acceleration */
}

.mic-glow {
  filter: blur(8px);
  transition: opacity 0.3s, r 0.3s;
  transform: translateZ(0); /* Force hardware acceleration */
}

.mic-glow-outer {
  filter: blur(15px);
  transform: translateZ(0); /* Force hardware acceleration */
}

.mic-body-recording {
  filter: drop-shadow(0 0 8px var(--level-color, #8A8AFF));
  transform: translateZ(0); /* Force hardware acceleration */
}

.mic-body-idle {
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
  transform: translateZ(0); /* Force hardware acceleration */
}

.mic-head-recording {
  filter: drop-shadow(0 0 3px var(--level-color, #8A8AFF));
  transform: translateZ(0); /* Force hardware acceleration */
}

.mic-head-idle {
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Microphone button styles */
.mic-button-recording {
  box-shadow: 0 0 25px var(--level-color, #8A8AFF);
  transform: translate(-50%, -50%) translateZ(10px) scale(1.1) !important;
  cursor: pointer !important; /* Force pointer cursor */
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease !important;
  pointer-events: auto !important; /* Ensure clicks are registered */
}

.mic-button-idle {
  box-shadow: 0 0 18px rgba(0, 0, 0, 0.8) !important; /* Consistent shadow */
  transform: translate(-50%, -50%) !important; /* Simpler transform */
  cursor: pointer !important; /* Force pointer cursor */
  transition: opacity 0.2s ease-in-out !important; /* Simple transition */
  pointer-events: auto !important; /* Ensure clicks are registered */
}

/* Style for when microphone permissions are being requested */
.mic-button-initializing {
  box-shadow: 0 0 15px rgba(230, 199, 112, 0.7) !important; /* Gold glow to indicate waiting for permission */
  transform: translate(-50%, -50%) !important; /* Simpler transform */
  cursor: wait !important; /* Wait cursor to indicate processing */
  transition: none !important; /* No transition during load */
  pointer-events: auto !important; /* Ensure clicks are registered */
  animation: micButtonPulse 1.5s ease-in-out infinite !important; /* Subtle pulsing animation */
}

.mic-button-disabled {
  cursor: not-allowed !important;
  opacity: 0.8;
}

/* Enhanced hover effects for better user feedback */
.mic-button-idle:hover:not(.mic-button-disabled) {
  box-shadow: 0 0 25px rgba(138, 138, 255, 0.8) !important;
  transform: translate(-50%, -50%) translateZ(8px) scale(1.08) !important;
  border-color: rgba(138, 138, 255, 1) !important;
  cursor: pointer;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease, border-color 0.3s ease !important;
}

.mic-button-recording:hover:not(.mic-button-disabled) {
  box-shadow: 0 0 30px var(--level-color, #8A8AFF);
  transform: translate(-50%, -50%) translateZ(12px) scale(1.12) !important;
  cursor: pointer;
}

/* Vinyl clickable area - enhanced hover effect to indicate it's for beat selection */
.vinyl-clickable:hover {
  stroke-width: 3 !important;
  stroke: rgba(138, 138, 255, 1) !important;
  filter: drop-shadow(0 0 15px rgba(138, 138, 255, 0.6)) !important;
  opacity: 1 !important;
  transform: scale(1.02) !important;
}

/* Select beat button styles */
.select-beat-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, rgba(95, 111, 255, 0.3), rgba(182, 91, 255, 0.3)) !important;
}

.select-beat-button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

@keyframes recordingPulse {
  0% { opacity: 0.7; transform: scale(1) translateZ(0); }
  50% { opacity: 0.3; transform: scale(1.05) translateZ(0); }
  100% { opacity: 0.7; transform: scale(1) translateZ(0); }
}

@keyframes pulse {
  0% { opacity: 0.8; transform: translateY(0) translateZ(0); }
  50% { opacity: 0.5; transform: translateY(-2px) translateZ(0); }
  100% { opacity: 0.8; transform: translateY(0) translateZ(0); }
}

/* Removed micButtonAppear animation as we're using direct styling */

/* Animation for microphone button when waiting for permissions */
@keyframes micButtonPulse {
  0% {
    box-shadow: 0 0 15px rgba(230, 199, 112, 0.5);
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(230, 199, 112, 0.8);
    transform: translate(-50%, -50%) scale(1.03);
  }
  100% {
    box-shadow: 0 0 15px rgba(230, 199, 112, 0.5);
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  .vinyl-component-container {
    transform: scale(0.9);
  }

  .mic-vinyl-integrated {
    transform: scale(0.9);
  }
}

@media (max-width: 480px) {
  .mic-vinyl-integrated {
    transform: scale(0.8);
  }
}
