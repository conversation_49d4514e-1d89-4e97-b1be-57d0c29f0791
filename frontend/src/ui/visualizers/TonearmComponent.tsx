/**
 * TonearmComponent: A premium tonearm for the vinyl record player interface.
 *
 * This component renders a realistic tonearm positioned at the right
 * edge of the vinyl record, with the base at the top right and the arm
 * extending downward adjacent to the vinyl.
 *
 * Visual Design:
 * - Realistic design based on professional turntable tonearms
 * - Dark base with metallic tonearm elements and gold accents
 * - Enhanced gold trim to match the record's edge
 * - Refined counterweight with subtle 3D effect
 * - Diamond-shaped needle tip for precision appearance
 * - Interactive hover effects and micro-animations
 * - Smooth animation when transitioning to playing state
 */
import React, { useEffect, useState } from 'react';
import './TonearmComponent.css';

export interface TonearmComponentProps {
  /** Whether the component is disabled */
  disabled?: boolean;

  /** Whether audio is currently playing */
  isPlaying?: boolean;
}

export const TonearmComponent: React.FC<TonearmComponentProps> = ({
  disabled = false,
  isPlaying = false
}) => {
  // State for load animation
  const [isLoaded, setIsLoaded] = useState(false);

  // Apply loaded state after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 300); // Short delay for the load animation

    return () => clearTimeout(timer);
  }, []);
  return (
    <div
      className={`tonearm-container ${disabled ? 'tonearm-disabled' : ''} ${isLoaded ? 'tonearm-loaded' : ''}`}
      role="img"
      aria-label="Record player tonearm"
    >
      {/* SVG for tonearm */}
      <svg
        width="100"
        height="370"
        viewBox="0 0 120 450"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={`tonearm-svg ${isPlaying ? 'tonearm-playing' : ''}`}
      >
        {/* Definitions for gradients and filters */}
        <defs>
          {/* Enhanced metallic gradient for tonearm - more premium finish */}
          <linearGradient id="tonearmMetalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#E0E2E5" />
            <stop offset="50%" stopColor="#B5B9BE" />
            <stop offset="100%" stopColor="#A9ADB2" />
          </linearGradient>

          {/* Metallic highlight gradient - enhanced for premium look */}
          <linearGradient id="tonearmHighlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#FFFFFF" stopOpacity="0.2" />
          </linearGradient>

          {/* Dark base gradient - matching our theme */}
          <linearGradient id="baseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#222222" />
            <stop offset="50%" stopColor="#1A1A1A" />
            <stop offset="100%" stopColor="#111111" />
          </linearGradient>

          {/* Enhanced gold accent gradient - matching our vinyl gold accents with more brilliance */}
          <linearGradient id="goldAccentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#F6D780" />
            <stop offset="50%" stopColor="#E6C770" />
            <stop offset="100%" stopColor="#D4AF37" />
          </linearGradient>

          {/* Brighter gold accent for highlights */}
          <linearGradient id="goldHighlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFE68A" />
            <stop offset="50%" stopColor="#F6D780" />
            <stop offset="100%" stopColor="#E6C770" />
          </linearGradient>

          {/* Counterweight gradient - darker metallic finish */}
          <linearGradient id="counterweightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#444444" />
            <stop offset="50%" stopColor="#333333" />
            <stop offset="100%" stopColor="#222222" />
          </linearGradient>

          {/* Diamond tip gradient */}
          <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" />
            <stop offset="50%" stopColor="#F6D780" />
            <stop offset="100%" stopColor="#E6C770" />
          </linearGradient>

          {/* Shadow filter - enhanced */}
          <filter id="tonearmShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="1" dy="1" stdDeviation="1" floodOpacity="0.4" />
          </filter>

          {/* Enhanced drop shadow for depth */}
          <filter id="enhancedShadow" x="-30%" y="-30%" width="160%" height="160%">
            <feDropShadow dx="0" dy="2" stdDeviation="2" floodOpacity="0.3" />
          </filter>

          {/* Gold glow filter */}
          <filter id="goldGlow" x="-30%" y="-30%" width="160%" height="160%">
            <feGaussianBlur stdDeviation="2" result="blur" />
            <feFlood floodColor="#E6C770" floodOpacity="0.5" result="goldColor" />
            <feComposite in="goldColor" in2="blur" operator="in" result="softGlow" />
            <feComposite in="SourceGraphic" in2="softGlow" operator="over" />
          </filter>

          {/* 3D effect for pivot base - enhanced */}
          <filter id="pivotBaseEffect" x="-30%" y="-30%" width="160%" height="160%">
            <feGaussianBlur stdDeviation="1" result="blur" />
            <feSpecularLighting result="specOut" in="blur" specularExponent="20" lightingColor="#FFFFFF">
              <fePointLight x="60" y="20" z="20" />
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0" />
          </filter>

          {/* Diamond shine effect */}
          <filter id="diamondShine" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.5" result="blur" />
            <feSpecularLighting result="specOut" in="blur" specularExponent="30" lightingColor="#FFFFFF">
              <fePointLight x="-32" y="0" z="10" />
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0" />
          </filter>
        </defs>

        {/* Base outer gold accent trim - keeping only the gold accent, no backdrop */}
        <circle
          cx="60"
          cy="30"
          r="25"
          fill="none"
          stroke="url(#goldAccentGradient)"
          strokeWidth="1.5"
          strokeOpacity="0.9"
          filter="url(#goldGlow)"
          className="tonearm-gold-accent"
        />

        {/* Base gold accent trim - enhanced */}
        <circle
          cx="60"
          cy="30"
          r="20"
          fill="none"
          stroke="url(#goldAccentGradient)"
          strokeWidth="1.2"
          strokeOpacity="0.8"
          className="tonearm-gold-accent"
        />

        {/* Additional inner gold accent ring */}
        <circle
          cx="60"
          cy="30"
          r="16"
          fill="none"
          stroke="url(#goldAccentGradient)"
          strokeWidth="0.8"
          strokeOpacity="0.7"
          className="tonearm-gold-accent"
        />

        {/* Tonearm - main arm element */}
        <g
          className="tonearm-arm"
          style={{
            transformOrigin: '60px 30px',
          }}
        >
          {/* Tonearm pivot housing - transparent */}
          <circle
            cx="60"
            cy="30"
            r="12"
            fill="none"
            className="tonearm-pivot-housing"
          />

          {/* Pivot housing accent - enhanced with stronger glow */}
          <circle
            cx="60"
            cy="30"
            r="12"
            fill="none"
            stroke="url(#goldHighlightGradient)"
            strokeWidth="1.5"
            strokeOpacity="1"
            filter="url(#goldGlow)"
            className="tonearm-gold-accent"
          />

          {/* Tonearm pivot point with metallic finish */}
          <circle
            cx="60"
            cy="30"
            r="5"
            fill="url(#tonearmMetalGradient)"
            filter="url(#pivotBaseEffect)"
            className="tonearm-pivot-point"
          />

          {/* Pivot point gold accent - enhanced */}
          <circle
            cx="60"
            cy="30"
            r="5"
            fill="none"
            stroke="url(#goldHighlightGradient)"
            strokeWidth="1.2"
            strokeOpacity="1"
            filter="url(#goldGlow)"
            className="tonearm-gold-accent"
          />

          {/* Enhanced counterweight at the back of the arm */}
          <g className="tonearm-counterweight">
            {/* Main counterweight body */}
            <circle
              cx="60"
              cy="10"
              r="12"
              fill="url(#counterweightGradient)"
              filter="url(#enhancedShadow)"
            />

            {/* Counterweight gold accent ring */}
            <circle
              cx="60"
              cy="10"
              r="12"
              fill="none"
              stroke="url(#goldAccentGradient)"
              strokeWidth="0.8"
              strokeOpacity="0.8"
              className="tonearm-gold-accent"
            />

            {/* Counterweight inner detail */}
            <circle
              cx="60"
              cy="10"
              r="8"
              fill="none"
              stroke="url(#tonearmMetalGradient)"
              strokeWidth="1.5"
            />

            {/* Counterweight adjustment detail */}
            <rect
              x="59"
              y="4"
              width="2"
              height="12"
              fill="url(#tonearmMetalGradient)"
              filter="url(#enhancedShadow)"
            />
          </g>

          {/* Main tonearm - straight arm with enhanced shadow */}
          <rect
            x="58"
            y="30"
            width="4"
            height="350"
            rx="2"
            fill="url(#tonearmMetalGradient)"
            filter="url(#enhancedShadow)"
            className="tonearm-main-arm"
          />

          {/* Gold accent line along tonearm - enhanced */}
          <rect
            x="61.5"
            y="30"
            width="0.8"
            height="350"
            fill="url(#goldAccentGradient)"
            opacity="0.8"
            filter="url(#goldGlow)"
            className="tonearm-gold-accent"
          />

          {/* Tonearm highlight - enhanced */}
          <rect
            x="58"
            y="30"
            width="2"
            height="350"
            rx="1"
            fill="url(#tonearmHighlightGradient)"
            fillOpacity="0.4"
            className="tonearm-highlight"
          />

          {/* Enhanced gold accent dots along the arm */}
          <circle cx="60" cy="80" r="1.2" fill="url(#goldHighlightGradient)" opacity="0.8" filter="url(#goldGlow)" className="tonearm-gold-accent" />
          <circle cx="60" cy="160" r="1.2" fill="url(#goldHighlightGradient)" opacity="0.8" filter="url(#goldGlow)" className="tonearm-gold-accent" />
          <circle cx="60" cy="240" r="1.2" fill="url(#goldHighlightGradient)" opacity="0.8" filter="url(#goldGlow)" className="tonearm-gold-accent" />
          <circle cx="60" cy="320" r="1.2" fill="url(#goldHighlightGradient)" opacity="0.8" filter="url(#goldGlow)" className="tonearm-gold-accent" />

          {/* Headshell - the part that holds the needle with curve */}
          <g transform="translate(60, 380)" className="tonearm-headshell">
            {/* Curved connector to headshell with enhanced shadow */}
            <path
              d="M0 0 C0 10, -5 15, -15 15"
              stroke="url(#tonearmMetalGradient)"
              strokeWidth="3"
              strokeLinecap="round"
              fill="none"
              filter="url(#enhancedShadow)"
              className="tonearm-connector"
            />

            {/* Gold accent on curved connector - enhanced */}
            <path
              d="M0 0 C0 10, -5 15, -15 15"
              stroke="url(#goldAccentGradient)"
              strokeWidth="0.8"
              strokeLinecap="round"
              fill="none"
              strokeOpacity="0.8"
              filter="url(#goldGlow)"
              className="tonearm-gold-accent"
            />

            {/* Headshell body */}
            <g transform="translate(-15, 15)" className="tonearm-cartridge-assembly">
              {/* Headshell body with enhanced shadow */}
              <rect
                x="-25"
                y="-5"
                width="25"
                height="10"
                rx="2"
                fill="url(#tonearmMetalGradient)"
                filter="url(#enhancedShadow)"
                className="tonearm-headshell-body"
              />

              {/* Enhanced headshell gold accent */}
              <rect
                x="-25"
                y="-5"
                width="25"
                height="10"
                rx="2"
                fill="none"
                stroke="url(#goldAccentGradient)"
                strokeWidth="1"
                strokeOpacity="0.9"
                filter="url(#goldGlow)"
                className="tonearm-gold-accent"
              />

              {/* Cartridge body with enhanced detail */}
              <rect
                x="-25"
                y="-4"
                width="12"
                height="8"
                rx="1"
                fill="url(#counterweightGradient)"
                filter="url(#enhancedShadow)"
                className="tonearm-cartridge"
              />

              {/* Cartridge gold accent - enhanced */}
              <rect
                x="-25"
                y="-4"
                width="12"
                height="8"
                rx="1"
                fill="none"
                stroke="url(#goldAccentGradient)"
                strokeWidth="0.5"
                strokeOpacity="0.8"
                filter="url(#goldGlow)"
                className="tonearm-gold-accent"
              />

              {/* Enhanced stylus cantilever */}
              <path
                d="M-25 0 L-32 0"
                stroke="url(#tonearmMetalGradient)"
                strokeWidth="1.2"
                strokeLinecap="round"
                className="tonearm-cantilever"
              />

              {/* Stylus cantilever highlight */}
              <path
                d="M-25 -0.5 L-30 -0.5"
                stroke="#FFFFFF"
                strokeWidth="0.3"
                strokeLinecap="round"
                strokeOpacity="0.5"
                className="tonearm-highlight"
              />

              {/* Premium diamond-shaped needle tip with shine effect */}
              <g className="tonearm-needle-tip">
                {/* Diamond base shape */}
                <path
                  d="M-32 -1.5 L-34 0 L-32 1.5 L-30 0 Z"
                  fill="url(#diamondGradient)"
                  filter="url(#diamondShine)"
                  className="tonearm-diamond"
                />

                {/* Diamond gold accent outline */}
                <path
                  d="M-32 -1.5 L-34 0 L-32 1.5 L-30 0 Z"
                  fill="none"
                  stroke="url(#goldAccentGradient)"
                  strokeWidth="0.3"
                  strokeOpacity="0.9"
                  className="tonearm-gold-accent"
                />

                {/* Diamond highlight */}
                <path
                  d="M-32 -1 L-31 0 L-32 0.5"
                  stroke="#FFFFFF"
                  strokeWidth="0.3"
                  strokeLinecap="round"
                  strokeOpacity="0.8"
                  className="tonearm-highlight"
                />
              </g>
            </g>
          </g>

          {/* Enhanced anti-skate weight mechanism */}
          <g className="tonearm-anti-skate">
            {/* Anti-skate weight base */}
            <rect
              x="70"
              y="30"
              width="10"
              height="5"
              fill="url(#counterweightGradient)"
              filter="url(#enhancedShadow)"
            />

            {/* Anti-skate gold accent */}
            <rect
              x="70"
              y="30"
              width="10"
              height="5"
              fill="none"
              stroke="url(#goldAccentGradient)"
              strokeWidth="0.5"
              strokeOpacity="0.8"
              className="tonearm-gold-accent"
            />
          </g>
        </g>
      </svg>
    </div>
  );
};

export default TonearmComponent;
