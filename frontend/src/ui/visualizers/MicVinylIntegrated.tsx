/**
 * MicVinylIntegrated: Main visual interface for the recording system
 *
 * PRIMARY RESPONSIBILITIES:
 * - Providing the primary user interface for the recording experience
 * - Handling microphone button clicks to start/stop recording
 * - Handling vinyl record clicks to open beat selection
 * - Displaying visual feedback for recording state (idle, recording, error)
 * - Visualizing audio input level during recording
 *
 * INTEGRATION POINTS:
 * - Receives recordingState from RecordingSystem
 * - Calls onMicClick callback when microphone is clicked
 * - Calls onBeatSelect callback when a beat is selected
 * - Integrates with TonearmComponent for visual feedback
 * - Uses useBeatStore for beat information
 *
 * DOES NOT HANDLE:
 * - Actual audio recording (handled by FreestyleRecorder)
 * - State management (handled by RecordingSystem)
 * - Backend API interactions (handled by RecordingSystem)
 *
 * VISUAL DESIGN:
 * - Deep black vinyl record (#050505) with subtle grooves
 * - Champagne gold rings (#E6C770) with 0.7 opacity for all circular elements
 * - Platinum/silver microphone with gold accents
 * - Consistent 1px stroke width for all gold rings
 * - Subtle drop shadows for depth without flickering
 */
/**
 * MicVinylIntegrated Component
 *
 * A UI component that displays a vinyl record with an integrated microphone.
 * This component is responsible for:
 * - Rendering the vinyl record visualization
 * - Rendering the microphone visualization
 * - Handling user interactions (clicks)
 * - Displaying recording state visually
 * - Showing beat selection UI
 *
 * It does NOT handle audio recording or playback directly - those responsibilities
 * are delegated to parent components through callbacks.
 */

import React, { useEffect, useRef } from "react";
import { useBeatStore } from '../../stores/beatStore';
import TonearmComponent from './TonearmComponent';
import './MicVinylIntegrated.css';

export interface MicVinylIntegratedProps {
  /** Current state of the recording process */
  isRecording: boolean;

  /** Whether the component is initializing (e.g., requesting mic permissions) */
  isInitializing?: boolean;

  /** Whether the component is in the process of stopping recording */
  isStopping?: boolean;

  /** Whether the component has an error */
  hasError?: boolean;

  /** Error message to display */
  errorMessage?: string;

  /** Whether audio is currently playing */
  isPlaying?: boolean;

  /** Whether the component is disabled */
  disabled?: boolean;

  /** Title of the current beat */
  beatTitle?: string;

  /** Function called when the microphone is clicked (to start/stop recording) */
  onClick: () => void;

  /** Function called when the preview button is clicked */
  onPreviewClick?: () => void;

  /** No longer used - kept for backward compatibility */
  onBeatSelect?: (beat: any) => void;

  /** Audio input level (0-1) for visualizations - optional */
  level?: number;
}

export const MicVinylIntegrated: React.FC<MicVinylIntegratedProps> = ({
  isRecording,
  isInitializing = false,
  isStopping = false,
  hasError = false,
  errorMessage = '',
  isPlaying = false,
  disabled = false,
  // beatTitle = 'No Beat Selected', // Not currently used but kept for future use
  onClick,
  // onPreviewClick, // Not currently used but kept for future use
  // onBeatSelect no longer used
  level = 0
}) => {
  // Map component props to recording state for CSS classes
  const recordingState = isRecording ? "recording"
                      : isInitializing ? "initializing"
                      : isStopping ? "stopping"
                      : hasError ? "error"
                      : "idle";

  // Get current beat information
  const currentBeat = useBeatStore(state => state.currentBeat);

  // Container ref for positioning
  const containerRef = useRef<HTMLDivElement>(null);

  // Ref for microphone body
  const micBodyRef = useRef<SVGPathElement>(null);

  // No animation sequence needed - everything is visible immediately

  // Extract beat metadata - only using genre for color
  const beatGenre = currentBeat?.genre || (currentBeat?.attributes?.genre as string);

  // Get a color based on the beat genre
  const getGenreColor = (genre?: string) => {
    if (!genre) return "#8A8AFF";

    const genreColors: {[key: string]: string} = {
      "Hip Hop": "#8A8AFF",
      "Trap": "#a259ff",
      "R&B": "#ff5f7e",
      "Boom Bap": "#ff7a5f",
      "Lo-Fi": "#7a6fff",
      "Drill": "#b367ff",
    };

    return genreColors[genre] || "#8A8AFF";
  };

  // Base color for the vinyl and mic
  const baseColor = getGenreColor(beatGenre);

  // Determine color based on level
  const getLevelColor = () => {
    let color = baseColor;
    if (level > 0.7) color = "#ff3a5e";
    else if (level > 0.5) color = "#ff5f7e";
    else if (level > 0.3) color = "#b367ff";

    // Update CSS variable for use in animations
    if (typeof document !== 'undefined') {
      document.documentElement.style.setProperty('--level-color', color);
    }

    return color;
  };

  // Removed animation for mic and vinyl based on audio level

  // Vinyl is no longer clickable - removed dropdown functionality

  // Keep vinyl opacity consistent regardless of state
  const vinylOpacity = 0.8;

  // Note: Animation has been removed for a cleaner design
  // We'll implement a new animation system later

  // Handle recording state changes for transition effects
  useEffect(() => {
    console.log("MicVinylIntegrated: Recording state changed to:", recordingState);

    // Update the level color CSS variable when the level changes
    document.documentElement.style.setProperty('--level-color', getLevelColor());

    // Apply the appropriate class to the microphone button based on recording state
    const micButton = document.querySelector('.mic-button');
    if (micButton) {
      console.log("Updating microphone button classes for state:", recordingState);

      // Remove all state classes first
      micButton.classList.remove('mic-button-recording');
      micButton.classList.remove('mic-button-idle');
      micButton.classList.remove('mic-button-initializing');
      micButton.classList.remove('mic-button-stopping');
      micButton.classList.remove('mic-button-stopped');
      micButton.classList.remove('mic-button-error');
      micButton.classList.remove('mic-button-paused');

      // Add the appropriate class based on recording state
      if (recordingState === 'recording') {
        micButton.classList.add('mic-button-recording');
        console.log("Added mic-button-recording class");
      } else if (recordingState === 'initializing') {
        micButton.classList.add('mic-button-initializing');
        console.log("Added mic-button-initializing class");
      } else if (recordingState === 'stopping') {
        micButton.classList.add('mic-button-stopping');
        console.log("Added mic-button-stopping class");
      } else if (recordingState === 'error') {
        micButton.classList.add('mic-button-error');
        console.log("Added mic-button-error class");
      } else {
        // Default to idle for all other states
        micButton.classList.add('mic-button-idle');
        console.log("Added mic-button-idle class (default)");
      }
    } else {
      console.warn("Could not find microphone button element to update classes");
    }

    // Log the current audio level for debugging
    console.log("Current audio level:", level, "Color:", getLevelColor());
  }, [recordingState, getLevelColor, level]);

  return (
    <div
      className="mic-vinyl-integrated"
      ref={containerRef}
      role="region"
      aria-label="Freestyle recording interface"
    >
      {/* Main container with relative positioning */}
      <div className="mic-vinyl-container">
        {/* Tonearm component positioned at the right of the vinyl */}
        <TonearmComponent
          disabled={disabled}
          isPlaying={isPlaying || recordingState === 'recording'}
        />
        {(() => {
          console.log("TonearmComponent rendered with isPlaying:", isPlaying || recordingState === 'recording');
          return null;
        })()}

        {/* SVG for integrated mic and vinyl */}
        <svg
          width="460"
          height="460"
          viewBox="0 0 280 280"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          style={{
            filter: "drop-shadow(0 12px 25px rgba(0, 0, 0, 0.35))",
            borderRadius: "50%",
            background: "transparent"
          }}
        >
          {/* Enhanced Vinyl Record Design */}

          {/* Base layer - dark background with subtle radial gradient */}
          <circle
            cx="140"
            cy="140"
            r="140"
            fill="url(#backgroundGradient)"
            className="vinyl-background"
          />

          {/* Vinyl record base - deep black with high-gloss finish */}
          <circle
            cx="140"
            cy="140"
            r="140"
            fill="#050505"
            className="vinyl-clickable"
            style={{
              opacity: vinylOpacity
            }}
          />

          {/* Vinyl record visualization - static version (no animation) */}
          <g className="vinyl-static">
            {/* Premium vinyl disc with deep black high-gloss finish */}
            <circle
              cx="140"
              cy="140"
              r="140"
              fill="#050505"
              opacity={vinylOpacity}
              style={{
                filter: "drop-shadow(0 0 10px rgba(0, 0, 0, 0.8))"
              }}
              className="vinyl-base"
            />

            {/* Very subtle radial gradient overlay for authentic vinyl texture */}
            <circle
              cx="140"
              cy="140"
              r="140"
              fill="url(#vinylGrooveGradient)"
              opacity="0.08"
              className="vinyl-texture-overlay"
            />

            {/* Realistic vinyl grooves - subtle and authentic */}
            {Array.from({ length: 80 }).map((_, i) => {
              // Calculate radius with graduated spacing (denser toward center)
              const baseSpacing = 0.9; // More realistic groove spacing
              const densityFactor = 1 + (i * 0.008); // Gradual spacing increase
              const spacing = baseSpacing * densityFactor;
              const radius = 135 - (i * spacing);

              // Skip circles that would overlap with the inner ring
              if (radius < 54) return null; // Updated to match the new smaller label size

              // Create subtle visual interest with varying opacities and stroke widths
              const isMajorAccent = i % 10 === 0; // Every 10th groove is a major accent
              const isAccent = i % 5 === 0; // Every 5th groove is an accent

              // More subtle opacity values for realistic appearance
              const opacity = isMajorAccent ? 0.35 : isAccent ? 0.25 : 0.15;

              // Thinner strokes for realistic appearance
              const strokeWidth = isMajorAccent ? 0.7 : isAccent ? 0.5 : 0.3;

              // Darker colors for realistic groove appearance
              const strokeColor = isMajorAccent
                ? "rgba(20, 20, 20, 0.9)"
                : isAccent
                  ? "rgba(15, 15, 15, 0.8)"
                  : "rgba(10, 10, 10, 0.7)";

              return (
                <circle
                  key={`groove-${i}`}
                  cx="140"
                  cy="140"
                  r={radius}
                  fill="none"
                  stroke={strokeColor}
                  strokeWidth={strokeWidth}
                  opacity={opacity}
                  className="vinyl-groove"
                />
              );
            })}

            {/* Subtle arc light reflection - realistic vinyl sheen */}
            <path
              d="M70,70 Q140,90 210,70"
              stroke="rgba(255, 255, 255, 0.08)"
              strokeWidth="25"
              fill="none"
              style={{
                filter: "blur(20px)",
                mixBlendMode: "soft-light"
              }}
            />

            {/* Very subtle horizontal light reflection - authentic vinyl look */}
            <path
              d="M60,140 L220,140"
              stroke="rgba(255, 255, 255, 0.05)"
              strokeWidth="100"
              fill="none"
              style={{
                filter: "blur(30px)",
                mixBlendMode: "soft-light"
              }}
            />

            {/* No dynamic gold highlight - removed for cleaner design */}

            {/* Navy blue label */}
            <circle
              cx="140"
              cy="140"
              r="54" // Decreased by 10% from 60 to make the vinyl part take up more
              fill="#0A1A2F"
              className="vinyl-label"
              style={{
                opacity: 1, /* Always fully visible */
                transition: 'none'
              }}
            />

            {/* Inner label ring - matching the outer rim */}
            <circle
              cx="140"
              cy="140"
              r="54" // Updated to match the navy blue label
              fill="none"
              stroke="#E6C770"
              strokeWidth="1.5" // Increased for better visibility
              strokeOpacity="0.7"
              className="vinyl-ring animated-ring"
              style={{
                opacity: 0.9, /* Always fully visible with increased opacity */
                transition: 'none',
                '--ring-circumference': '339px', // Updated: 2 * PI * 54
                filter: 'drop-shadow(0 0 2px rgba(230, 199, 112, 0.8))' // Subtle glow for emphasis
              } as React.CSSProperties}
            />

            {/* Small center spindle hole - hidden during initial load */}
            <circle
              cx="140"
              cy="140"
              r="4"
              fill="#050505" // Changed to match vinyl background instead of gold
              stroke="none"
              style={{ opacity: 0.8 }}
              className="spindle-hole"
            />
          </g>

          {/* Single gold rim highlight that doesn't rotate - subtle outline */}
          <circle
            cx="140"
            cy="140"
            r="140"
            fill="none"
            stroke="#E6C770"
            strokeWidth="1.5" // Increased for better visibility
            strokeOpacity="0.7"
            className="vinyl-ring animated-ring"
            style={{
              opacity: 0.9, /* Always fully visible with increased opacity */
              transition: 'none',
              '--ring-circumference': '880px', // Set the CSS variable for the outer ring circumference
              filter: 'drop-shadow(0 0 2px rgba(230, 199, 112, 0.8))' // Subtle glow
            } as React.CSSProperties}
          />

          {/* Premium gradient definitions */}
          <defs>
            {/* Background gradient - deep midnight blue - matching page background */}
            <radialGradient id="backgroundGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
              <stop offset="0%" stopColor="#0F1A30" />
              <stop offset="100%" stopColor="#070D19" />
            </radialGradient>

            {/* Microphone background gradient - matching darkest part of page gradient */}
            <linearGradient id="micBackgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#070D19" />
              <stop offset="100%" stopColor="#050A14" />
            </linearGradient>

            {/* Lighter metallic gradient for microphone - directional lighting from upper right */}
            <linearGradient id="platinumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#C5C9CE" />
              <stop offset="50%" stopColor="#B5B9BE" />
              <stop offset="100%" stopColor="#A5A9AE" />
            </linearGradient>

            {/* Directional highlight gradient for microphone - upper right light source */}
            <linearGradient id="micHighlightGradient" x1="100%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.8)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0)" />
            </linearGradient>

            {/* Shadow gradient for microphone - bottom left */}
            <linearGradient id="micShadowGradient" x1="0%" y1="100%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="rgba(0,0,0,0.3)" />
              <stop offset="100%" stopColor="rgba(0,0,0,0)" />
            </linearGradient>

            {/* Dark charcoal gradient for microphone handle */}
            <linearGradient id="charcoalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#2C2C2C" />
              <stop offset="50%" stopColor="#3A3A3A" />
              <stop offset="100%" stopColor="#2C2C2C" />
            </linearGradient>

            {/* Microphone stand gradient - slightly darker/cooler platinum finish */}
            <linearGradient id="micStandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#B5B9BE" />
              <stop offset="50%" stopColor="#969BA1" />
              <stop offset="100%" stopColor="#7D8186" />
            </linearGradient>

            {/* Microphone stand highlight gradient */}
            <linearGradient id="micStandHighlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#C5C9CE" />
              <stop offset="100%" stopColor="#A9ADB2" />
            </linearGradient>

            {/* Microphone stand shadow gradient */}
            <linearGradient id="micStandShadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#7D8186" />
              <stop offset="100%" stopColor="#5D6166" />
            </linearGradient>

            {/* Edge highlight gradient for 3D effect */}
            <linearGradient id="edgeHighlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.7)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0.2)" />
            </linearGradient>

            {/* Enhanced gold accent gradient for stand with directional lighting */}
            <linearGradient id="goldAccentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#F6D780" />
              <stop offset="50%" stopColor="#E6C770" />
              <stop offset="100%" stopColor="#D4AF37" />
            </linearGradient>

            {/* Gold highlight gradient for catching light */}
            <linearGradient id="goldHighlightGradient" x1="100%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.9)" />
              <stop offset="50%" stopColor="rgba(255,240,180,0.5)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0)" />
            </linearGradient>

            {/* Darker metallic highlight gradient for microphone */}
            <linearGradient id="silverHighlightGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="rgba(169, 173, 178, 0.1)" />
              <stop offset="50%" stopColor="rgba(169, 173, 178, 0.3)" />
              <stop offset="100%" stopColor="rgba(169, 173, 178, 0.1)" />
            </linearGradient>

            {/* Reflection filter */}
            <filter id="reflection-blur" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
            </filter>

            {/* Removed gold glow filter to prevent initial loading glow */}

            {/* Subtle radial gradient for authentic vinyl texture */}
            <radialGradient id="vinylGrooveGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
              <stop offset="0%" stopColor="#222222" stopOpacity="0.05" />
              <stop offset="40%" stopColor="#181818" stopOpacity="0.08" />
              <stop offset="70%" stopColor="#101010" stopOpacity="0.1" />
              <stop offset="100%" stopColor="#080808" stopOpacity="0.15" />
            </radialGradient>

            {/* Note: Removed the groove-enhance filter for a more subtle, realistic look */}
          </defs>
        </svg>

        {/* Enhanced Microphone Button - integrated with the center of the vinyl */}
        <button
          onClick={(e) => {
            e.stopPropagation(); // Prevent vinyl click handler from firing
            e.preventDefault(); // Prevent any default button behavior

            console.log("Microphone button clicked, current state:", recordingState, "disabled:", disabled);

            // Only proceed if not disabled
            if (!disabled) {
              try {
                // Call the onClick callback provided by RecordingSystem
                console.log("Calling onClick callback");
                onClick();
              } catch (error) {
                console.error("Error in microphone click handler:", error);
              }
            } else {
              console.log("Microphone button is disabled, ignoring click");
            }
          }}
          disabled={disabled}
          className={`mic-button mic-button-focus mic-button-${recordingState} ${disabled ? "disabled" : ""}`}
          aria-label={recordingState === "recording" ? "Stop recording" : "Start recording"}
          style={{
            width: 138, // Reduced by ~18% from 168px
            height: 138, // Reduced by ~18% from 168px
            borderRadius: "50%",
            background: "linear-gradient(145deg, #070D19, #050A14)", // Use the darkest part of the page gradient
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "absolute",
            top: "50%",
            left: "50%",
            cursor: disabled ? "not-allowed" : "pointer",
            outline: "none",
            padding: 0,
            zIndex: 10,
            willChange: "transform, opacity, border-color, box-shadow", // Optimize all animated properties
            boxShadow: "0 0 18px rgba(0, 0, 0, 0.8)", /* Always visible */
            border: "1px solid rgba(230, 199, 112, 0.7)", // Match the CSS border
            transition: "transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease",
            transform: "translate(-50%, -50%)", // Simple transform
            opacity: 1 /* Always fully visible */
          }}
        >
          {/* Professional Studio Condenser Microphone - Platinum/Silver Finish */}
          <svg
            width="138" // Reduced by ~18% from 168px
            height="138" // Reduced by ~18% from 168px
            viewBox="0 0 160 160" // Keeping the same viewBox to maintain proportions
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mic-svg"
            style={{
              opacity: 1, /* Always fully visible */
              transition: 'none'
            }}
          >



            {/* Professional Studio Condenser Microphone with platinum/silver finish */}
            <g
              className="mic-body"
              style={{
                transform: "scale(1.2)", // Fixed scale, no animation
                transformOrigin: "80px 80px"
                // Removed transition for animation
              }}
            >
              {/* Microphone Stand - V-shaped yoke support structure - positioned behind mic */}
              <g className="mic-stand">
                {/* 3D Vertical stem with width - with flat bottom, slightly shorter to avoid overlapping the gold ring */}
                <path
                  d="M77 120
                     L77 145.2
                     L83 145.2
                     L83 120
                     Q83 117, 80 117
                     Q77 117, 77 120"
                  fill="url(#micStandGradient)"
                  stroke="none"
                />

                {/* Left edge highlight on vertical stem - adjusted to match new height */}
                <path
                  d="M77.5 123 L77.5 144.1"
                  stroke="url(#edgeHighlightGradient)"
                  strokeWidth="0.8"
                  fill="none"
                  strokeLinecap="round"
                  opacity="0.5"
                />

                {/* Highlight on vertical stem - adjusted to match new shorter height */}
                <path
                  d="M78 120 L78 142"
                  stroke="url(#micStandHighlightGradient)"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  opacity="0.7"
                />

                {/* Gold accent line on vertical stem - adjusted to match new shorter height */}
                <path
                  d="M80 120 L80 142"
                  stroke="url(#goldAccentGradient)"
                  strokeWidth="0.8"
                  fill="none"
                  strokeLinecap="round"
                  opacity="0.8"
                />

                {/* Connection joint between stem and base */}
                <circle
                  cx="80"
                  cy="190"
                  r="4"
                  fill="url(#micStandGradient)"
                  stroke="none"
                  filter="drop-shadow(0px 1px 1px rgba(0,0,0,0.3))"
                />

                {/* Gold accent on connection joint */}
                <circle
                  cx="80"
                  cy="190"
                  r="2.5"
                  fill="none"
                  stroke="url(#goldAccentGradient)"
                  strokeWidth="0.8"
                  opacity="0.9"
                />

                {/* 3D Circular weighted base with shadow effect - larger for stability */}
                <ellipse
                  cx="80"
                  cy="200"
                  rx="15"
                  ry="5"
                  fill="rgba(0,0,0,0.3)"
                  opacity="0.5"
                  style={{
                    filter: "blur(3px)"
                  }}
                />

                {/* 3D Circular weighted base - main body - larger for stability */}
                <circle
                  cx="80"
                  cy="195"
                  r="12"
                  fill="url(#micStandGradient)"
                  stroke="none"
                  filter="drop-shadow(0px 2px 2px rgba(0,0,0,0.3))"
                />

                {/* Base top edge highlight */}
                <path
                  d="M72 188 A 12 6 0 0 1 88 188"
                  stroke="url(#edgeHighlightGradient)"
                  strokeWidth="1"
                  fill="none"
                  opacity="0.6"
                />

                {/* Base top highlight */}
                <ellipse
                  cx="80"
                  cy="193"
                  rx="10"
                  ry="4"
                  fill="url(#micStandHighlightGradient)"
                  opacity="0.4"
                />

                {/* Gold accent ring on base */}
                <circle
                  cx="80"
                  cy="195"
                  r="9"
                  fill="none"
                  stroke="url(#goldAccentGradient)"
                  strokeWidth="1"
                  opacity="0.9"
                />

                {/* Center detail on base */}
                <circle
                  cx="80"
                  cy="195"
                  r="3"
                  fill="url(#micStandShadowGradient)"
                  stroke="url(#goldAccentGradient)"
                  strokeWidth="0.5"
                />

                {/* Additional gold accent ring on base for premium look */}
                <circle
                  cx="80"
                  cy="195"
                  r="11"
                  fill="none"
                  stroke="url(#goldAccentGradient)"
                  strokeWidth="0.3"
                  opacity="0.7"
                />
              </g>

              {/* 3D Y-shaped stand - left arm with shadow and highlight (emerging from behind mic) - adjusted to stop at the edge of the circle */}
              <path
                d="M62 70
                   C62 88, 68 102, 74 110
                   C76 112, 78 114, 80 115"
                fill="url(#micStandGradient)"
                stroke="none"
                strokeLinejoin="round"
                strokeWidth="1.5"
                filter="drop-shadow(0.5px 0.5px 0.5px rgba(0,0,0,0.15))"
                style={{ zIndex: -1 }}
              />

              {/* Left arm top edge highlight - positioned behind the circle */}
              <path
                d="M62 70
                   C62 85, 66 98, 72 106"
                stroke="url(#edgeHighlightGradient)"
                strokeWidth="0.5"
                fill="none"
                strokeLinecap="round"
                opacity="0.4"
                style={{ zIndex: -1 }}
              />

              {/* Left arm center highlight - positioned behind the circle */}
              <path
                d="M63 75
                   C63 88, 68 100, 74 108"
                stroke="url(#micStandHighlightGradient)"
                strokeWidth="0.8"
                fill="none"
                strokeLinecap="round"
                opacity="0.5"
                style={{ zIndex: -1 }}
              />

              {/* Right arm of Y-shaped stand with shadow and highlight (emerging from behind mic) - adjusted to stop at the edge of the circle */}
              <path
                d="M98 70
                   C98 88, 92 102, 86 110
                   C84 112, 82 114, 80 115"
                fill="url(#micStandGradient)"
                stroke="none"
                strokeLinejoin="round"
                strokeWidth="1.5"
                filter="drop-shadow(0.5px 0.5px 0.5px rgba(0,0,0,0.15))"
                style={{ zIndex: -1 }}
              />

              {/* Right arm top edge highlight - positioned behind the circle */}
              <path
                d="M98 70
                   C98 85, 94 98, 88 106"
                stroke="url(#edgeHighlightGradient)"
                strokeWidth="0.5"
                fill="none"
                strokeLinecap="round"
                opacity="0.4"
                style={{ zIndex: -1 }}
              />

              {/* Right arm center highlight - positioned behind the circle */}
              <path
                d="M97 75
                   C97 88, 92 100, 86 108"
                stroke="url(#micStandHighlightGradient)"
                strokeWidth="0.8"
                fill="none"
                strokeLinecap="round"
                opacity="0.5"
                style={{ zIndex: -1 }}
              />

              {/* Y junction connection - clean and aligned - positioned behind the circle */}
              <circle
                cx="80"
                cy="115"
                r="3.5"
                fill="url(#micStandGradient)"
                stroke="none"
                filter="none"
                style={{ zIndex: -1 }}
              />

              {/* Gold accent at Y junction - perfectly aligned - positioned behind the circle */}
              <circle
                cx="80"
                cy="115"
                r="2.2"
                fill="none"
                stroke="url(#goldAccentGradient)"
                strokeWidth="0.8"
                opacity="0.9"
                style={{ filter: "none", zIndex: -1 }}
              />

              {/* Enhanced shadow for left connection - perfectly aligned */}
              <circle
                cx="62"
                cy="70"
                r="3"
                fill="rgba(0,0,0,0.2)"
                style={{ filter: "none" }}
              />

              {/* Connection hardware at microphone attachment points - left side - clean and aligned */}
              <circle
                cx="62"
                cy="70"
                r="3"
                fill="url(#micStandGradient)"
                stroke="none"
                filter="none"
              />

              {/* Gold accent on left connection - perfectly aligned */}
              <circle
                cx="62"
                cy="70"
                r="2"
                fill="none"
                stroke="url(#goldAccentGradient)"
                strokeWidth="0.8"
                opacity="0.9"
                style={{ filter: "none" }}
              />

              {/* Enhanced shadow for right connection - more subtle and perfectly aligned */}
              <circle
                cx="98"
                cy="70"
                r="3"
                fill="rgba(0,0,0,0.2)"
                style={{ filter: "none" }}
              />

              {/* Connection hardware at microphone attachment points - right side (appears to emerge from behind) - more subtle */}
              <circle
                cx="98"
                cy="70"
                r="3"
                fill="url(#micStandGradient)"
                stroke="none"
                filter="none"
              />

              {/* Gold accent on right connection - perfectly aligned */}
              <circle
                cx="98"
                cy="70"
                r="2"
                fill="none"
                stroke="url(#goldAccentGradient)"
                strokeWidth="0.8"
                opacity="0.9"
                style={{ filter: "none" }}
              />

              {/* Professional platinum/silver microphone body with classic studio shape - positioned slightly lower */}
              <path
                ref={micBodyRef}
                d="M60 50
                   C60 35, 68 30, 80 30
                   C92 30, 100 35, 100 50
                   L100 85
                   C100 100, 92 105, 80 105
                   C68 105, 60 100, 60 85
                   Z"
                fill="url(#platinumGradient)"
                stroke="none"
                className="mic-body"
              />

              {/* Directional highlight from upper right - further cleaned up */}
              <path
                d="M85 32
                   C92 35, 97 40, 98 50
                   L98 70"
                fill="none"
                stroke="url(#micHighlightGradient)"
                strokeWidth="1.5"
                opacity="0.25"
                strokeLinecap="round"
                style={{ filter: "none" }}
              />

              {/* Bottom-left shadow for depth - cleaned up */}
              <path
                d="M61 70
                   L61 85
                   C61 99, 68 104, 80 104"
                fill="none"
                stroke="url(#micShadowGradient)"
                strokeWidth="2"
                opacity="0.25"
                strokeLinecap="round"
                strokeLinejoin="round"
                style={{ filter: "none" }}
              />

              {/* Subtle edge highlight on microphone body for 3D effect - top - further cleaned up */}
              <path
                d="M67 33
                   C72 32, 76 31, 80 31
                   C84 31, 88 32, 93 34"
                fill="none"
                stroke="url(#edgeHighlightGradient)"
                strokeWidth="0.6"
                opacity="0.3"
                strokeLinecap="round"
                style={{ filter: "none" }}
              />

              {/* Solid background for microphone face to prevent transparency */}
              <path
                d="M65 45
                   C65 45, 70 44, 80 44
                   C90 44, 95 45, 95 45
                   L95 90
                   C95 90, 90 91, 80 91
                   C70 91, 65 90, 65 90
                   Z"
                fill="url(#platinumGradient)"
              />

              {/* Subtle gradient overlay for microphone face */}
              <path
                d="M65 45
                   C65 45, 70 44, 80 44
                   C90 44, 95 45, 95 45
                   L95 90
                   C95 90, 90 91, 80 91
                   C70 91, 65 90, 65 90
                   Z"
                fill="url(#micHighlightGradient)"
                opacity="0.1"
              />

              {/* Platinum/silver grid pattern on mic head with enhanced 3D effect */}
              <g className="mic-grid">
                {Array.from({ length: 10 }).map((_, i) => (
                  <g key={`grille-group-${i}`}>
                    {/* Main grille line */}
                    <path
                      key={`grille-${i}`}
                      d={`M65 ${45 + i * 5}
                          C65 ${45 + i * 5}, 70 ${44 + i * 5}, 80 ${44 + i * 5}
                          C90 ${44 + i * 5}, 95 ${45 + i * 5}, 95 ${45 + i * 5}`}
                      stroke="#222222"
                      strokeWidth="1.5"
                      opacity={0.9 + (i % 2 === 0 ? 0.1 : 0)}
                    />

                    {/* Highlight line for 3D effect - more subtle */}
                    <path
                      key={`grille-highlight-${i}`}
                      d={`M65 ${44.5 + i * 5}
                          C65 ${44.5 + i * 5}, 70 ${43.5 + i * 5}, 80 ${43.5 + i * 5}
                          C90 ${43.5 + i * 5}, 95 ${44.5 + i * 5}, 95 ${44.5 + i * 5}`}
                      stroke="rgba(255,255,255,0.2)"
                      strokeWidth="0.5"
                      opacity={0.2 + (i % 2 === 0 ? 0.1 : 0)}
                    />
                  </g>
                ))}
              </g>

              {/* Silver highlight on side of mic - cleaned up */}
              <path
                d="M61 65
                   C61 55, 63 45, 65 40"
                stroke="url(#silverHighlightGradient)"
                strokeWidth="1"
                fill="none"
                opacity="0.7"
                style={{ filter: "none" }}
              />

              {/* Silver highlight on other side of mic - cleaned up */}
              <path
                d="M99 65
                   C97 55, 95 45, 93 40"
                stroke="url(#silverHighlightGradient)"
                strokeWidth="1"
                fill="none"
                opacity="0.7"
                style={{ filter: "none" }}
              />



            </g>

          </svg>


        </button>
      </div>

      {/* Beat selector dropdown removed - no longer needed */}

      {/* Beat Title Text removed to make room for future information panel */}

      {/* Microphone instruction text - always visible except during recording */}
      {recordingState !== "recording" && (
        <div className="instruction-text">
          Tap Mic to Record
        </div>
      )}

      {/* Error message */}
      {hasError && errorMessage && (
        <div
          style={{
            color: "#FFD700",
            fontSize: 14,
            fontWeight: 600,
            textAlign: "center",
            maxWidth: 250,
            marginTop: 10,
            textShadow: "0 0 5px rgba(255, 215, 0, 0.3)"
          }}
          className="error-message"
        >
          {errorMessage}
        </div>
      )}
    </div>
  );
};

export default MicVinylIntegrated;
