/**
 * MicVinylIntegrated Component Styles - Premium Hip-Hop Golden Era Theme
 *
 * Color Scheme:
 * - Deep black vinyl: #050505
 * - Champagne gold accents: #E6C770 (with 0.7 opacity for rings)
 * - Platinum/silver microphone: gradient from #E5E4E2 to #C0C0C0
 * - Dark charcoal mic stand: gradient from #2C2C2C to #3A3A3A
 * - Background gradient: #0F1A30 to #070D19
 *
 * Key Design Elements:
 * - Consistent gold rings with 1px stroke width and 0.7 opacity
 * - Subtle drop shadows (3px blur radius) for depth without flickering
 * - No animations on the rings to prevent visual bugs
 * - Unified styling through the .vinyl-ring class
 */

/* Main container - no background (background handled by page) */
.mic-vinyl-integrated {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0;
  width: 100%;
  padding: 20px 0 40px 0; /* Reduced top padding to move component up and reduce space */
  box-sizing: border-box;
  /* Remove overflow hidden to prevent container outline */
}

/* Remove noise texture from component - will be handled by page background */

/* Remove vignette effect from component - will be handled by page background */

/* Container for the vinyl and microphone */
.mic-vinyl-container {
  position: relative;
  width: 460px; /* Reduced by ~18% from 560px to accommodate instructional text */
  height: 460px; /* Reduced by ~18% from 560px to maintain aspect ratio */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  /* Enhanced drop shadow for better depth */
  filter: drop-shadow(0 15px 25px rgba(0, 0, 0, 0.35));
  z-index: 3; /* Ensure it's above the tonearm */
  /* Position relative for absolute positioning of tonearm */
  position: relative;
  overflow: visible; /* Allow tonearm to extend outside container */
}

/* Premium Vinyl Styling */
.vinyl-background {
  filter: blur(5px);
  opacity: 0.8;
}

/* Vinyl clickable area with premium styling */
.vinyl-clickable {
  transition: stroke 0.3s ease, filter 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  cursor: pointer;
  transform-origin: center;
  transform: scale(1);
  filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.7));
}

/* Golden rim highlight on hover with smoother transitions */
.vinyl-clickable:hover {
  transform: scale(1.02);
  filter: drop-shadow(0 12px 25px rgba(0, 0, 0, 0.8));
}

/* Static vinyl styling */
.vinyl-static {
  transform-origin: 140px 140px;
  /* No animations applied */
}

/* Realistic vinyl grooves styling */
.vinyl-groove {
  transition: opacity 0.3s, stroke-width 0.3s;
}

/* Removed vinyl-reflection classes - simplified design */

/* Premium vinyl label styling - deep navy blue instead of burgundy */
.vinyl-label {
  filter: drop-shadow(0 0 15px rgba(0, 0, 0, 0.7));
}

/* Unified styling for all gold rings (outer vinyl rim, inner label ring, mic button border) */
.vinyl-ring {
  stroke: #E6C770; /* Champagne gold color */
  stroke-width: 1px; /* Thin subtle stroke */
  stroke-opacity: 0.7; /* Reduced opacity for elegance */
  filter: none !important; /* Explicitly set to none to prevent initial loading glow */
  box-shadow: none !important; /* Explicitly set to none to prevent initial loading glow */
  z-index: 5; /* Consistent z-index to prevent layering issues */
  pointer-events: none; /* Allow clicks to pass through to elements below */
  /* No animations to prevent flickering */
  opacity: 1 !important; /* Force opacity to 1 to prevent flickering */
  animation: none !important; /* Disable animation to prevent flickering */
  transition: none !important; /* Disable transitions to prevent flickering */
}

/* Spindle hole styling */
.spindle-hole {
  opacity: 0.8 !important;
  fill: #050505 !important;
  animation: none !important;
  transition: none !important;
}

/* Removed traveling gold highlight */

/* Microphone Button Styling - Base styles */
.mic-button {
  background: linear-gradient(145deg, #070D19, #050A14) !important; /* Match darkest part of page gradient */
  border: 1px solid rgba(230, 199, 112, 0.9) !important; /* Increased opacity for better visibility */
  box-shadow: 0 0 5px rgba(230, 199, 112, 0.5) !important; /* Add subtle glow to make it stand out */
  opacity: 1 !important; /* Force opacity to 1 to prevent flickering */
  visibility: visible !important; /* Force visibility */
  display: block !important; /* Force display */
  /* Add smooth transitions for all hover effects */
  transition: transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* Hover effect with smoother transitions */
.mic-button:hover {
  transform: translate(-50%, -50%) scale(1.02) !important;
  border-color: rgba(230, 199, 112, 1) !important; /* Full opacity on hover */
  box-shadow: 0 0 8px rgba(230, 199, 112, 0.7) !important; /* Enhanced glow on hover */
}

/* Active state with smooth transition */
.mic-button:active {
  transform: translate(-50%, -50%) scale(1.005) !important;
  border-color: rgba(230, 199, 112, 0.95) !important;
  box-shadow: 0 0 6px rgba(230, 199, 112, 0.6) !important;
}

/* Disabled state */
.mic-button.disabled {
  cursor: not-allowed !important;
  opacity: 0.8 !important; /* Increased opacity so it's still clearly visible */
  border-color: rgba(230, 199, 112, 0.5) !important; /* Keep gold border but slightly dimmed */
  background: linear-gradient(145deg, #070D19, #050A14) !important; /* Keep the same background */
  box-shadow: 0 0 3px rgba(230, 199, 112, 0.3) !important; /* Reduced glow but still visible */
  /* Maintain smooth transitions even in disabled state */
  transition: transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease !important;
}

/* Ensure hover doesn't change the disabled state */
.mic-button.disabled:hover {
  transform: translate(-50%, -50%) !important; /* No scale change */
  border-color: rgba(230, 199, 112, 0.5) !important; /* Keep the same border */
  box-shadow: 0 0 3px rgba(230, 199, 112, 0.3) !important; /* Keep the same shadow */
}

/* Recording state - pulsing red border */
.mic-button-recording {
  border-color: rgba(255, 58, 94, 0.9) !important; /* Red border for recording state */
  box-shadow: 0 0 10px rgba(255, 58, 94, 0.7) !important; /* Red glow for recording state */
  animation: recordingPulse 2s infinite alternate ease-in-out !important;
}

/* Recording state hover */
.mic-button-recording:hover {
  border-color: rgba(255, 58, 94, 1) !important; /* Brighter red on hover */
  box-shadow: 0 0 12px rgba(255, 58, 94, 0.8) !important; /* Enhanced red glow on hover */
}

/* Initializing state - yellow pulsing border */
.mic-button-initializing {
  border-color: rgba(255, 215, 0, 0.9) !important; /* Gold border for initializing state */
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.7) !important; /* Gold glow for initializing state */
  animation: initializingPulse 1s infinite alternate ease-in-out !important;
}

/* Stopping state - blue pulsing border */
.mic-button-stopping {
  border-color: rgba(95, 111, 255, 0.9) !important; /* Blue border for stopping state */
  box-shadow: 0 0 8px rgba(95, 111, 255, 0.7) !important; /* Blue glow for stopping state */
  animation: stoppingPulse 0.5s infinite alternate ease-in-out !important;
}

/* Stopped state - green border */
.mic-button-stopped {
  border-color: rgba(46, 204, 113, 0.9) !important; /* Green border for stopped state */
  box-shadow: 0 0 8px rgba(46, 204, 113, 0.7) !important; /* Green glow for stopped state */
}

/* Error state - red border */
.mic-button-error {
  border-color: rgba(231, 76, 60, 0.9) !important; /* Red border for error state */
  box-shadow: 0 0 8px rgba(231, 76, 60, 0.7) !important; /* Red glow for error state */
  animation: errorPulse 0.5s infinite alternate ease-in-out !important;
}

/* Paused state - orange border */
.mic-button-paused {
  border-color: rgba(243, 156, 18, 0.9) !important; /* Orange border for paused state */
  box-shadow: 0 0 8px rgba(243, 156, 18, 0.7) !important; /* Orange glow for paused state */
}

/* Idle state - gold border (default) */
.mic-button-idle {
  border-color: rgba(230, 199, 112, 0.9) !important; /* Gold border for idle state */
  box-shadow: 0 0 5px rgba(230, 199, 112, 0.5) !important; /* Gold glow for idle state */
}

/* Recording pulse animation */
@keyframes recordingPulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 58, 94, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 58, 94, 0.8);
  }
}

/* Initializing pulse animation */
@keyframes initializingPulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
  }
  100% {
    box-shadow: 0 0 12px rgba(255, 215, 0, 0.8);
  }
}

/* Stopping pulse animation */
@keyframes stoppingPulse {
  0% {
    box-shadow: 0 0 5px rgba(95, 111, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 12px rgba(95, 111, 255, 0.8);
  }
}

/* Error pulse animation */
@keyframes errorPulse {
  0% {
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
  }
  100% {
    box-shadow: 0 0 12px rgba(231, 76, 60, 0.8);
  }
}

/* Premium Microphone SVG elements */
.mic-svg {
  position: relative;
  z-index: 5;
}

/* Removed mic-glow class - not used in current design */

/* Platinum/Silver microphone elements */
.mic-body {
  /* Removed transitions for animation */
  fill: url(#platinumGradient) !important;
  stroke: #C0C0C0 !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

/* Microphone handle styling - dark charcoal with metallic sheen */
.mic-handle {
  fill: #2C2C2C !important;
  stroke: #2C2C2C !important;
}

/* Note: Simplified microphone stand design with direct support arms */

/* Microphone grid pattern - platinum with gold accents */
.mic-grid {
  stroke: #E5E4E2 !important;
  stroke-width: 1.2px;
  opacity: 0.9;
}

/* Removed recording animation for the microphone */

/* Note: Removed mic-mount animation for simplified design */

/* Removed recording-indicator classes - not used in current design */

/* Premium Beat dropdown styling */
.beat-dropdown {
  border: 1px solid #BF9B30 !important;
  background: rgba(15, 26, 48, 0.95) !important;
}

/* Beat dropdown hover effect */
.dropdown-hover:hover {
  background-color: rgba(191, 155, 48, 0.15) !important;
  border: 1px solid rgba(191, 155, 48, 0.3) !important;
}

/* Beat title text styling removed - will be part of future information panel */

/* Instruction text styling - positioned closer to and centered with the vinyl */
.instruction-text {
  color: rgba(230, 230, 250, 1); /* Fully opaque for better visibility */
  font-size: 14px; /* Slightly smaller */
  font-weight: 600; /* Bolder */
  font-family: 'Roboto Condensed', 'Helvetica Neue', sans-serif; /* Professional audio-related font */
  text-align: center;
  letter-spacing: 1.2px;
  position: absolute; /* Absolute positioning */
  bottom: -20px; /* Positioned closer to the vinyl */
  left: 50%; /* Center horizontally */
  transform: translateX(-50%); /* Ensure perfect centering */
  width: auto; /* Let the text determine its width */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Stronger shadow for better readability */
  z-index: 100; /* Very high z-index to ensure it appears above everything */
  padding: 5px 15px; /* Add some padding */
  /* Add a subtle glow effect */
  animation: textGlow 3s infinite alternate ease-in-out;
  /* Add subtle background for better visibility */
  background-color: rgba(10, 26, 47, 0.7); /* Semi-transparent navy background */
  border-radius: 4px; /* Rounded corners */
  border: 1px solid rgba(230, 199, 112, 0.3); /* Subtle gold border to match theme */
}

/* Subtle glow animation for the instruction text */
@keyframes textGlow {
  0% {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  }
  100% {
    text-shadow: 0 1px 5px rgba(230, 199, 112, 0.7);
  }
}

/* Tonearm styles are in TonearmComponent.css */

/* Premium animations - controlled sequence */

/* Spin animation for loading indicator */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animation for rings appearing - gold rings are the focal point */
@keyframes ringAppear {
  0% {
    opacity: 0;
    stroke-opacity: 0;
    stroke-dashoffset: var(--ring-circumference, 880); /* Uses the ring's circumference */
  }
  100% {
    opacity: 1;
    stroke-opacity: 0.9; /* Increased for better visibility */
    stroke-dashoffset: 0;
  }
}

/* Removed micAppear animation as we're using direct opacity transitions */

/* Removed smoothFadeIn animation as we're using direct opacity transitions */

/* Helper classes for animations */
.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
}

.animated-ring {
  stroke-dasharray: 880; /* Circumference of the outer ring (2 * PI * 140) */
  animation: ringAppear 0.3s ease-in-out forwards;
}

/* For the inner ring with r=54 */
.vinyl-ring[r="54"] {
  stroke-dasharray: 339; /* Circumference of the inner ring (2 * PI * 54) */
}

/* Removed animated-fade class as we're using direct opacity transitions instead */

.mic-button-hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

/* Removed mic-button-appearing class as we're using direct opacity transitions */

/* Prevent clicks during animation */
.not-clickable {
  pointer-events: none !important;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -48%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* Removed goldReflection animation - not used in current design */

/* Removed vinyl animation keyframes */

/* Removed goldPulse animation - not used in current design */

/* Removed micGlowPulse animation - not used in current design */

/* Removed glinting effect on microphone grid */

/* Removed micShimmer animation - not used in current design */

/* Note: Removed micMountGlow animation for simplified design */

/* Removed text fade animation for better performance */

/* Note: The vinylRimPulse animation was removed to prevent flickering.
   All rings now use static styling without animations for better visual stability. */

/* Removed groove highlight animations */
