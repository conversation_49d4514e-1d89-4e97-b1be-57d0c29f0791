/**
 * TonearmComponent Styles - Premium Hip-Hop Golden Era Theme
 *
 * Color Scheme:
 * - Deep black vinyl: #050505
 * - Champagne gold accents: #E6C770 (with 0.7 opacity for rings)
 * - Metallic tonearm: gradient from #E0E2E5 to #A9ADB2
 * - Dark base: #222222
 *
 * Key Design Elements:
 * - Realistic vertical tonearm based on professional turntable reference
 * - Enhanced gold trim to match the record's edge
 * - Refined counterweight with subtle 3D effect
 * - Diamond-shaped needle tip for precision appearance
 * - Interactive hover effects and micro-animations
 * - Smooth animations for playing state transitions
 */

/* Container for the tonearm */
.tonearm-container {
  position: absolute;
  top: 65px; /* Adjusted for smaller size */
  right: -80px; /* Adjusted for smaller size */
  width: 100px; /* Reduced from 120px */
  height: 370px; /* Reduced from 450px */
  pointer-events: none; /* Allow clicks to pass through to elements below */
  z-index: 20; /* Position above the vinyl but below any UI controls */
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1); /* Premium smooth transition */
  filter: none; /* Remove drop shadow to ensure transparency */
  opacity: 0; /* Start invisible for load animation */
  transform: translateX(20px); /* Start offset for load animation */
  background: transparent; /* Ensure background is transparent */
}

/* Loaded state - fade in with slight movement */
.tonearm-loaded {
  opacity: 1;
  transform: translateX(0);
}

/* SVG styling */
.tonearm-svg {
  position: absolute;
  top: 0;
  left: 0;
  overflow: visible; /* Allow elements to extend outside the SVG */
  transition: transform 1.2s cubic-bezier(0.34, 1.56, 0.64, 1); /* Premium smooth transition for playing state */
}

/* Tonearm arm styling */
.tonearm-arm {
  transform-origin: 60px 30px; /* Set pivot point - matches the SVG coordinates */
  transform: rotate(0deg); /* Default position - arm is pointing straight down */
  transition: transform 1.2s cubic-bezier(0.34, 1.56, 0.64, 1); /* Premium smooth transition for playing state */
}

/* Playing state - positions the needle on the record */
.tonearm-svg.tonearm-playing .tonearm-arm {
  transform: rotate(45deg); /* Position needle on the record */
  transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1); /* Smoother transition */
}

/* Disabled state */
.tonearm-disabled {
  opacity: 0.6;
  filter: grayscale(30%);
}

/* Gold accent elements - enhanced visibility */
.tonearm-gold-accent {
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 2px rgba(230, 199, 112, 0.7));
}

/* Hover effect on container - make gold accents glow more */
.tonearm-container:hover:not(.tonearm-disabled) .tonearm-gold-accent {
  stroke-opacity: 1;
  filter: drop-shadow(0 0 4px rgba(230, 199, 112, 0.9));
}

/* Diamond tip hover effect */
.tonearm-container:hover:not(.tonearm-disabled) .tonearm-diamond {
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.8));
}

/* Micro-animation for the diamond tip */
@keyframes diamondShine {
  0% { filter: drop-shadow(0 0 1px rgba(255, 255, 255, 0.5)); }
  50% { filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8)); }
  100% { filter: drop-shadow(0 0 1px rgba(255, 255, 255, 0.5)); }
}

.tonearm-diamond {
  animation: diamondShine 3s infinite ease-in-out;
}

/* Micro-animation for the counterweight - subtle movement */
@keyframes counterweightAdjust {
  0% { transform: translateY(0); }
  50% { transform: translateY(-0.5px); }
  100% { transform: translateY(0); }
}

.tonearm-counterweight {
  animation: counterweightAdjust 5s infinite ease-in-out;
}
