/**
 * WaveformVisualizer Component
 *
 * A clean, focused component for visualizing audio waveforms.
 * This component handles:
 * - Rendering waveforms from AudioBuffer data
 * - Displaying a playhead
 * - Handling seek interactions
 * - Applying visual styling with gradients
 *
 * It's designed to be reusable across both recording and preview modes.
 */

import React from 'react';
import { useWaveform, UseWaveformOptions } from '../../audio/hooks/useWaveform';

// Add keyframes for the loading spinner animation
const spinKeyframes = `
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
`;

// Add the keyframes to the document head
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = spinKeyframes;
  document.head.appendChild(style);
}

export interface WaveformVisualizerProps extends UseWaveformOptions {
  className?: string;
  style?: React.CSSProperties;
  precomputedPeaks?: number[][];
}

// Define the component
const WaveformVisualizerComponent = ({
  audioUrl,
  audioBuffer,
  currentTime,
  duration,
  isPlaying,
  onSeek,
  height = 100,
  gradientColors,
  waveformKey,
  className = '',
  style = {},
  precomputedPeaks
}: WaveformVisualizerProps) => {
  // Use the waveform hook
  const {
    waveformCanvasRef,
    playheadCanvasRef,
    containerRef,
    loading,
    error,
    handleSeekClick
  } = useWaveform({
    audioUrl,
    audioBuffer,
    currentTime,
    duration,
    isPlaying,
    onSeek,
    height,
    gradientColors,
    waveformKey,
    precomputedPeaks // Pass precomputed peaks to the hook
  });

  return (
    <div
      ref={containerRef}
      className={`waveform-visualizer relative w-full cursor-pointer ${className}`}
      onClick={handleSeekClick}
      style={{
        height: `${height}px`,
        width: '100%',
        border: '1px solid rgba(230, 199, 112, 0.3)',
        borderRadius: '4px',
        backgroundColor: 'rgba(10, 26, 47, 0.3)',
        ...style
      }}
    >
      {/* Waveform canvas */}
      <canvas
        ref={waveformCanvasRef}
        className="waveform-canvas absolute top-0 left-0 w-full h-full"
        style={{ height: '100%', width: '100%' }}
      />

      {/* Playhead canvas */}
      <canvas
        ref={playheadCanvasRef}
        className="playhead-canvas absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{ height: '100%', width: '100%' }}
      />

      {/* Loading state */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
          <div className="w-6 h-6 rounded-full border-2 border-[rgba(230,199,112,0.3)] border-t-[rgba(230,199,112,0.9)] animate-spin"></div>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
          <div className="text-red-500 text-sm">{error}</div>
        </div>
      )}
    </div>
  );
};

// Custom equality function for memoization
const arePropsEqual = (prevProps: WaveformVisualizerProps, nextProps: WaveformVisualizerProps) => {
  // Only re-render if these props change
  // We don't need to force re-render on initial load anymore since we're using consistent high-quality settings
  return (
    prevProps.audioBuffer === nextProps.audioBuffer &&
    prevProps.audioUrl === nextProps.audioUrl &&
    prevProps.waveformKey === nextProps.waveformKey &&
    prevProps.height === nextProps.height &&
    // Allow small changes in currentTime without re-rendering
    Math.abs(prevProps.currentTime - nextProps.currentTime) < 0.1
  );
};

// Export the memoized component
export const WaveformVisualizer = React.memo(WaveformVisualizerComponent, arePropsEqual);

export default WaveformVisualizer;
