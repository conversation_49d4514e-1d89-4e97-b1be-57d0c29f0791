/**
 * GainControl: A premium microphone gain control slider
 *
 * This component provides a styled slider for controlling microphone gain
 * with a percentage display (0% to 200%).
 * The default value of 1.0 represents 100% (source volume).
 */
import React from 'react';
import './GainControl.css';

interface GainControlProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
}

export const GainControl: React.FC<GainControlProps> = ({
  value,
  onChange,
  min = 0,
  max = 2,
  step = 0.01
}) => {
  return (
    <div className="gain-control-container">
      <div className="slider-with-value">
        <input
          type="range"
          className="volume-slider gain-slider"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(parseFloat(e.target.value))}
          aria-label="Microphone gain control"
          aria-valuemin={min}
          aria-valuemax={max}
          aria-valuenow={value}
        />
        <div className="gain-value-overlay">{Math.round(value * 100)}%</div>
      </div>
    </div>
  );
};

export default GainControl;
