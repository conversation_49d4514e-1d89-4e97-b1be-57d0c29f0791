/**
 * BeatLibraryTab.css: Styling for the beat library tab
 */

.beat-library-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Search and filter section */
.search-filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.search-container {
  width: 100%;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  background: rgba(10, 26, 47, 0.7);
  border: 1px solid rgba(230, 199, 112, 0.3);
  border-radius: 4px;
  color: rgba(230, 230, 250, 0.9);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
  border-color: rgba(230, 199, 112, 0.5);
  box-shadow: 0 0 5px rgba(230, 199, 112, 0.3);
}

.genre-filter, .sort-filter {
  flex: 1;
  padding: 8px 12px;
  background: rgba(10, 26, 47, 0.7);
  border: 1px solid rgba(230, 199, 112, 0.3);
  border-radius: 4px;
  color: rgba(230, 230, 250, 0.9);
  font-size: 14px;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.genre-filter:focus, .sort-filter:focus {
  border-color: rgba(230, 199, 112, 0.5);
  box-shadow: 0 0 5px rgba(230, 199, 112, 0.3);
}

.genre-filter option, .sort-filter option {
  background: #0A1A2F;
  color: rgba(230, 230, 250, 0.9);
}

/* Favorites toggle */
.favorites-toggle {
  margin-top: 5px;
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.toggle-label input {
  margin-right: 8px;
  accent-color: rgba(230, 199, 112, 0.8);
}

.toggle-text {
  font-size: 14px;
  color: rgba(230, 230, 250, 0.9);
}

/* Beat list section */
.beat-list-section {
  flex: 1;
  overflow-y: auto;
}

.beat-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.beat-item {
  background: rgba(10, 26, 47, 0.5);
  border: 1px solid rgba(230, 199, 112, 0.2);
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.beat-item:hover {
  background: rgba(10, 26, 47, 0.7);
  border-color: rgba(230, 199, 112, 0.4);
}

.beat-item.selected {
  background: linear-gradient(145deg, rgba(10, 26, 47, 0.8), rgba(15, 35, 60, 0.8));
  border-color: rgba(230, 199, 112, 0.6);
}

.beat-item-content {
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.beat-item-info {
  flex: 1;
}

.beat-item-title {
  font-size: 15px;
  font-weight: 600;
  color: rgba(230, 230, 250, 1);
  margin-bottom: 4px;
}

.beat-item-details {
  display: flex;
  gap: 10px;
  font-size: 12px;
  color: rgba(230, 230, 250, 0.7);
}

.beat-item-actions {
  display: flex;
  gap: 8px;
}

.favorite-button, .preview-button, .select-button {
  background: rgba(10, 26, 47, 0.7);
  border: 1px solid rgba(230, 199, 112, 0.3);
  border-radius: 4px;
  color: rgba(230, 230, 250, 0.9);
  font-size: 12px;
  padding: 6px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.favorite-button:hover, .preview-button:hover, .select-button:hover {
  background: rgba(15, 35, 60, 0.8);
  border-color: rgba(230, 199, 112, 0.5);
}

.favorite-button.favorited {
  background: rgba(230, 199, 112, 0.2);
  border-color: rgba(230, 199, 112, 0.6);
  color: rgba(230, 199, 112, 0.9);
}

.preview-button.playing {
  background: rgba(230, 199, 112, 0.2);
  border-color: rgba(230, 199, 112, 0.6);
  color: rgba(230, 199, 112, 0.9);
}

.select-button:disabled {
  background: rgba(230, 199, 112, 0.2);
  border-color: rgba(230, 199, 112, 0.4);
  color: rgba(230, 199, 112, 0.7);
  cursor: default;
}

.no-beats-message {
  text-align: center;
  padding: 20px;
  color: rgba(230, 230, 250, 0.7);
  font-style: italic;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(230, 199, 112, 0.3);
  border-top: 3px solid rgba(230, 199, 112, 0.9);
  border-radius: 50%;
  margin: 0 auto 15px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
