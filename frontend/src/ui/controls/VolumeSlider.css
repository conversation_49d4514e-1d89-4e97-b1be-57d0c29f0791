/**
 * VolumeSlider.css: Styling for the volume slider component
 */

.volume-slider-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.slider-with-value {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.volume-slider {
  flex: 1;
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, rgba(230, 199, 112, 0.7), rgba(230, 199, 112, 0.2));
  border-radius: 3px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #E6C770;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #E6C770;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.volume-slider:focus {
  outline: none;
}

.volume-slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(230, 199, 112, 0.3);
}

.volume-slider:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(230, 199, 112, 0.3);
}

.volume-value-overlay {
  position: absolute;
  right: -45px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: rgba(230, 230, 250, 0.9);
  text-align: right;
  font-family: 'Roboto Mono', monospace;
  width: 40px;
}
