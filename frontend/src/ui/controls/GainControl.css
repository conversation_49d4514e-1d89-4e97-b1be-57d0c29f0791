/**
 * GainControl.css: Styling for the gain control component
 */

.gain-control-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.slider-with-value {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.gain-slider {
  flex: 1;
  width: 100%;
  /* Uses the same styling as volume-slider from VolumeSlider.css */
}

.gain-value-overlay {
  position: absolute;
  right: -45px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: rgba(230, 230, 250, 0.9);
  text-align: right;
  font-family: 'Roboto Mono', monospace;
  width: 40px;
}
