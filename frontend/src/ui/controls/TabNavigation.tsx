/**
 * TabNavigation: A premium tab navigation system for the collapsible panel
 * 
 * This component provides a tab-based navigation system for switching between
 * different sections of the collapsible panel.
 */
import React from 'react';
import './TabNavigation.css';

interface Tab {
  id: string;
  label: string;
  icon?: string;
}

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tabId: string) => void;
  tabs: Tab[];
}

export const TabNavigation: React.FC<TabNavigationProps> = ({ 
  activeTab, 
  setActiveTab, 
  tabs 
}) => {
  return (
    <div 
      className="tab-navigation"
      role="tablist"
      aria-label="Panel sections"
    >
      {tabs.map((tab) => (
        <button
          key={tab.id}
          id={`tab-${tab.id}`}
          className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
          onClick={() => setActiveTab(tab.id)}
          role="tab"
          aria-selected={activeTab === tab.id}
          aria-controls={`panel-${tab.id}`}
          tabIndex={activeTab === tab.id ? 0 : -1}
        >
          {tab.icon && <span className="tab-icon" aria-hidden="true">{tab.icon}</span>}
          <span className="tab-label">{tab.label}</span>
        </button>
      ))}
    </div>
  );
};

export default TabNavigation;
