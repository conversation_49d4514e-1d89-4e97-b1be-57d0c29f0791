/**
 * MicrophoneSelector: A dropdown for selecting microphone input
 * 
 * This component provides a styled dropdown for selecting from
 * available microphone inputs.
 */
import React from 'react';
import './MicrophoneSelector.css';

interface MicrophoneSelectorProps {
  availableMics: MediaDeviceInfo[];
  selectedMic: MediaDeviceInfo | null;
  onChange: (mic: MediaDeviceInfo) => void;
}

export const MicrophoneSelector: React.FC<MicrophoneSelectorProps> = ({ 
  availableMics, 
  selectedMic, 
  onChange 
}) => {
  return (
    <select
      className="mic-selector"
      value={selectedMic?.deviceId || ''}
      onChange={(e) => {
        const selected = availableMics.find(mic => mic.deviceId === e.target.value);
        if (selected) {
          onChange(selected);
        }
      }}
      aria-label="Microphone input selector"
    >
      {availableMics.length === 0 && (
        <option value="" disabled>No microphones found</option>
      )}
      {availableMics.map(mic => (
        <option key={mic.deviceId} value={mic.deviceId}>
          {mic.label || `Microphone ${mic.deviceId.slice(0, 5)}`}
        </option>
      ))}
    </select>
  );
};

export default MicrophoneSelector;
