/**
 * TabNavigation.css: Styling for the tab navigation component
 */

.tab-navigation {
  display: flex;
  border-bottom: 1px solid rgba(230, 199, 112, 0.3);
  margin-bottom: 20px;
}

.tab-button {
  flex: 1;
  background: transparent;
  border: none;
  color: rgba(230, 230, 250, 0.7);
  padding: 12px 0;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  font-family: 'Roboto Condensed', 'Helvetica Neue', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  will-change: color; /* Optimize for animation */
}

.tab-button:hover {
  color: rgba(230, 230, 250, 0.9);
}

.tab-button.active {
  color: rgba(230, 199, 112, 0.9);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 15%;
  width: 70%;
  height: 2px;
  background: rgba(230, 199, 112, 0.9);
  border-radius: 1px;
}

.tab-icon {
  font-size: 16px;
}

/* Focus styles for accessibility */
.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(230, 199, 112, 0.5);
  border-radius: 4px;
}
