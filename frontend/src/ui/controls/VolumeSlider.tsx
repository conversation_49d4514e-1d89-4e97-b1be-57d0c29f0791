/**
 * VolumeSlider: A premium volume control slider
 *
 * This component provides a styled slider for controlling volume
 * with a percentage display (0% to 200%).
 * The default value of 1.0 represents 100% (source volume).
 */
import React from 'react';
import './VolumeSlider.css';

interface VolumeSliderProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
}

export const VolumeSlider: React.FC<VolumeSliderProps> = ({
  value,
  onChange,
  min = 0,
  max = 2,
  step = 0.01
}) => {
  return (
    <div className="volume-slider-container">
      <div className="slider-with-value">
        <input
          type="range"
          className="volume-slider"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(parseFloat(e.target.value))}
          aria-label="Volume control"
          aria-valuemin={min}
          aria-valuemax={max}
          aria-valuenow={value}
        />
        <div className="volume-value-overlay">{Math.round(value * 100)}%</div>
      </div>
    </div>
  );
};

export default VolumeSlider;
