/**
 * AudioControlsTab.css: Styling for the audio controls tab
 */

.audio-controls-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100%; /* Ensure content doesn't exceed container width */
}

/* Control section styling */
.control-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(230, 199, 112, 0.15);
}

.control-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: rgba(230, 230, 250, 0.9);
  margin-bottom: 12px;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Roboto Condensed', 'Helvetica Neue', sans-serif;
}

/* Control row styling */
.control-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: nowrap; /* Prevent wrapping */
  width: 100%;
  max-width: 100%;
  overflow: visible;
}

.control-row:last-child {
  margin-bottom: 0;
}

.control-label {
  flex: 0 0 100px; /* Fixed width */
  font-size: 14px;
  color: rgba(230, 230, 250, 0.8);
  margin-right: 10px;
  min-width: 100px;
  max-width: 100px;
  white-space: nowrap;
}

.control-input {
  flex: 2;
  padding: 8px 12px;
  background: rgba(10, 26, 47, 0.7);
  border: 1px solid rgba(230, 199, 112, 0.3);
  border-radius: 4px;
  color: rgba(230, 230, 250, 0.9);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  max-width: 65%; /* Ensure it doesn't overflow */
  min-width: 0; /* Allow flex shrinking */
}

.control-input:focus {
  border-color: rgba(230, 199, 112, 0.5);
  box-shadow: 0 0 5px rgba(230, 199, 112, 0.3);
}

.control-slider {
  flex: 2;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, rgba(230, 199, 112, 0.7), rgba(230, 199, 112, 0.2));
  border-radius: 3px;
  outline: none;
  margin: 0 10px;
  max-width: 65%; /* Ensure it doesn't overflow */
  min-width: 0; /* Allow flex shrinking */
}

.control-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(230, 199, 112, 0.9);
  cursor: pointer;
  border: 2px solid rgba(10, 26, 47, 0.7);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.control-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(230, 199, 112, 0.9);
  cursor: pointer;
  border: 2px solid rgba(10, 26, 47, 0.7);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.control-value {
  flex: 0 0 50px;
  text-align: right;
  font-size: 14px;
  color: rgba(230, 230, 250, 0.8);
  font-family: 'Roboto Mono', monospace;
}

.control-slider-container {
  flex: 1;
  width: calc(100% - 120px); /* Account for label width and margin */
  margin: 0 10px;
  position: relative;
  padding-right: 45px; /* Make room for the value overlay */
}

/* Beat information styling */
.beat-info-section {
  margin-bottom: 25px;
  border-bottom: 1px solid rgba(230, 199, 112, 0.25);
  padding-bottom: 20px;
}

.beat-info {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.beat-title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(230, 230, 250, 1);
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.beat-details {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.beat-detail {
  font-size: 14px;
  color: rgba(230, 230, 250, 0.7);
}

.beat-waveform {
  margin-top: 15px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.beat-waveform-container {
  height: 60px;
  margin: 15px 0;
  background: rgba(10, 26, 47, 0.5);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(230, 199, 112, 0.2);
  width: 100%;
  max-width: 100%;
  position: relative; /* Required for absolute positioning of the waveform */
  display: block; /* Ensure it's displayed as a block element */
  min-height: 60px; /* Ensure minimum height */
}

/* Fix for waveform visualization to match container */
.beat-waveform-container .waveform-visualizer,
.audio-controls-waveform {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

.beat-waveform-container canvas,
.audio-controls-waveform canvas {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(230, 230, 250, 0.8);
  margin-top: 5px;
  font-family: 'Roboto Mono', monospace;
  width: 100%;
  max-width: 100%;
}

.beat-runtime {
  font-size: 14px;
  color: rgba(230, 230, 250, 0.8);
  text-align: right;
  margin-bottom: 10px;
  font-family: 'Roboto Mono', monospace;
}

.beat-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.metadata-pill {
  background: rgba(10, 26, 47, 0.7);
  border: 1px solid rgba(230, 199, 112, 0.2);
  border-radius: 12px;
  padding: 3px 10px;
  font-size: 12px;
  color: rgba(230, 230, 250, 0.9);
  display: inline-block;
}

/* Control group styling */
.control-group {
  margin-bottom: 15px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  display: block;
  font-size: 14px;
  color: rgba(230, 230, 250, 0.8);
  margin-bottom: 5px;
  font-family: 'Roboto Condensed', 'Helvetica Neue', sans-serif;
}
