import React from "react";

export default function StudioLayout({
  children,
  mainVisual,
}: {
  children: React.ReactNode;
  mainVisual?: React.ReactNode;
}) {
  return (
    <div className="min-h-screen w-full bg-gradient-to-b from-hiphop-black via-hiphop-purple/80 to-black text-[#FAFAFA] font-urban flex flex-col items-center justify-start relative overflow-x-hidden">
      {/* Panoramic studio background */}
      <div className="absolute inset-0 z-0">
        <img
          src="/studio-pano.jpg"
          alt="Recording Studio Panoramic Background"
          className="w-full h-full object-cover object-center brightness-75 select-none pointer-events-none z-0 animate-pulse-slow"
          style={{ filter: "blur(1px) saturate(1.2)" }}
        />
        {/* Subtle animated lights and overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-hiphop-purple/30 to-black/80" />
        <div className="absolute left-0 top-0 w-1/3 h-full bg-gradient-to-r from-hiphop-neon/10 to-transparent pointer-events-none" />
        <div className="absolute right-0 top-0 w-1/3 h-full bg-gradient-to-l from-yellow-300/10 to-transparent pointer-events-none" />
      </div>
      {/* Main visual (e.g., microphone, piano) */}
      {mainVisual && (
        <div className="absolute top-1/3 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20 flex items-center justify-center pointer-events-none">
          {mainVisual}
        </div>
      )}
      {/* Glassy overlay for main content */}
      <div className="relative z-10 w-full max-w-6xl mx-auto px-4 py-16 flex flex-col items-center gap-12">
        <div className="bg-white/10 backdrop-blur-2xl rounded-3xl border border-gray-700 shadow-2xl p-10 w-full flex flex-col gap-10">
          {children}
        </div>
      </div>
      {/* Animated LED meters at the bottom */}
      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 flex gap-1 z-20">
        {[...Array(24)].map((_, i) => (
          <div
            key={i}
            className="led-bar bg-hiphop-neon/80 w-1 rounded animate-led-bar"
            style={{ height: `${20 + Math.abs(Math.sin(i)) * 60}px` }}
          />
        ))}
      </div>
    </div>
  );
}
