/**
 * CollapsiblePanel.css: Styling for the collapsible panel component
 */

.collapsible-panel {
  position: absolute;
  top: 80px; /* Align with vinyl's vertical position */
  right: 0;
  width: 320px;
  height: calc(100vh - 160px); /* Adjust height to fit screen */
  background: linear-gradient(145deg, #0D1725, #050A14);
  border-left: 1px solid rgba(230, 199, 112, 0.3);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), opacity 0.3s ease;
  z-index: 50;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  will-change: transform; /* Optimize for animation */
}

.collapsible-panel.collapsed {
  transform: translateX(310px);
}

.panel-toggle {
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 60px;
  background: linear-gradient(145deg, #0D1725, #050A14);
  border: 1px solid rgba(230, 199, 112, 0.3);
  border-right: none;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(230, 199, 112, 0.8);
  font-size: 18px;
  transition: background 0.2s ease, color 0.2s ease;
  z-index: 51;
  will-change: background-color, color; /* Optimize for animation */
}

.panel-toggle:hover {
  background: linear-gradient(145deg, #0F1A30, #070D19);
  color: rgba(230, 199, 112, 1);
}

.panel-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-panel {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(230, 199, 112, 0.3) rgba(10, 26, 47, 0.5);
}

/* Custom scrollbar styling */
.tab-panel::-webkit-scrollbar {
  width: 6px;
}

.tab-panel::-webkit-scrollbar-track {
  background: rgba(10, 26, 47, 0.5);
  border-radius: 3px;
}

.tab-panel::-webkit-scrollbar-thumb {
  background: rgba(230, 199, 112, 0.3);
  border-radius: 3px;
}

.tab-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(230, 199, 112, 0.5);
}

/* Section styling */
.panel-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(230, 199, 112, 0.15);
}

.panel-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: rgba(230, 230, 250, 0.9);
  margin-bottom: 12px;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Roboto Condensed', 'Helvetica Neue', sans-serif;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .collapsible-panel {
    width: 280px;
  }
  
  .collapsible-panel.collapsed {
    transform: translateX(270px);
  }
}

@media (max-width: 768px) {
  .collapsible-panel {
    width: 100%;
    right: -100%;
    border-radius: 0;
    top: 0;
    height: 100vh;
    transform: translateX(0);
  }
  
  .collapsible-panel.collapsed {
    transform: translateX(100%);
  }
  
  .panel-toggle {
    left: -40px;
    width: 40px;
  }
}
