/**
 * CollapsiblePanel: Side panel for audio controls and beat library
 *
 * This component provides a collapsible panel on the right side of the session page
 * with tabs for audio controls and beat library. It serves as a container for
 * technical controls and beat selection functionality.
 *
 * Key Features:
 * - Tabbed interface for organizing controls
 * - Audio Controls tab for microphone and beat volume settings
 * - Beat Library tab for browsing and selecting beats
 * - Collapsible design to maximize recording space when needed
 *
 * Integration Points:
 * - Receives audio state and controls from the session page
 * - Passes microphone and gain settings to the RecordingSystem
 * - Passes beat selection to the session page and useBeatStore
 */
import React, { useState } from 'react';
import { TabNavigation } from '../controls';
import { AudioControlsTab, BeatLibraryTab } from '../layout';
import './CollapsiblePanel.css';

// Tab definitions
const TABS = [
  { id: 'audio', label: 'Audio Controls', icon: '🎚️' },
  { id: 'beats', label: 'Beat Library', icon: '🎵' }
];

interface CollapsiblePanelProps {
  // Panel state
  isCollapsed: boolean;
  toggleCollapse: () => void;

  // Audio controls props
  currentBeat: any;
  beatDuration: number;
  currentTime: number;
  beatVolume: number;
  setBeatVolume: (volume: number) => void;
  micGain: number;
  setMicGain: (gain: number) => void;
  availableMics: MediaDeviceInfo[];
  selectedMic: MediaDeviceInfo | null;
  setSelectedMic: (mic: MediaDeviceInfo) => void;
  beatAudioBuffer: AudioBuffer | null;

  // Beat library props
  beats: any[];
  onBeatSelect: (beat: any) => void;
  onBeatPreview: (beat: any) => void;
  isPreviewPlaying: boolean;
  stopPreview: () => void;
}

export const CollapsiblePanel: React.FC<CollapsiblePanelProps> = ({
  isCollapsed,
  toggleCollapse,
  // Audio controls props
  currentBeat,
  beatDuration,
  currentTime,
  beatVolume,
  setBeatVolume,
  micGain,
  setMicGain,
  availableMics,
  selectedMic,
  setSelectedMic,
  beatAudioBuffer,
  // Beat library props
  beats,
  onBeatSelect,
  onBeatPreview,
  isPreviewPlaying,
  stopPreview
}) => {
  const [activeTab, setActiveTab] = useState('audio');

  return (
    <div
      className={`collapsible-panel ${isCollapsed ? 'collapsed' : ''}`}
      role="region"
      aria-label="Audio controls and beat library panel"
    >
      <button
        className="panel-toggle"
        onClick={toggleCollapse}
        aria-label={isCollapsed ? "Expand audio controls panel" : "Collapse audio controls panel"}
        aria-expanded={!isCollapsed}
      >
        <span aria-hidden="true">{isCollapsed ? '>' : '<'}</span>
      </button>

      <div className="panel-content">
        <TabNavigation
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          tabs={TABS}
        />

        <div
          role="tabpanel"
          aria-labelledby={`tab-${activeTab}`}
          className="tab-panel"
        >
          {activeTab === 'audio' ? (
            <AudioControlsTab
              currentBeat={currentBeat}
              beatDuration={beatDuration}
              currentTime={currentTime}
              beatVolume={beatVolume}
              setBeatVolume={setBeatVolume}
              micGain={micGain}
              setMicGain={setMicGain}
              availableMics={availableMics}
              selectedMic={selectedMic}
              setSelectedMic={setSelectedMic}
              beatAudioBuffer={beatAudioBuffer}
            />
          ) : (
            <BeatLibraryTab
              beats={beats}
              currentBeat={currentBeat}
              onBeatSelect={onBeatSelect}
              onBeatPreview={onBeatPreview}
              isPreviewPlaying={isPreviewPlaying}
              stopPreview={stopPreview}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default CollapsiblePanel;
