/**
 * BeatLibraryTab Component
 *
 * This component displays a list of available beats in the CollapsiblePanel.
 * It allows users to:
 * - Browse available beats with search and filtering
 * - Preview beats before selecting
 * - Select a beat for recording
 * - Filter by genre and sort by different criteria
 * - Mark beats as favorites (UI only, not persisted)
 */
import React, { useState, useEffect } from 'react';
import { useBeatStore } from '../../stores/beatStore';
import '../controls/BeatLibraryTab.css';

interface BeatLibraryTabProps {
  beats: any[];
  currentBeat: any;
  onBeatSelect: (beat: any) => void;
  onBeatPreview: (beat: any) => void;
  isPreviewPlaying: boolean;
  stopPreview: () => void;
}

// Helper function to get a color based on genre
const getGenreColor = (genre: string = '') => {
  const genreMap: Record<string, string> = {
    'Trap': '#5f6fff',
    'Boom Bap': '#a259ff',
    'Lo-Fi': '#ff5f7e',
    'Drill': '#61dafb',
    'Hip Hop': '#ffb86c',
    'R&B': '#8be9fd',
    'Pop': '#50fa7b',
  };

  return genreMap[genre] || '#5f6fff';
};

export const BeatLibraryTab: React.FC<BeatLibraryTabProps> = ({
  beats,
  currentBeat,
  onBeatSelect,
  onBeatPreview,
  isPreviewPlaying,
  stopPreview
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const [filteredBeats, setFilteredBeats] = useState(beats);
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // Get favorite functionality from beat store
  const { favoriteBeats = [] } = useBeatStore(state => ({
    favoriteBeats: state.favoriteBeats || []
  }));

  // Check if a beat is favorited
  const isFavorite = (beatId: string) => {
    if (!favoriteBeats || !Array.isArray(favoriteBeats)) return false;
    return favoriteBeats.some(beat => beat.id === beatId || String(beat.id) === String(beatId));
  };

  // Toggle favorite function (stub since it's not implemented in the store yet)
  const toggleFavorite = (beatId: string) => {
    console.log('Toggle favorite for beat:', beatId);
    // This would be implemented in the beatStore in the future
  };

  // Extract unique genres for filter dropdown
  const genreSet = new Set<string>();
  beats.forEach(beat => {
    const genre = beat.attributes?.genre || beat.genre;
    if (genre) genreSet.add(genre);
  });
  const genres = Array.from(genreSet);

  // Sort beats based on selected criteria
  const sortBeats = (beatsToSort: any[]) => {
    switch(sortBy) {
      case 'newest':
        return [...beatsToSort].sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime());
      case 'oldest':
        return [...beatsToSort].sort((a, b) => new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime());
      case 'bpm':
        const getBpm = (beat: any) => beat.attributes?.bpm || beat.bpm || 0;
        return [...beatsToSort].sort((a, b) => getBpm(a) - getBpm(b));
      case 'name':
        const getTitle = (beat: any) => beat.attributes?.title || beat.title || '';
        return [...beatsToSort].sort((a, b) => getTitle(a).localeCompare(getTitle(b)));
      default:
        return beatsToSort;
    }
  };

  // Filter and sort beats
  useEffect(() => {
    let filtered = [...beats];

    // Filter by favorites if enabled
    if (showFavoritesOnly) {
      filtered = filtered.filter(beat => isFavorite(beat.id));
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(beat => {
        const title = beat.attributes?.title || beat.title || '';
        const artist = beat.attributes?.artist || beat.artist || '';
        const genre = beat.attributes?.genre || beat.genre || '';
        return title.toLowerCase().includes(term) ||
               artist.toLowerCase().includes(term) ||
               genre.toLowerCase().includes(term);
      });
    }

    // Filter by genre
    if (selectedGenre) {
      filtered = filtered.filter(beat => {
        const genre = beat.attributes?.genre || beat.genre || '';
        return genre === selectedGenre;
      });
    }

    // Apply sorting
    const sortedBeats = sortBeats(filtered);
    setFilteredBeats(sortedBeats);
  }, [beats, searchTerm, selectedGenre, sortBy, showFavoritesOnly, favoriteBeats]);

  return (
    <div className="beat-library-tab">
      {/* Search and Filter Section */}
      <div className="panel-section search-filter-section">
        <div className="search-container">
          <input
            type="text"
            className="search-input"
            placeholder="Search beats..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="filter-controls">
          <select
            className="genre-filter"
            value={selectedGenre}
            onChange={(e) => setSelectedGenre(e.target.value)}
          >
            <option value="">All Genres</option>
            {genres.map(genre => (
              <option key={genre} value={genre}>{genre}</option>
            ))}
          </select>

          <select
            className="sort-filter"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="bpm">BPM (Low to High)</option>
            <option value="name">Name (A-Z)</option>
          </select>
        </div>

        <div className="favorites-toggle">
          <label className="toggle-label">
            <input
              type="checkbox"
              checked={showFavoritesOnly}
              onChange={() => setShowFavoritesOnly(!showFavoritesOnly)}
            />
            <span className="toggle-text">Favorites Only</span>
          </label>
        </div>
      </div>

      {/* Beat List Section */}
      <div className="panel-section beat-list-section">
        <h3 className="section-title">Available Beats</h3>

        {filteredBeats.length === 0 ? (
          <div className="no-beats-message">
            {beats.length === 0 ? (
              <>
                <div className="loading-spinner"></div>
                <p>Loading beats...</p>
              </>
            ) : (
              <p>No beats found. Try adjusting your search or filter.</p>
            )}
          </div>
        ) : (
          <div className="beat-list">
            {filteredBeats.map((beat) => {
              const beatTitle = beat.attributes?.title || beat.title;
              const beatGenre = beat.attributes?.genre || beat.genre;
              const beatBpm = beat.attributes?.bpm || beat.bpm;
              const beatColor = getGenreColor(beatGenre);
              const isSelected = String(beat.id) === String(currentBeat?.id);

              return (
                <div
                  key={beat.id}
                  className={`beat-item ${isSelected ? 'selected' : ''}`}
                  style={{
                    borderLeft: isSelected ? `4px solid ${beatColor}` : '4px solid transparent'
                  }}
                >
                  <div className="beat-item-content">
                    <div className="beat-item-info">
                      <div className="beat-item-title">{beatTitle}</div>
                      <div className="beat-item-details">
                        {beatGenre && <span>{beatGenre}</span>}
                        {beatBpm && <span>{beatBpm} BPM</span>}
                      </div>
                    </div>

                    <div className="beat-item-actions">
                      <button
                        className={`favorite-button ${isFavorite(beat.id) ? 'favorited' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFavorite(beat.id);
                        }}
                        aria-label={isFavorite(beat.id) ? 'Remove from favorites' : 'Add to favorites'}
                      >
                        {isFavorite(beat.id) ? '★' : '☆'}
                      </button>

                      <button
                        className={`preview-button ${isPreviewPlaying && currentBeat?.id === beat.id ? 'playing' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (isPreviewPlaying && currentBeat?.id === beat.id) {
                            stopPreview();
                          } else {
                            onBeatPreview(beat);
                          }
                        }}
                        aria-label={isPreviewPlaying && currentBeat?.id === beat.id ? "Stop preview" : "Preview beat"}
                      >
                        {isPreviewPlaying && currentBeat?.id === beat.id ? '■' : '▶'}
                      </button>

                      <button
                        className="select-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          onBeatSelect(beat);
                        }}
                        aria-label={`Select ${beatTitle}`}
                        disabled={isSelected}
                      >
                        {isSelected ? 'Selected' : 'Select'}
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default BeatLibraryTab;
