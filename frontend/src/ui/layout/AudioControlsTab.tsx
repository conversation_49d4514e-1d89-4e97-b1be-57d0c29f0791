/**
 * AudioControlsTab Component
 *
 * This component provides controls for audio settings in the CollapsiblePanel.
 * It includes:
 * - Microphone input selection
 * - Microphone gain control
 * - Beat volume control
 * - Beat waveform visualization
 */
import React from 'react';
import { WaveformVisualizer } from '../visualizers';
import { GainControl, VolumeSlider } from '../controls';
import '../controls/AudioControlsTab.css';

interface AudioControlsTabProps {
  currentBeat: any;
  beatDuration: number;
  currentTime: number;
  beatVolume: number;
  setBeatVolume: (volume: number) => void;
  micGain: number;
  setMicGain: (gain: number) => void;
  availableMics: MediaDeviceInfo[];
  selectedMic: MediaDeviceInfo | null;
  setSelectedMic: (mic: MediaDeviceInfo) => void;
  beatAudioBuffer: AudioBuffer | null;
}

export const AudioControlsTab: React.FC<AudioControlsTabProps> = ({
  currentBeat,
  beatDuration,
  currentTime,
  beatVolume,
  setBeatVolume,
  micGain,
  setMicGain,
  availableMics,
  selectedMic,
  setSelectedMic,
  beatAudioBuffer
}) => {
  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className="audio-controls-tab">
      {/* Beat Info and Waveform Section - Moved to the top */}
      {/* Always show the section, even if there's no current beat */}
        <div className="control-section beat-info-section">
          <h3 className="section-title">Current Beat</h3>
          <div className="beat-info">
            <h3 className="beat-title">
              {currentBeat ? (currentBeat.title || currentBeat.attributes?.title || 'Current Beat') : 'No Beat Selected'}
            </h3>
            {currentBeat && (
              <div className="beat-details">
                <span className="beat-detail">
                  {currentBeat.bpm || currentBeat.attributes?.bpm || '??'} BPM
                </span>
                <span className="beat-detail">
                  {currentBeat.key || currentBeat.attributes?.key || '??'}
                </span>
              </div>
            )}
            <div className="beat-waveform">
              <div className="beat-waveform-container" style={{ height: '60px' }}>
                {/* Debug info */}
                {(() => {
                  console.log("AudioControlsTab rendering waveform:", {
                    hasBeatAudioBuffer: !!beatAudioBuffer,
                    beatAudioBufferLength: beatAudioBuffer?.length,
                    beatAudioUrl: currentBeat?.audio_url,
                    currentTime,
                    beatDuration
                  });
                  return null;
                })()}
                {/* Always use a consistent high-quality waveform visualization */}
                <WaveformVisualizer
                  audioBuffer={beatAudioBuffer}
                  audioUrl={currentBeat?.audio_url || ''}
                  currentTime={currentTime}
                  duration={beatDuration || 0}
                  isPlaying={false}
                  height={60}
                  onSeek={() => {}} // No seek functionality in the controls tab
                  gradientColors={["#5f6fff", "#7a6fff", "#b367ff", "#d45fff", "#ff5f7e", "#ff7a5f"]}
                  waveformKey={`audio-controls-waveform-${currentBeat?.id || 'default'}`} // Use stable key for consistent rendering
                  className="audio-controls-waveform"
                  style={{
                    borderRadius: '4px',
                    overflow: 'hidden',
                    height: '100%',
                    width: '100%',
                    border: '1px solid rgba(230, 199, 112, 0.3)',
                    backgroundColor: 'rgba(10, 26, 47, 0.3)'
                  }}
                />
              </div>
              <div className="time-display">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(beatDuration)}</span>
              </div>
            </div>
          </div>
        </div>

      {/* Microphone Input Section */}
      <div className="control-section">
        <h3 className="section-title">Microphone Input</h3>
        <div className="control-row">
          <label htmlFor="mic-select" className="control-label">
            Input Device
          </label>
          <select
            id="mic-select"
            className="control-input"
            value={selectedMic?.deviceId || 'default'}
            onChange={(e) => {
              const selectedDevice = availableMics.find(
                (device) => device.deviceId === e.target.value
              );
              if (selectedDevice) {
                setSelectedMic(selectedDevice);
              }
            }}
          >
            <option value="default">Default Microphone</option>
            {availableMics.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `Microphone (${device.deviceId.slice(-4)})`}
              </option>
            ))}
          </select>
        </div>
        <div className="control-row">
          <label htmlFor="mic-gain" className="control-label">
            Mic Volume
          </label>
          <div className="control-slider-container">
            <GainControl
              value={micGain}
              onChange={setMicGain}
            />
          </div>
        </div>
      </div>

      {/* Beat Controls Section */}
      <div className="control-section">
        <h3 className="section-title">Beat Controls</h3>
        <div className="control-row">
          <label htmlFor="beat-volume" className="control-label">
            Beat Volume
          </label>
          <div className="control-slider-container">
            <VolumeSlider
              value={beatVolume}
              onChange={setBeatVolume}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioControlsTab;
