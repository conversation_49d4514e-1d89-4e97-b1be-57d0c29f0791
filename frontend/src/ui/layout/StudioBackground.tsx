/**
 * StudioBackground Component
 *
 * A clean, minimal background that focuses solely on the main interface elements
 * without any distracting studio elements
 */
import React from 'react';

interface StudioBackgroundProps {
  className?: string;
}

export const StudioBackground: React.FC<StudioBackgroundProps> = ({ className }) => {
  return (
    <div
      className={`studio-background ${className || ''}`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 0,
        overflow: 'hidden',
        pointerEvents: 'none', // Allow clicks to pass through
      }}
    >
      {/* Clean deep blue gradient background */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'radial-gradient(circle at center, #0D1725 0%, #050A14 80%)',
          opacity: 0.9,
        }}
      />

      {/* No additional studio elements - clean, minimal background */}
    </div>
  );
};

export default StudioBackground;
