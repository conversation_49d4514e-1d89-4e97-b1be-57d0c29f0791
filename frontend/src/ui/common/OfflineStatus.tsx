import { useEffect, useState } from "react";

// Mock implementation since useServiceWorker is not available
const useServiceWorker = () => ({
  isOffline: false,
  isUpdateAvailable: false,
  update: () => console.log('Update called')
});

export function OfflineStatus() {
  const { isOffline, isUpdateAvailable, update } = useServiceWorker();
  const [showOffline, setShowOffline] = useState(false);
  const [showUpdate, setShowUpdate] = useState(false);

  useEffect(() => {
    if (isOffline) {
      setShowOffline(true);
      const timer = setTimeout(() => setShowOffline(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [isOffline]);

  useEffect(() => {
    if (isUpdateAvailable) {
      setShowUpdate(true);
    }
  }, [isUpdateAvailable]);

  if (!showOffline && !showUpdate) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
      {showOffline && (
        <div
          className="bg-yellow-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 animate-fade-in"
          role="alert"
          aria-live="polite"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>You're offline. Some features may be limited.</span>
        </div>
      )}

      {showUpdate && (
        <div
          className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2"
          role="alert"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          <span>Update available!</span>
          <button
            onClick={() => {
              update();
              setShowUpdate(false);
            }}
            className="ml-2 bg-white text-blue-600 px-2 py-1 rounded text-sm hover:bg-blue-50 transition-colors"
            aria-label="Update application"
          >
            Update
          </button>
          <button
            onClick={() => setShowUpdate(false)}
            className="ml-2 text-white/80 hover:text-white"
            aria-label="Dismiss update notification"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}

// Add keyframe animation for fade-in effect
const styles = `
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(1rem);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
`;

// Add styles to document head
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
