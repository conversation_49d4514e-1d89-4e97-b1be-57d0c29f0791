import { useState, useCallback } from "react";
import { useFocusManagement } from "../../hooks/useFocusManagement";

interface DropdownItem {
  id: string | number;
  label: string;
  value: string | number;
}

interface DropdownProps {
  items: DropdownItem[];
  selectedItem?: DropdownItem;
  onChange: (item: DropdownItem) => void;
  label: string;
  id: string;
}

export default function Dropdown({
  items,
  selectedItem,
  onChange,
  label,
  id,
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { containerRef } = useFocusManagement<HTMLDivElement>({
    trapFocus: isOpen,
    restoreFocus: true,
  });

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      switch (e.key) {
        case "ArrowDown":
        case "ArrowUp": {
          e.preventDefault();
          const currentIndex = selectedItem
            ? items.findIndex((item) => item.id === selectedItem.id)
            : -1;
          const nextIndex =
            e.key === "ArrowDown"
              ? (currentIndex + 1) % items.length
              : (currentIndex - 1 + items.length) % items.length;
          onChange(items[nextIndex]);
          break;
        }
        case "Enter":
        case " ":
          e.preventDefault();
          setIsOpen((prev) => !prev);
          break;
        case "Escape":
          setIsOpen(false);
          break;
        default:
          // Handle type-ahead search
          const char = e.key.toLowerCase();
          if (char.length === 1) {
            const matchingItem = items.find((item) =>
              item.label.toLowerCase().startsWith(char),
            );
            if (matchingItem) {
              onChange(matchingItem);
            }
          }
          break;
      }
    },
    [items, selectedItem, onChange],
  );

  return (
    <div
      ref={containerRef}
      className="relative font-urban"
      onKeyDown={handleKeyDown}
    >
      <label
        id={`${id}-label`}
        className="block text-lg font-bold text-hiphop-gold mb-2 tracking-wide"
      >
        {label}
      </label>
      <button
        type="button"
        className="w-full bg-hiphop-black border-2 border-hiphop-gold text-hiphop-gold px-5 py-3 rounded-hiphop flex items-center justify-between hover:bg-hiphop-purple/20 focus:outline-none focus:ring-2 focus:ring-hiphop-neon focus:border-hiphop-neon transition-all duration-200 text-lg font-urban"
        onClick={() => setIsOpen(!isOpen)}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-labelledby={`${id}-label`}
        aria-controls={`${id}-listbox`}
      >
        <span>{selectedItem?.label || "Select an option"}</span>
        <svg
          className={`w-5 h-5 transition-transform ${isOpen ? "rotate-180" : ""}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <ul
          id={`${id}-listbox`}
          role="listbox"
          aria-labelledby={`${id}-label`}
          className="absolute z-10 w-full mt-1 bg-hiphop-black border-2 border-hiphop-gold rounded-hiphop shadow-hiphop max-h-60 overflow-auto focus:outline-none text-lg font-urban"
          tabIndex={-1}
        >
          {items.map((item) => (
            <li
              key={item.id}
              role="option"
              aria-selected={selectedItem?.id === item.id}
              className={`px-5 py-3 cursor-pointer transition-all duration-200 rounded-hiphop font-bold text-hiphop-gold
                ${
                  selectedItem?.id === item.id
                    ? "bg-hiphop-purple/40 text-hiphop-neon"
                    : "hover:bg-hiphop-purple/20 hover:text-hiphop-neon"
                }
              `}
              onClick={() => {
                onChange(item);
                setIsOpen(false);
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  onChange(item);
                  setIsOpen(false);
                }
              }}
              tabIndex={0}
            >
              {item.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
