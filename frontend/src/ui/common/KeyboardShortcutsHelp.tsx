import { useState, useEffect } from "react";
import Modal from "./Modal";
import { useKeyboardShortcuts } from "../../hooks/useKeyboardShortcuts";

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function KeyboardShortcutsHelp({
  isOpen,
  onClose,
}: KeyboardShortcutsHelpProps) {
  const [shortcuts, setShortcuts] = useState<
    Array<{ keys: string[]; description: string }>
  >([]);

  // Define global shortcuts
  const { getShortcutList } = useKeyboardShortcuts([
    {
      keys: ["Control", "?"],
      description: "Show/hide keyboard shortcuts",
      handler: () => {},
      scope: "global",
    },
    {
      keys: ["Control", "/"],
      description: "Focus search",
      handler: () => {},
      scope: "global",
    },
    {
      keys: ["Control", "b"],
      description: "Toggle beat library",
      handler: () => {},
      scope: "global",
    },
    {
      keys: ["Escape"],
      description: "Close modal / Clear selection",
      handler: () => {},
      scope: "global",
    },
  ]);

  useEffect(() => {
    setShortcuts(getShortcutList());
  }, [getShortcutList]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Keyboard Shortcuts">
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-white mb-4">
            Global Shortcuts
          </h3>
          <div className="grid gap-4">
            {shortcuts.map((shortcut, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-gray-300">{shortcut.description}</span>
                <div className="flex items-center space-x-1">
                  {shortcut.keys.map((key, keyIndex) => (
                    <kbd
                      key={keyIndex}
                      className="px-2 py-1 text-sm font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md"
                    >
                      {key}
                    </kbd>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-white mb-4">
            Freestyle Mode
          </h3>
          <div className="grid gap-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Play/Pause beat</span>
              <kbd className="px-2 py-1 text-sm font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md">
                Space
              </kbd>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Skip to next beat</span>
              <kbd className="px-2 py-1 text-sm font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md">
                →
              </kbd>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Previous beat</span>
              <kbd className="px-2 py-1 text-sm font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md">
                ←
              </kbd>
            </div>
          </div>
        </div>

        <div className="pt-4 border-t border-gray-600">
          <p className="text-sm text-gray-400">
            Press{" "}
            <kbd className="px-1 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">
              Control
            </kbd>{" "}
            +{" "}
            <kbd className="px-1 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">
              ?
            </kbd>{" "}
            at any time to show this help dialog
          </p>
        </div>
      </div>
    </Modal>
  );
}
