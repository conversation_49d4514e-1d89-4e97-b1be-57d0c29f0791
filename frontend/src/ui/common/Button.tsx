import React from "react";
import clsx from "clsx";

type ButtonVariant = "primary" | "secondary" | "accent" | "danger";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  children: React.ReactNode;
  className?: string;
}

const baseStyles =
  "font-urban font-bold rounded-hiphop px-6 py-3 shadow-hiphop transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-hiphop-neon focus:ring-offset-2 text-lg";

const variantStyles: Record<ButtonVariant, string> = {
  primary:
    "bg-hiphop-gold text-hiphop-black hover:bg-hiphop-purple hover:text-hiphop-gold hover:shadow-lg active:scale-95",
  secondary:
    "bg-hiphop-black text-hiphop-gold border-2 border-hiphop-gold hover:bg-hiphop-purple hover:text-hiphop-neon hover:border-hiphop-neon",
  accent:
    "bg-hiphop-neon text-hiphop-black hover:bg-hiphop-gold hover:text-hiphop-purple",
  danger:
    "bg-hiphop-red text-white hover:bg-hiphop-black hover:text-hiphop-red",
};

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = "primary", children, className, ...props }, ref) => (
    <button
      ref={ref}
      className={clsx(baseStyles, variantStyles[variant], className)}
      {...props}
    >
      {children}
    </button>
  ),
);
Button.displayName = "Button";

export default Button;
