import { useEffect } from "react";
import { useFocusManagement } from "../../hooks/useFocusManagement";
import Card from "./Card";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export default function Modal({
  isOpen,
  onClose,
  title,
  children,
}: ModalProps) {
  const { /* containerRef */ } = useFocusManagement<HTMLDivElement>({
    trapFocus: isOpen,
    restoreFocus: true,
    autoFocus: true,
  });

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-hiphop-black bg-opacity-80 transition-opacity animate-fade-in"
        aria-hidden="true"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <Card
          className="relative max-w-lg w-full p-8 animate-fade-in"
          accent="gold"
        >
          {/* Close button */}
          <button
            type="button"
            className="absolute top-4 right-4 text-hiphop-gold hover:text-hiphop-neon focus:outline-none focus:ring-2 focus:ring-hiphop-neon rounded-full p-2 bg-hiphop-black/70"
            onClick={onClose}
            aria-label="Close modal"
          >
            <svg
              className="h-7 w-7"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          {/* Title */}
          <h2
            id="modal-title"
            className="text-3xl font-black text-hiphop-gold mb-6 tracking-wider font-urban"
          >
            {title}
          </h2>

          {/* Content */}
          <div className="text-hiphop-gold font-urban text-lg">{children}</div>
        </Card>
      </div>
    </div>
  );
}
