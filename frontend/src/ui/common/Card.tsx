import React from "react";
import clsx from "clsx";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  accent?: "gold" | "purple" | "neon";
}

const accentStyles = {
  gold: "border-hiphop-gold shadow-hiphop",
  purple: "border-hiphop-purple shadow-hiphop",
  neon: "border-hiphop-neon shadow-hiphop",
};

const Card: React.FC<CardProps> = ({
  children,
  className,
  header,
  footer,
  accent = "gold",
}) => (
  <div
    className={clsx(
      "bg-hiphop-black border-2 rounded-hiphop p-6 font-urban text-hiphop-gold transition-all duration-200",
      accentStyles[accent],
      "hover:shadow-2xl hover:scale-[1.01] focus-within:shadow-2xl",
      className,
    )}
    tabIndex={0}
  >
    {header && (
      <div className="mb-4 text-2xl font-black tracking-wide text-hiphop-gold">
        {header}
      </div>
    )}
    <div>{children}</div>
    {footer && (
      <div className="mt-4 border-t border-hiphop-purple pt-4 text-hiphop-neon">
        {footer}
      </div>
    )}
  </div>
);

export default Card;
