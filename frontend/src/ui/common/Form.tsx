import {
  useId,
  createContext,
  useContext,
  useCallback,
  ReactNode,
  useState,
} from "react";
// Toast context not available, creating a simple mock
const useToast = () => ({
  showToast: (message: string) => console.log('Toast:', message)
});

interface FormContextType {
  register: (name: string) => {
    id: string;
    "aria-describedby"?: string;
    "aria-invalid"?: boolean;
  };
  setError: (name: string, message: string) => void;
  clearError: (name: string) => void;
  getError: (name: string) => string | undefined;
}

const FormContext = createContext<FormContextType | null>(null);

interface FormProps {
  children: ReactNode;
  onSubmit: (e: React.FormEvent) => void;
  "aria-label"?: string;
  className?: string;
}

export function Form({
  children,
  onSubmit,
  "aria-label": ariaLabel,
  className,
}: FormProps) {
  const formId = useId();
  const { showToast } = useToast();
  const [errors, setErrors] = useState<Map<string, string>>(new Map());

  const register = useCallback(
    (name: string) => {
      const id = `${formId}-${name}`;
      const error = errors.get(name);

      return {
        id,
        "aria-describedby": error ? `${id}-error` : undefined,
        "aria-invalid": error ? true : undefined,
      };
    },
    [formId, errors],
  );

  const setError = useCallback(
    (name: string, message: string) => {
      setErrors((prev) => {
        const newErrors = new Map(prev);
        newErrors.set(name, message);
        return newErrors;
      });
      showToast(`Error: ${message}`);
    },
    [showToast],
  );

  const clearError = useCallback((name: string) => {
    setErrors((prev) => {
      const newErrors = new Map(prev);
      newErrors.delete(name);
      return newErrors;
    });
  }, []);

  const getError = useCallback(
    (name: string) => {
      return errors.get(name);
    },
    [errors],
  );

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit(e);
    },
    [onSubmit],
  );

  return (
    <FormContext.Provider value={{ register, setError, clearError, getError }}>
      <form
        onSubmit={handleSubmit}
        aria-label={ariaLabel}
        className={className}
        noValidate
      >
        {children}
      </form>
    </FormContext.Provider>
  );
}

interface FormFieldProps {
  name: string;
  label: string;
  type?: string;
  required?: boolean;
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  autoComplete?: string;
  className?: string;
  validate?: (value: string) => string | undefined;
  children?: ReactNode;
}

export function FormField({
  name,
  label,
  type = "text",
  required,
  pattern,
  minLength,
  maxLength,
  min,
  max,
  autoComplete,
  className,
  validate,
  children,
}: FormFieldProps) {
  const form = useContext(FormContext);
  if (!form) throw new Error("FormField must be used within a Form");

  const { register, getError } = form;
  const { id, ...attrs } = register(name);
  const error = getError(name);

  const handleValidation = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      const value = e.target.value;
      let message: string | undefined;

      if (required && !value) {
        message = `${label} is required`;
      } else if (pattern && !new RegExp(pattern).test(value)) {
        message = `${label} format is invalid`;
      } else if (minLength && value.length < minLength) {
        message = `${label} must be at least ${minLength} characters`;
      } else if (maxLength && value.length > maxLength) {
        message = `${label} must be no more than ${maxLength} characters`;
      } else if (min && Number(value) < min) {
        message = `${label} must be at least ${min}`;
      } else if (max && Number(value) > max) {
        message = `${label} must be no more than ${max}`;
      } else if (validate) {
        message = validate(value);
      }

      if (message) {
        form.setError(name, message);
      } else {
        form.clearError(name);
      }
    },
    [
      form,
      name,
      label,
      required,
      pattern,
      minLength,
      maxLength,
      min,
      max,
      validate,
    ],
  );

  return (
    <div className={className}>
      <label
        htmlFor={id}
        className="block text-lg font-bold font-urban text-hiphop-gold mb-2 tracking-wide"
      >
        {label}
        {required && (
          <span className="text-hiphop-red ml-1" aria-hidden="true">
            *
          </span>
        )}
      </label>
      <input
        id={id}
        name={name}
        type={type}
        required={required}
        pattern={pattern}
        minLength={minLength}
        maxLength={maxLength}
        min={min}
        max={max}
        autoComplete={autoComplete}
        className="w-full bg-hiphop-black border-2 border-hiphop-gold text-hiphop-gold px-5 py-3 rounded-hiphop font-urban text-lg focus:outline-none focus:ring-2 focus:ring-hiphop-neon focus:border-hiphop-neon transition-all duration-200 placeholder:text-hiphop-gold/50"
        onBlur={handleValidation}
        {...attrs}
      />
      {error && (
        <div
          id={`${id}-error`}
          className="mt-1 text-sm text-hiphop-red font-semibold"
          role="alert"
        >
          {error}
        </div>
      )}
      {children}
    </div>
  );
}

export function useForm() {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error("useForm must be used within a Form");
  }
  return context;
}
