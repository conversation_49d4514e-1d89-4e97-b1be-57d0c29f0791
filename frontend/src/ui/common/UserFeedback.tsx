/**
 * User Feedback System
 * 
 * Provides comprehensive user feedback for operations, errors, and status updates.
 * Includes toast notifications, loading states, and error recovery options.
 */

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';

export type FeedbackType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface FeedbackMessage {
  id: string;
  type: FeedbackType;
  title: string;
  message: string;
  duration?: number;
  actions?: FeedbackAction[];
  metadata?: Record<string, any>;
  timestamp: number;
}

export interface FeedbackAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

interface FeedbackContextType {
  messages: FeedbackMessage[];
  showFeedback: (feedback: Omit<FeedbackMessage, 'id' | 'timestamp'>) => string;
  hideFeedback: (id: string) => void;
  clearAll: () => void;
  showSuccess: (title: string, message: string, duration?: number) => string;
  showError: (title: string, message: string, actions?: FeedbackAction[]) => string;
  showWarning: (title: string, message: string, duration?: number) => string;
  showInfo: (title: string, message: string, duration?: number) => string;
  showLoading: (title: string, message: string) => string;
}

const FeedbackContext = createContext<FeedbackContextType | null>(null);

export function FeedbackProvider({ children }: { children: React.ReactNode }) {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);

  const showFeedback = useCallback((feedback: Omit<FeedbackMessage, 'id' | 'timestamp'>) => {
    const id = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newMessage: FeedbackMessage = {
      ...feedback,
      id,
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, newMessage]);

    // Auto-hide after duration (if specified)
    if (feedback.duration && feedback.duration > 0) {
      setTimeout(() => {
        hideFeedback(id);
      }, feedback.duration);
    }

    return id;
  }, []);

  const hideFeedback = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setMessages([]);
  }, []);

  const showSuccess = useCallback((title: string, message: string, duration = 5000) => {
    return showFeedback({ type: 'success', title, message, duration });
  }, [showFeedback]);

  const showError = useCallback((title: string, message: string, actions?: FeedbackAction[]) => {
    return showFeedback({ type: 'error', title, message, actions });
  }, [showFeedback]);

  const showWarning = useCallback((title: string, message: string, duration = 7000) => {
    return showFeedback({ type: 'warning', title, message, duration });
  }, [showFeedback]);

  const showInfo = useCallback((title: string, message: string, duration = 5000) => {
    return showFeedback({ type: 'info', title, message, duration });
  }, [showFeedback]);

  const showLoading = useCallback((title: string, message: string) => {
    return showFeedback({ type: 'loading', title, message });
  }, [showFeedback]);

  const value: FeedbackContextType = {
    messages,
    showFeedback,
    hideFeedback,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading
  };

  return (
    <FeedbackContext.Provider value={value}>
      {children}
      <FeedbackContainer />
    </FeedbackContext.Provider>
  );
}

export function useFeedback() {
  const context = useContext(FeedbackContext);
  if (!context) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
}

function FeedbackContainer() {
  const { messages, hideFeedback } = useFeedback();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {messages.map(message => (
        <FeedbackToast
          key={message.id}
          message={message}
          onClose={() => hideFeedback(message.id)}
        />
      ))}
    </div>
  );
}

function FeedbackToast({ message, onClose }: { message: FeedbackMessage; onClose: () => void }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation
  };

  const getIcon = () => {
    switch (message.type) {
      case 'success':
        return (
          <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'loading':
        return (
          <svg className="w-5 h-5 text-blue-400 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      default:
        return null;
    }
  };

  const getBackgroundColor = () => {
    switch (message.type) {
      case 'success':
        return 'bg-green-900/90 border-green-500/50';
      case 'error':
        return 'bg-red-900/90 border-red-500/50';
      case 'warning':
        return 'bg-yellow-900/90 border-yellow-500/50';
      case 'info':
        return 'bg-blue-900/90 border-blue-500/50';
      case 'loading':
        return 'bg-blue-900/90 border-blue-500/50';
      default:
        return 'bg-gray-900/90 border-gray-500/50';
    }
  };

  return (
    <div
      className={`
        ${getBackgroundColor()}
        backdrop-blur-md rounded-lg border p-4 shadow-lg
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold text-white mb-1">
            {message.title}
          </h4>
          <p className="text-sm text-gray-300">
            {message.message}
          </p>
          
          {message.actions && message.actions.length > 0 && (
            <div className="mt-3 flex space-x-2">
              {message.actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className={`
                    px-3 py-1 text-xs font-medium rounded transition-colors
                    ${action.style === 'primary' ? 'bg-blue-600 hover:bg-blue-700 text-white' :
                      action.style === 'danger' ? 'bg-red-600 hover:bg-red-700 text-white' :
                      'bg-gray-600 hover:bg-gray-700 text-white'}
                  `}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
        
        {message.type !== 'loading' && (
          <button
            onClick={handleClose}
            className="flex-shrink-0 text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
}

/**
 * Specialized feedback hooks for common scenarios
 */
export function useAudioFeedback() {
  const feedback = useFeedback();

  const showMicrophoneError = useCallback((error: Error) => {
    if (error.message.includes('permission') || error.message.includes('denied')) {
      return feedback.showError(
        'Microphone Permission Required',
        'Please allow microphone access to record audio.',
        [
          {
            label: 'Grant Permission',
            action: () => {
              // Trigger permission request
              navigator.mediaDevices.getUserMedia({ audio: true }).catch(console.error);
            },
            style: 'primary'
          }
        ]
      );
    } else {
      return feedback.showError(
        'Microphone Error',
        'Unable to access microphone. Please check your device settings.',
        [
          {
            label: 'Retry',
            action: () => window.location.reload(),
            style: 'primary'
          }
        ]
      );
    }
  }, [feedback]);

  const showRecordingStarted = useCallback(() => {
    return feedback.showSuccess(
      'Recording Started',
      'Your freestyle session is now being recorded.'
    );
  }, [feedback]);

  const showRecordingStopped = useCallback(() => {
    return feedback.showSuccess(
      'Recording Stopped',
      'Your recording has been saved and is ready for preview.'
    );
  }, [feedback]);

  const showProcessingRecording = useCallback(() => {
    return feedback.showLoading(
      'Processing Recording',
      'Mixing your vocals with the beat...'
    );
  }, [feedback]);

  const showBeatLoadError = useCallback((beatTitle: string) => {
    return feedback.showError(
      'Beat Loading Failed',
      `Unable to load "${beatTitle}". Please try a different beat.`,
      [
        {
          label: 'Retry',
          action: () => window.location.reload(),
          style: 'primary'
        }
      ]
    );
  }, [feedback]);

  return {
    showMicrophoneError,
    showRecordingStarted,
    showRecordingStopped,
    showProcessingRecording,
    showBeatLoadError
  };
}

/**
 * Hook for operation status feedback
 */
export function useOperationFeedback() {
  const feedback = useFeedback();
  const [operations, setOperations] = useState<Map<string, string>>(new Map());

  const startOperation = useCallback((operationId: string, title: string, message: string) => {
    const feedbackId = feedback.showLoading(title, message);
    setOperations(prev => new Map(prev).set(operationId, feedbackId));
    return feedbackId;
  }, [feedback]);

  const completeOperation = useCallback((operationId: string, title: string, message: string) => {
    const feedbackId = operations.get(operationId);
    if (feedbackId) {
      feedback.hideFeedback(feedbackId);
      setOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(operationId);
        return newMap;
      });
    }
    return feedback.showSuccess(title, message);
  }, [feedback, operations]);

  const failOperation = useCallback((operationId: string, title: string, message: string, actions?: FeedbackAction[]) => {
    const feedbackId = operations.get(operationId);
    if (feedbackId) {
      feedback.hideFeedback(feedbackId);
      setOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(operationId);
        return newMap;
      });
    }
    return feedback.showError(title, message, actions);
  }, [feedback, operations]);

  return {
    startOperation,
    completeOperation,
    failOperation
  };
}
