/**
 * Retry Mechanism Utilities
 * 
 * Provides robust retry mechanisms for failed operations with
 * exponential backoff, circuit breaker patterns, and error classification.
 */

import { performanceMonitor } from './performance';

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (attempt: number, error: Error) => void;
  onFailure?: (error: Error, attempts: number) => void;
  timeout?: number;
}

export interface CircuitBreakerOptions {
  failureThreshold?: number;
  resetTimeout?: number;
  monitoringPeriod?: number;
}

export type RetryableOperation<T> = () => Promise<T>;

/**
 * Retry an operation with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: RetryableOperation<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffMultiplier = 2,
    retryCondition = () => true,
    onRetry,
    onFailure,
    timeout = 30000
  } = options;

  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      // Add timeout to the operation
      const result = await Promise.race([
        operation(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Operation timeout')), timeout)
        )
      ]);

      // Record successful retry if not first attempt
      if (attempt > 1) {
        performanceMonitor.recordMetric({
          name: 'retry_success',
          value: attempt,
          timestamp: Date.now(),
          type: 'custom',
          metadata: { attempts: attempt }
        });
      }

      return result;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Check if we should retry this error
      if (!retryCondition(lastError)) {
        throw lastError;
      }

      // If this is the last attempt, don't retry
      if (attempt === maxAttempts) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        baseDelay * Math.pow(backoffMultiplier, attempt - 1),
        maxDelay
      );

      // Add jitter to prevent thundering herd
      const jitteredDelay = delay + Math.random() * 1000;

      console.warn(`Operation failed (attempt ${attempt}/${maxAttempts}), retrying in ${jitteredDelay}ms:`, lastError.message);

      // Call retry callback
      if (onRetry) {
        onRetry(attempt, lastError);
      }

      // Record retry attempt
      performanceMonitor.recordMetric({
        name: 'retry_attempt',
        value: attempt,
        timestamp: Date.now(),
        type: 'custom',
        metadata: { 
          error: lastError.message,
          delay: jitteredDelay
        }
      });

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, jitteredDelay));
    }
  }

  // All attempts failed
  if (onFailure) {
    onFailure(lastError!, maxAttempts);
  }

  // Record final failure
  performanceMonitor.recordMetric({
    name: 'retry_failure',
    value: maxAttempts,
    timestamp: Date.now(),
    type: 'custom',
    metadata: { 
      error: lastError!.message,
      totalAttempts: maxAttempts
    }
  });

  throw lastError!;
}

/**
 * Circuit Breaker implementation
 */
export class CircuitBreaker<T> {
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private successCount = 0;

  constructor(
    private operation: RetryableOperation<T>,
    private options: CircuitBreakerOptions = {}
  ) {}

  async execute(): Promise<T> {
    const {
      failureThreshold = 5,
      resetTimeout = 60000,
      monitoringPeriod = 10000
    } = this.options;

    // Check if circuit should be reset
    if (this.state === 'open' && Date.now() - this.lastFailureTime > resetTimeout) {
      this.state = 'half-open';
      this.successCount = 0;
      console.log('Circuit breaker transitioning to half-open state');
    }

    // If circuit is open, fail fast
    if (this.state === 'open') {
      throw new Error('Circuit breaker is open - operation not attempted');
    }

    try {
      const result = await this.operation();
      
      // Success - reset failure count and close circuit if half-open
      if (this.state === 'half-open') {
        this.successCount++;
        if (this.successCount >= 2) { // Require multiple successes to close
          this.state = 'closed';
          this.failureCount = 0;
          console.log('Circuit breaker closed after successful operations');
        }
      } else {
        this.failureCount = 0;
      }

      return result;
    } catch (error) {
      this.failureCount++;
      this.lastFailureTime = Date.now();

      // Open circuit if failure threshold reached
      if (this.failureCount >= failureThreshold) {
        this.state = 'open';
        console.warn(`Circuit breaker opened after ${this.failureCount} failures`);
        
        performanceMonitor.recordMetric({
          name: 'circuit_breaker_opened',
          value: this.failureCount,
          timestamp: Date.now(),
          type: 'custom',
          metadata: { operation: this.operation.name }
        });
      }

      throw error;
    }
  }

  getState() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      successCount: this.successCount
    };
  }

  reset() {
    this.state = 'closed';
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.successCount = 0;
  }
}

/**
 * Predefined retry conditions for common scenarios
 */
export const RetryConditions = {
  // Retry on network errors
  networkErrors: (error: Error): boolean => {
    const message = error.message.toLowerCase();
    return message.includes('network') || 
           message.includes('fetch') || 
           message.includes('timeout') ||
           message.includes('connection');
  },

  // Retry on audio-related errors (but not permission errors)
  audioErrors: (error: Error): boolean => {
    const message = error.message.toLowerCase();
    return (message.includes('audio') || message.includes('microphone')) &&
           !message.includes('permission') &&
           !message.includes('denied');
  },

  // Retry on temporary errors
  temporaryErrors: (error: Error): boolean => {
    const message = error.message.toLowerCase();
    return message.includes('temporary') ||
           message.includes('busy') ||
           message.includes('unavailable') ||
           message.includes('timeout');
  },

  // Don't retry on user errors
  notUserErrors: (error: Error): boolean => {
    const message = error.message.toLowerCase();
    return !message.includes('permission') &&
           !message.includes('denied') &&
           !message.includes('invalid') &&
           !message.includes('unauthorized');
  }
};

/**
 * Specialized retry functions for common operations
 */
export const AudioRetry = {
  /**
   * Retry microphone access with user-friendly handling
   */
  async getMicrophone(deviceId?: string): Promise<MediaStream> {
    return retryWithBackoff(
      async () => {
        return navigator.mediaDevices.getUserMedia({
          audio: deviceId ? { deviceId } : true
        });
      },
      {
        maxAttempts: 3,
        baseDelay: 2000,
        retryCondition: RetryConditions.temporaryErrors,
        onRetry: (attempt, error) => {
          console.log(`Retrying microphone access (attempt ${attempt}):`, error.message);
        }
      }
    );
  },

  /**
   * Retry audio context initialization
   */
  async initializeAudioContext(): Promise<AudioContext> {
    return retryWithBackoff(
      async () => {
        const context = new AudioContext({
          latencyHint: 'interactive',
          sampleRate: 48000
        });
        
        if (context.state === 'suspended') {
          await context.resume();
        }
        
        return context;
      },
      {
        maxAttempts: 3,
        baseDelay: 1000,
        retryCondition: RetryConditions.audioErrors
      }
    );
  },

  /**
   * Retry beat loading with network error handling
   */
  async loadBeat(url: string): Promise<ArrayBuffer> {
    return retryWithBackoff(
      async () => {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to load beat: ${response.status} ${response.statusText}`);
        }
        return response.arrayBuffer();
      },
      {
        maxAttempts: 3,
        baseDelay: 2000,
        retryCondition: RetryConditions.networkErrors,
        onRetry: (attempt, error) => {
          console.log(`Retrying beat load (attempt ${attempt}):`, error.message);
        }
      }
    );
  }
};

/**
 * React hook for retry operations
 */
export function useRetry() {
  const retry = async <T>(
    operation: RetryableOperation<T>,
    options?: RetryOptions
  ): Promise<T> => {
    return retryWithBackoff(operation, options);
  };

  const createCircuitBreaker = <T>(
    operation: RetryableOperation<T>,
    options?: CircuitBreakerOptions
  ): CircuitBreaker<T> => {
    return new CircuitBreaker(operation, options);
  };

  return {
    retry,
    createCircuitBreaker,
    AudioRetry,
    RetryConditions
  };
}

/**
 * Global circuit breakers for common operations
 */
export const GlobalCircuitBreakers = {
  audioContext: new CircuitBreaker(
    () => AudioRetry.initializeAudioContext(),
    { failureThreshold: 3, resetTimeout: 30000 }
  ),

  microphone: new CircuitBreaker(
    () => AudioRetry.getMicrophone(),
    { failureThreshold: 5, resetTimeout: 60000 }
  )
};
