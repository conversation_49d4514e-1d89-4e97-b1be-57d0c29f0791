/**
 * Performance Monitoring Utilities
 * 
 * Provides comprehensive performance monitoring for the freestyle app,
 * including audio processing metrics, component render times, and
 * memory usage tracking.
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'memory' | 'audio' | 'custom';
  metadata?: Record<string, any>;
}

interface AudioMetrics {
  latency: number;
  bufferSize: number;
  sampleRate: number;
  processingTime: number;
  dropouts: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isMonitoring = false;

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    if (typeof window === 'undefined') return;

    // Observe navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: entry.name,
              value: entry.duration,
              timestamp: Date.now(),
              type: 'timing',
              metadata: { entryType: entry.entryType }
            });
          }
        });
        navObserver.observe({ entryTypes: ['navigation', 'measure', 'mark'] });
        this.observers.push(navObserver);
      } catch (e) {
        console.warn('Performance observer not supported:', e);
      }
    }
  }

  startMonitoring() {
    this.isMonitoring = true;
    this.startMemoryMonitoring();
    console.log('Performance monitoring started');
  }

  stopMonitoring() {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    console.log('Performance monitoring stopped');
  }

  private startMemoryMonitoring() {
    if (!this.isMonitoring) return;

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.recordMetric({
          name: 'memory_usage',
          value: memory.usedJSHeapSize,
          timestamp: Date.now(),
          type: 'memory',
          metadata: {
            totalJSHeapSize: memory.totalJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit
          }
        });
      }

      if (this.isMonitoring) {
        setTimeout(checkMemory, 5000); // Check every 5 seconds
      }
    };

    checkMemory();
  }

  recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log critical performance issues
    if (metric.type === 'audio' && metric.value > 100) {
      console.warn(`High audio latency detected: ${metric.value}ms`);
    }
  }

  // Audio-specific performance tracking
  recordAudioMetrics(metrics: AudioMetrics) {
    Object.entries(metrics).forEach(([key, value]) => {
      this.recordMetric({
        name: `audio_${key}`,
        value: typeof value === 'number' ? value : 0,
        timestamp: Date.now(),
        type: 'audio',
        metadata: { audioMetrics: metrics }
      });
    });
  }

  // Component render time tracking
  measureComponentRender<T>(componentName: string, renderFn: () => T): T {
    const startTime = performance.now();
    const result = renderFn();
    const endTime = performance.now();

    this.recordMetric({
      name: `component_render_${componentName}`,
      value: endTime - startTime,
      timestamp: Date.now(),
      type: 'timing',
      metadata: { component: componentName }
    });

    return result;
  }

  // Async operation timing
  async measureAsync<T>(operationName: string, asyncFn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    try {
      const result = await asyncFn();
      const endTime = performance.now();
      
      this.recordMetric({
        name: `async_${operationName}`,
        value: endTime - startTime,
        timestamp: Date.now(),
        type: 'timing',
        metadata: { operation: operationName, success: true }
      });

      return result;
    } catch (error) {
      const endTime = performance.now();
      
      this.recordMetric({
        name: `async_${operationName}`,
        value: endTime - startTime,
        timestamp: Date.now(),
        type: 'timing',
        metadata: { operation: operationName, success: false, error: error.message }
      });

      throw error;
    }
  }

  // Get performance summary
  getSummary() {
    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 60000); // Last minute

    const summary = {
      totalMetrics: this.metrics.length,
      recentMetrics: recentMetrics.length,
      averageRenderTime: this.getAverageByType('timing', recentMetrics),
      memoryUsage: this.getLatestByName('memory_usage'),
      audioLatency: this.getAverageByName('audio_latency', recentMetrics),
      criticalIssues: this.getCriticalIssues(recentMetrics)
    };

    return summary;
  }

  private getAverageByType(type: string, metrics = this.metrics): number {
    const typeMetrics = metrics.filter(m => m.type === type);
    if (typeMetrics.length === 0) return 0;
    
    const sum = typeMetrics.reduce((acc, m) => acc + m.value, 0);
    return sum / typeMetrics.length;
  }

  private getAverageByName(name: string, metrics = this.metrics): number {
    const nameMetrics = metrics.filter(m => m.name === name);
    if (nameMetrics.length === 0) return 0;
    
    const sum = nameMetrics.reduce((acc, m) => acc + m.value, 0);
    return sum / nameMetrics.length;
  }

  private getLatestByName(name: string): PerformanceMetric | null {
    const nameMetrics = this.metrics.filter(m => m.name === name);
    return nameMetrics.length > 0 ? nameMetrics[nameMetrics.length - 1] : null;
  }

  private getCriticalIssues(metrics: PerformanceMetric[]): string[] {
    const issues: string[] = [];

    // Check for high render times
    const renderMetrics = metrics.filter(m => m.name.includes('component_render'));
    const avgRenderTime = this.getAverageByType('timing', renderMetrics);
    if (avgRenderTime > 16) { // 60fps = 16.67ms per frame
      issues.push(`High average render time: ${avgRenderTime.toFixed(2)}ms`);
    }

    // Check for memory leaks
    const memoryMetrics = metrics.filter(m => m.name === 'memory_usage');
    if (memoryMetrics.length >= 2) {
      const latest = memoryMetrics[memoryMetrics.length - 1];
      const previous = memoryMetrics[memoryMetrics.length - 2];
      const memoryIncrease = latest.value - previous.value;
      if (memoryIncrease > 10 * 1024 * 1024) { // 10MB increase
        issues.push(`Potential memory leak: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB increase`);
      }
    }

    // Check for audio issues
    const audioLatency = this.getAverageByName('audio_latency', metrics);
    if (audioLatency > 50) {
      issues.push(`High audio latency: ${audioLatency.toFixed(2)}ms`);
    }

    return issues;
  }

  // Export metrics for analysis
  exportMetrics() {
    return {
      metrics: this.metrics,
      summary: this.getSummary(),
      timestamp: Date.now()
    };
  }

  // Clear all metrics
  clearMetrics() {
    this.metrics = [];
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceMonitor() {
  const measureRender = (componentName: string) => {
    return (renderFn: () => any) => 
      performanceMonitor.measureComponentRender(componentName, renderFn);
  };

  const measureAsync = (operationName: string) => {
    return (asyncFn: () => Promise<any>) => 
      performanceMonitor.measureAsync(operationName, asyncFn);
  };

  return {
    measureRender,
    measureAsync,
    recordMetric: performanceMonitor.recordMetric.bind(performanceMonitor),
    getSummary: performanceMonitor.getSummary.bind(performanceMonitor)
  };
}

// Performance timing decorator
export function performanceTiming(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measureAsync(
        `${target.constructor.name}.${propertyName}`,
        () => method.apply(this, args)
      );
    };

    return descriptor;
  };
}

// Initialize monitoring in development
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.startMonitoring();
}
