"use client";
import React from "react";

// --- Viral Mechanic Placeholders ---
const LiveUserCounter = () => (
  <div className="fixed bottom-4 right-4 bg-gradient-to-r from-[#5f6fff]/80 to-[#a259ff]/80 px-4 py-2 rounded-full text-sm font-bold shadow-lg z-30">
    <span className="animate-pulse">🎤 1,204 artists in session</span>
  </div>
);

// 1. Studio Background Overlays & Lighting Effects
const StudioBackgroundOverlays = () => (
  <>
    {/* Blurred mixing board SVG overlay */}
    <svg
      className="absolute inset-0 w-full h-full z-0 pointer-events-none"
      style={{ opacity: 0.1 }}
      viewBox="0 0 1440 900"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="200"
        y="600"
        width="1040"
        height="80"
        rx="40"
        fill="#5f6fff"
        fillOpacity="0.2"
      />
      <rect
        x="400"
        y="700"
        width="640"
        height="40"
        rx="20"
        fill="#a259ff"
        fillOpacity="0.18"
      />
      {/* Faint microphone silhouette */}
      <ellipse
        cx="1200"
        cy="300"
        rx="40"
        ry="120"
        fill="#fff"
        fillOpacity="0.08"
      />
      {/* Faint soundwave */}
      <polyline
        points="0,800 100,780 200,820 300,760 400,830 500,790 600,850 700,770 800,800 900,840 1000,780 1100,820 1200,790 1300,850 1400,800"
        stroke="#fff"
        strokeWidth="8"
        opacity="0.07"
      />
    </svg>
    {/* Gradient and spotlight effects */}
    <div className="absolute inset-0 z-0 pointer-events-none">
      <div className="absolute left-1/2 top-0 -translate-x-1/2 w-[700px] h-[300px] bg-gradient-radial from-[#a259ff44] to-transparent blur-3xl opacity-70" />
      <div className="absolute right-0 top-1/3 w-[300px] h-[200px] bg-gradient-to-br from-[#5f6fff55] to-transparent blur-2xl opacity-60 rotate-12" />
      <div className="absolute left-0 bottom-0 w-[400px] h-[180px] bg-gradient-to-tr from-[#ff5f7e55] to-transparent blur-2xl opacity-50 -rotate-6" />
      {/* Light streak */}
      <div className="absolute left-1/4 top-1/2 w-[400px] h-1 bg-gradient-to-r from-[#fff7] via-[#a259ff88] to-transparent blur-md opacity-40 rotate-2" />
    </div>
  </>
);

// 2. Studio-Themed Icons
// StudioIcons removed as per new design

// Music Note Icon for Beat Cards
const MusicNoteIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    className="mx-auto mb-1"
    aria-hidden="true"
  >
    <g>
      <ellipse cx="22" cy="25" rx="5" ry="4" fill="#FF6F61" />
      <ellipse cx="10" cy="27" rx="4" ry="3" fill="#FF6F61" fillOpacity="0.7" />
      <rect x="15" y="7" width="3" height="16" rx="1.5" fill="#FF6F61" />
      <rect x="15" y="7" width="10" height="3" rx="1.5" fill="#FF6F61" />
    </g>
  </svg>
);

// Detailed Microphone Icon for CTA
const DetailedMicIcon = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    className="inline-block align-middle mr-3"
    aria-hidden="true"
  >
    <ellipse
      cx="14"
      cy="10"
      rx="6"
      ry="8"
      fill="#fff"
      fillOpacity="0.9"
      stroke="#5f6fff"
      strokeWidth="2"
    />
    <rect x="11" y="18" width="6" height="5" rx="2" fill="#a259ff" />
    <rect
      x="9"
      y="23"
      width="10"
      height="2"
      rx="1"
      fill="#5f6fff"
      fillOpacity="0.7"
    />
    <rect x="13" y="2" width="2" height="4" rx="1" fill="#5f6fff" />
    <rect x="12" y="24" width="4" height="2" rx="1" fill="#FF6F61" />
  </svg>
);

// 3. Subtle Texture Overlay (vinyl/brushed metal)
const TextureOverlay = () => (
  <svg
    className="absolute inset-0 w-full h-full z-0 pointer-events-none"
    style={{ opacity: 0.07 }}
    viewBox="0 0 1440 900"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* Vinyl grooves */}
    <circle
      cx="720"
      cy="450"
      r="400"
      stroke="#fff"
      strokeOpacity="0.12"
      strokeWidth="2"
    />
    <circle
      cx="720"
      cy="450"
      r="300"
      stroke="#fff"
      strokeOpacity="0.10"
      strokeWidth="1.5"
    />
    <circle
      cx="720"
      cy="450"
      r="200"
      stroke="#fff"
      strokeOpacity="0.08"
      strokeWidth="1"
    />
    {/* Speaker mesh dots */}
    {Array.from({ length: 40 }).map((_, i) => (
      <circle
        key={i}
        cx={100 + i * 32}
        cy={860}
        r="2"
        fill="#fff"
        fillOpacity="0.10"
      />
    ))}
  </svg>
);

// 4. Animated Waveform & REC Light
// Symmetrical Animated Waveform Icon
const AnimatedWaveform = () => {
  // Symmetrical heights for a balanced look
  const heights = [10, 18, 26, 32, 26, 18, 10];
  return (
    <svg
      width="112"
      height="36"
      viewBox="0 0 112 36"
      fill="none"
      className="mx-auto"
      style={{ display: "block" }}
    >
      <g>
        {heights.map((h, i) => (
          <rect
            key={i}
            x={i * 16}
            y={36 - h}
            width="10"
            height={h}
            rx="4"
            fill="#5f6fff"
            className="animate-pulse"
            style={{ animationDelay: `${i * 0.08}s` }}
          />
        ))}
      </g>
    </svg>
  );
};

const AnimatedRecLight = () => (
  <div className="flex items-center gap-2 mt-2 mb-2">
    <span className="relative flex h-4 w-4">
      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-[#ff5f7e] opacity-75"></span>
      <span className="relative inline-flex rounded-full h-4 w-4 bg-[#ff5f7e] border-2 border-white/80"></span>
    </span>
    <span className="text-xs text-[#ff5f7e] font-bold tracking-widest">
      REC
    </span>
  </div>
);

// 5. Tech-Inspired Font (Urbanist or similar)
// (Assume font is loaded in Tailwind config or global CSS)

// 6. Section Divider (Headphone Cable/Fader)
const SectionDivider = () => (
  <div className="w-full flex items-center justify-center my-12">
    <svg
      width="320"
      height="24"
      viewBox="0 0 320 24"
      fill="none"
      className="mx-auto"
    >
      {/* Headphone cable */}
      <path
        d="M0 12 Q80 0 160 12 T320 12"
        stroke="#a6a6a6"
        strokeWidth="3"
        fill="none"
      />
      {/* Fader knob */}
      <rect
        x="155"
        y="4"
        width="10"
        height="16"
        rx="5"
        fill="#5f6fff"
        stroke="#fff"
        strokeWidth="2"
      />
    </svg>
  </div>
);

// Trending Beats (updated for accent colors and tactile card style)
const TrendingBeats = () => (
  <div className="w-full max-w-2xl mx-auto mt-10 mb-4">
    <h2 className="text-lg font-bold text-white/80 mb-2 flex items-center gap-2">
      <svg
        width="20"
        height="20"
        fill="none"
        viewBox="0 0 20 20"
        className="text-[#ff5f7e]"
      >
        <rect x="8" y="2" width="4" height="16" rx="2" fill="currentColor" />
        <rect x="2" y="8" width="4" height="10" rx="2" fill="#5f6fff" />
        <rect x="14" y="8" width="4" height="10" rx="2" fill="#a259ff" />
      </svg>
      Trending Beats
    </h2>
    <div className="flex gap-4 overflow-x-auto pb-2">
      {["Supreme Flow", "Hands Up!"].map((beat) => (
        <div
          key={beat}
          className="min-w-[140px] bg-white/10 rounded-2xl px-4 py-3 text-center text-white font-semibold shadow-lg hover:scale-105 transition-transform cursor-pointer border border-white/10 backdrop-blur-md flex flex-col items-center"
        >
          <MusicNoteIcon />
          {beat}
        </div>
      ))}
    </div>
  </div>
);

// --- Main Landing Page ---
const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex flex-col items-center justify-center overflow-hidden bg-gradient-to-br from-[#181C2B] via-[#23294A] to-[#2B1A3A] text-white font-urbanist">
      {/* Studio background overlays and textures */}
      <StudioBackgroundOverlays />
      <TextureOverlay />
      {/* FOMO: Live user counter (bottom right) */}
      <LiveUserCounter />
      {/* Hero Content */}
      <div className="relative z-10 flex flex-col items-center mt-32 mb-8 px-4 text-center">
        <h1 className="text-5xl md:text-7xl font-extrabold mb-10 bg-gradient-to-r from-[#5f6fff] via-[#a259ff] to-[#ff5f7e] bg-clip-text text-transparent drop-shadow-xl font-tech">
          RapStudio Pro
        </h1>
        <h2
          className="text-xl md:text-2xl font-semibold mb-8"
          style={{ color: "#D1D5DB" }}
        >
          No Experience Needed. Showcase Your Talent.
        </h2>
        <p
          className="text-base md:text-lg max-w-2xl mb-8 font-medium drop-shadow"
          style={{ color: "#a6a6a6" }}
        >
          Unlock an exclusive and diverse beat selection, with studio-quality
          tools during your freestyle. Mess around with friends or create
          your next masterpiece. Unleash your flow.
        </p>
        {/* Animated REC light - now centered between text and CTA */}
        <div className="mb-6 flex flex-row items-center justify-center gap-6">
          <AnimatedRecLight />
          <AnimatedWaveform />
        </div>
        {/* Main CTA with animated waveform and glow */}
        <div className="relative flex flex-col items-center">
          <a
            href="/beats"
            className="inline-flex items-center px-10 py-4 rounded-full bg-gradient-to-r from-[#5f6fff] to-[#a259ff] text-white font-bold text-xl shadow-lg hover:scale-105 transition-transform mb-2 ring-2 ring-[#5f6fff]/40 animate-pulse focus:outline-none focus:ring-4 focus:ring-[#a259ff]/60"
          >
            <DetailedMicIcon />
            Start Your Freestyle Now
          </a>
        </div>
      </div>
      {/* FOMO: Trending Beats (now only 2 beats) */}
      <TrendingBeats />
      {/* Section divider (headphone cable/fader) */}
      <SectionDivider />
      {/* Gamification & Retention Hooks (scaffolding) */}
      <div className="w-full max-w-2xl mx-auto mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Gamification */}
        <div className="bg-white/10 rounded-2xl p-5 flex flex-col items-center shadow-lg border border-white/10 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-white/90 flex items-center gap-2">
            {/* Waveform icon */}
            <svg
              width="20"
              height="20"
              fill="none"
              viewBox="0 0 20 20"
              className="text-[#5f6fff]"
            >
              <rect
                x="2"
                y="12"
                width="2"
                height="6"
                rx="1"
                fill="currentColor"
              />
              <rect
                x="6"
                y="8"
                width="2"
                height="10"
                rx="1"
                fill="currentColor"
              />
              <rect
                x="10"
                y="4"
                width="2"
                height="14"
                rx="1"
                fill="currentColor"
              />
              <rect
                x="14"
                y="10"
                width="2"
                height="8"
                rx="1"
                fill="currentColor"
              />
            </svg>
            Your Streak: <span className="text-[#5f6fff]">3 days</span>
          </h3>
          <p className="text-white/70 text-sm mb-2">
            Keep your daily flow alive to unlock rewards!
          </p>
          <div className="flex gap-2">
            <span className="bg-[#5f6fff]/20 px-3 py-1 rounded-full text-xs">
              Flow Score: 87
            </span>
            <span className="bg-[#a259ff]/20 px-3 py-1 rounded-full text-xs">
              Premium Beats: 2/10
            </span>
          </div>
        </div>
        {/* Retention */}
        <div className="bg-white/10 rounded-2xl p-5 flex flex-col items-center shadow-lg border border-white/10 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-white/90 flex items-center gap-2">
            {/* Headphones icon */}
            <svg
              width="20"
              height="20"
              fill="none"
              viewBox="0 0 20 20"
              className="text-[#a259ff]"
            >
              <path
                d="M4 12v-1a6 6 0 0 1 12 0v1"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <rect x="1" y="12" width="4" height="6" rx="2" fill="#5f6fff" />
              <rect x="15" y="12" width="4" height="6" rx="2" fill="#5f6fff" />
            </svg>
            Your Progress
          </h3>
          <p className="text-white/70 text-sm mb-2">
            Track your growth, unlock badges, and build your freestyle
            portfolio.
          </p>
          <div className="flex gap-2">
            <span className="bg-[#ff5f7e]/20 px-3 py-1 rounded-full text-xs">
              Badges: 4
            </span>
            <span className="bg-[#5f6fff]/20 px-3 py-1 rounded-full text-xs">
              Battles Won: 1
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

// --- Main Export ---
export default function LandingPage() {
  return (
    <>
      <HeroSection />
    </>
  );
}
