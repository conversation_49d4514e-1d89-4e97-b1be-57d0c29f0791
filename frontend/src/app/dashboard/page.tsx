"use client";

import React, { useEffect, useState } from "react";
import { WaveformVisualizer } from "../../ui/visualizers";

// Mock auth and API for now since we're focusing on component consolidation
const useAuth = () => ({ user: { premium: true } });
const api = {
  getFreestyleSessions: () => Promise.resolve({ data: [] }),
  getBeats: () => Promise.resolve({ data: [] })
};

export default function DashboardPage() {
  const { user } = useAuth();
  const [sessions, setSessions] = useState<any[]>([]);
  const [beats, setBeats] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user?.premium) return;
    setLoading(true);
    Promise.all([api.getFreestyleSessions(), api.getBeats()])
      .then(([sessionsRes, beatsRes]) => {
        setSessions(sessionsRes.data || []);
        setBeats(beatsRes.data || []);
        setLoading(false);
      })
      .catch(() => {
        setError("Failed to load sessions or beats.");
        setLoading(false);
      });
  }, [user]);

  const getBeatDetails = (beatId: number) => {
    const beat = beats.find((b: any) => b.id === beatId);
    if (!beat)
      return { title: `Beat #${beatId}`, producer: "", bpm: "", key: "" };
    return {
      title: beat.title,
      producer: beat.producer_name,
      bpm: beat.bpm,
      key: beat.key,
    };
  };

  if (!user?.premium) {
    return (
      <div className="max-w-2xl mx-auto mt-16 p-8 bg-white/10 rounded-xl text-center shadow-lg">
        <h2 className="text-2xl font-bold mb-4 text-yellow-300">
          Premium Feature
        </h2>
        <p className="text-lg text-white/80 mb-6">
          Upgrade to Premium to save and access your completed sessions here!
        </p>
        <a
          href="/subscription"
          className="inline-block px-6 py-3 bg-gradient-to-br from-yellow-400 via-purple-500 to-pink-400 text-black font-bold rounded-xl shadow-lg hover:scale-105 transition"
        >
          Upgrade Now
        </a>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full flex flex-col font-urbanist overflow-hidden bg-gradient-to-br from-[#181C2B] via-[#23294A] to-[#2B1A3A] text-white">
      <div className="max-w-3xl mx-auto mt-12 p-4 sm:p-6 bg-white/10 rounded-2xl shadow-2xl backdrop-blur-xl border border-white/10">
        <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold mb-8 text-white bg-gradient-to-r from-[#48CAE4] via-[#B367FF] to-[#FFB6E6] bg-clip-text text-transparent drop-shadow-xl font-tech tracking-tight">
          Your Completed Sessions
        </h1>
        {loading ? (
          <div className="text-white/70">Loading...</div>
        ) : error ? (
          <div className="text-red-400">{error}</div>
        ) : sessions.length === 0 ? (
          <div className="text-white/60">
            No completed sessions yet. Start freestyling!
          </div>
        ) : (
          <ul className="divide-y divide-white/10">
            {sessions.map((session) => {
              const beat = getBeatDetails(Number(session.beat_id));
              return (
                <li
                  key={session.id}
                  className="py-8 flex flex-col md:flex-row md:items-center md:justify-between gap-6 md:gap-2 focus-within:ring-2 focus-within:ring-[#48CAE4] rounded-xl transition-shadow"
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-black text-xl sm:text-2xl text-white mb-1 break-words">
                      {beat.title}
                    </div>
                    <div className="text-base text-gray-200 mb-1 break-words">
                      {beat.producer}
                    </div>
                    <div className="text-sm text-gray-300 mb-2">
                      {beat.bpm} BPM • {beat.key}
                    </div>
                    <div className="text-white/70 text-xs mb-2">
                      Date: {new Date(session.created_at).toLocaleString()}
                    </div>
                    {session.recording_url && (
                      <div className="w-full max-w-xl mt-2">
                        {/* Use WaveformVisualizer with a default duration since we don't know the actual duration */}
                        <WaveformVisualizer
                          audioUrl={session.recording_url}
                          height={40}
                          currentTime={0}
                          duration={180}
                          isPlaying={false}
                          waveformKey={`dashboard-waveform-${session.id}`}
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-row gap-2 sm:gap-4 items-center mt-4 md:mt-0">
                    {session.recording_url && (
                      <a
                        href={session.recording_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-xl font-bold shadow hover:bg-blue-700 transition text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-[#48CAE4]"
                        aria-label={`Play session for beat ${beat.title}`}
                        tabIndex={0}
                      >
                        Play
                      </a>
                    )}
                    {session.recording_url && (
                      <a
                        href={session.recording_url}
                        download
                        className="px-3 sm:px-4 py-2 bg-green-600 text-white rounded-xl font-bold shadow hover:bg-green-700 transition text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-[#48CAE4]"
                        aria-label={`Download session for beat ${beat.title}`}
                        tabIndex={0}
                      >
                        Download
                      </a>
                    )}
                  </div>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </div>
  );
}
