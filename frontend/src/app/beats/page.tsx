"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
// import { api } from "@/lib/api";
import { useBeatStore } from '../../stores';

// --- Studio Background Overlays (unique for beat page) ---
const BeatPageBackground = () => (
  <>
    {/* Blurred studio speakers and faders */}
    <svg
      className="absolute inset-0 w-full h-full z-0 pointer-events-none"
      style={{ opacity: 0.13 }}
      viewBox="0 0 1440 900"
      fill="none"
    >
      {/* Speaker cones */}
      <ellipse
        cx="300"
        cy="800"
        rx="90"
        ry="60"
        fill="#5f6fff"
        fillOpacity="0.18"
      />
      <ellipse
        cx="1200"
        cy="200"
        rx="60"
        ry="90"
        fill="#a259ff"
        fillOpacity="0.13"
      />
      {/* Fader lines */}
      {[...Array(7)].map((_, i) => (
        <rect
          key={i}
          x={200 + i * 140}
          y={600}
          width="8"
          height="120"
          rx="4"
          fill="#fff"
          fillOpacity="0.08"
        />
      ))}
      {/* Meter LEDs */}
      {[...Array(12)].map((_, i) => (
        <circle
          key={i}
          cx={400 + i * 60}
          cy={720 + (i % 2 === 0 ? 0 : 18)}
          r="7"
          fill="#ff5f7e"
          fillOpacity="0.09"
        />
      ))}
    </svg>
    {/* Gradient and spotlight overlays */}
    <div className="absolute inset-0 z-0 pointer-events-none">
      <div className="absolute left-1/2 top-0 -translate-x-1/2 w-[600px] h-[260px] bg-gradient-radial from-[#5f6fff44] to-transparent blur-3xl opacity-60" />
      <div className="absolute right-0 top-1/4 w-[260px] h-[180px] bg-gradient-to-br from-[#a259ff55] to-transparent blur-2xl opacity-50 rotate-12" />
      <div className="absolute left-0 bottom-0 w-[340px] h-[120px] bg-gradient-to-tr from-[#ff5f7e55] to-transparent blur-2xl opacity-40 -rotate-6" />
      {/* Light streak */}
      <div className="absolute left-1/3 top-1/2 w-[340px] h-1 bg-gradient-to-r from-[#fff7] via-[#5f6fff88] to-transparent blur-md opacity-30 rotate-2" />
    </div>
  </>
);

// --- Animated Music Note Icon ---
const AnimatedMusicNote = () => (
  <svg
    width="40"
    height="40"
    viewBox="0 0 40 40"
    fill="none"
    className="mx-auto mb-2 animate-bounce-slow"
    aria-hidden="true"
  >
    <g>
      <ellipse cx="30" cy="32" rx="7" ry="6" fill="#FF6F61" />
      <ellipse cx="14" cy="36" rx="5" ry="4" fill="#FF6F61" fillOpacity="0.7" />
      <rect x="20" y="10" width="4" height="20" rx="2" fill="#FF6F61" />
      <rect x="20" y="10" width="14" height="4" rx="2" fill="#FF6F61" />
    </g>
  </svg>
);

// --- Animated VU Meter Icon ---
const AnimatedVUMeter = () => {
  const heights = [8, 18, 28, 36, 28, 18, 8];
  return (
    <svg
      width="80"
      height="36"
      viewBox="0 0 80 36"
      fill="none"
      className="mx-auto animate-pulse"
      aria-hidden="true"
    >
      <g>
        {heights.map((h, i) => (
          <rect
            key={i}
            x={i * 12}
            y={36 - h}
            width="8"
            height={h}
            rx="3"
            fill={i === 3 ? "#ff5f7e" : "#5f6fff"}
            className="transition-all duration-300"
            style={{ opacity: 0.8 - Math.abs(3 - i) * 0.13 }}
          />
        ))}
      </g>
    </svg>
  );
};

// --- Beat Card ---
const BeatCard = ({
  beat,
  onSelect,
  validIds,
}: {
  beat: any;
  onSelect: () => void;
  validIds: number[];
}) => (
  <div
    className="rounded-2xl border-2 border-white/10 p-8 flex flex-col items-center min-w-[260px] max-w-xs transition hover:scale-105 hover:border-[#5f6fff] bg-white/5 backdrop-blur-md cursor-pointer group shadow-lg relative"
    onClick={() => {
      // Defensive: log the beat object and id
      if (!validIds.includes(Number(beat.id))) {
        // eslint-disable-next-line no-console
        console.error(
          "[ERROR][Beats] Attempted to select invalid beat id:",
          beat.id,
          "Valid ids:",
          validIds,
        );
        return;
      }
      // eslint-disable-next-line no-console
      console.log("[Beats] BeatCard clicked:", beat);
      onSelect();
    }}
    tabIndex={0}
    role="button"
    aria-label={`Select beat: ${beat.title}`}
    data-testid={`beat-card-${beat.id}`}
    data-beat-id={beat.id}
  >
    <AnimatedMusicNote />
    <div
      className="text-2xl font-extrabold text-white mb-1 text-center group-hover:text-[#FF6F61] transition font-tech"
      style={{ textShadow: "0 2px 8px #000" }}
    >
      {beat.title}
    </div>
    <div
      className="text-base text-gray-200 mb-1 text-center group-hover:text-[#5f6fff] transition"
      style={{ textShadow: "0 1px 4px #000" }}
    >
      {beat.producer_name}
    </div>
    <div
      className="text-sm text-gray-300 mb-4 text-center"
      style={{ textShadow: "0 1px 4px #000" }}
    >
      {beat.bpm} BPM • {beat.key}
    </div>
    <AnimatedVUMeter />
    <div
      className="mt-4 text-[#5f6fff] font-bold opacity-90 group-hover:opacity-100 transition text-lg"
      style={{ textShadow: "0 1px 4px #000", letterSpacing: "0.03em" }}
    >
      Start Freestyling &rarr;
    </div>
  </div>
);

// --- Main Beat Selection Page ---
const BeatsPage = () => {
  const router = useRouter();
  const [beats, setBeats] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { selectBeatByObject } = useBeatStore();

  useEffect(() => {
    setLoading(true);
    setError(null);

    // Use the loadBeats function from the store instead of the API
    const { loadBeats } = useBeatStore.getState();
    loadBeats()
      .then(() => {
        // Get the beats from the store
        const storeBeats = useBeatStore.getState().beats;
        setBeats(storeBeats || []);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error loading beats:", error);
        setError("Failed to load beats.");
        setLoading(false);
      });
  }, []);

  return (
    <div className="min-h-screen w-full relative flex flex-col font-urbanist overflow-hidden bg-gradient-to-br from-[#181C2B] via-[#23294A] to-[#2B1A3A] text-white">
      {/* Unique studio background overlays */}
      <BeatPageBackground />
      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-center relative z-20 py-24">
        <h1 className="text-4xl md:text-5xl font-extrabold mb-10 text-center bg-gradient-to-r from-[#5f6fff] via-[#a259ff] to-[#ff5f7e] bg-clip-text text-transparent drop-shadow-xl font-tech tracking-tight">
          Choose Your Beat
        </h1>
        {loading ? (
          <div className="text-center text-lg text-gray-400 py-12">
            Loading beats...
          </div>
        ) : error ? (
          <div className="text-center text-lg text-red-400 py-12">{error}</div>
        ) : (
          <div className="flex flex-wrap justify-center gap-10 w-full max-w-5xl">
            {beats.map((beat: any) => (
              <BeatCard
                key={beat.id}
                beat={beat}
                onSelect={() => {
                  selectBeatByObject(beat);
                  router.push("/session");
                }}
                validIds={beats.map((b: any) => Number(b.id))}
              />
            ))}
          </div>
        )}
        {/* Placeholder for premium/free library logic and future expansion */}
        <div className="mt-12 text-center text-sm text-white/60 max-w-xl mx-auto">
          <span className="block mb-1">
            More beats coming soon for premium users.
          </span>
          <span className="block">
            Upgrade to unlock the full library and exclusive features.
          </span>
        </div>
      </main>
    </div>
  );
};

export default BeatsPage;
