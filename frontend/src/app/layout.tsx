"use client";

// Removed unused Metadata import
import "./globals.css";
import { useEffect } from "react";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Service worker registration removed

  useEffect(() => {
    const handler = (event: PromiseRejectionEvent) => {
      if (
        event.reason &&
        event.reason.name === "AbortError" &&
        /signal is aborted without reason/i.test(event.reason.message)
      ) {
        event.preventDefault();
      }
    };
    window.addEventListener("unhandledrejection", handler);
    return () => window.removeEventListener("unhandledrejection", handler);
  }, []);

  return (
    <html lang="en" className="font-urbanist">
      <body>
        {children}
      </body>
    </html>
  );
}
