"use client";

import "./globals.css";
import { useEffect } from "react";
import { ErrorBoundary } from "../ui/common/ErrorBoundary";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Service worker registration removed

  useEffect(() => {
    const handler = (event: PromiseRejectionEvent) => {
      if (
        event.reason &&
        event.reason.name === "AbortError" &&
        /signal is aborted without reason/i.test(event.reason.message)
      ) {
        event.preventDefault();
      }
    };
    window.addEventListener("unhandledrejection", handler);
    return () => window.removeEventListener("unhandledrejection", handler);
  }, []);

  return (
    <html lang="en" className="font-urbanist">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#0D1725" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="antialiased">
        <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
