"use client";
/**
 * Freestyle Session Page
 *
 * This is the main page for the freestyle rap recording experience.
 * It serves as the container for all recording components and manages
 * the global state through various stores.
 *
 * Key Features:
 * - Microphone recording with visual feedback
 * - Beat selection and playback
 * - Waveform visualization
 * - Technical controls in collapsible panel
 *
 * Component Hierarchy:
 * FreestyleSessionPage
 * ├── StudioBackground (visual backdrop)
 * ├── RecordingSystem (main controller)
 * │   ├── MicVinylIntegrated (visual interface)
 * │   └── FreestyleRecorder (audio processing)
 * └── CollapsiblePanel (technical controls)
 *     ├── AudioControlsTab (mic and beat controls)
 *     └── BeatLibraryTab (beat selection)
 */
import React, { useState, useEffect, useRef, useCallback } from "react";
import { RecordingSystem } from "../../audio/components/RecordingSystem";
import { StudioBackground } from '../../ui/layout/StudioBackground';
import { CollapsiblePanel } from '../../ui/layout/CollapsiblePanel';
import { ErrorBoundary } from '../../ui/common/ErrorBoundary';
import { FeedbackProvider } from '../../ui/common/UserFeedback';
import { useBeatStore } from '../../stores/beatStore';
import { useAudioStore } from '../../stores/audioStore';
import { useRecordingStateMachine } from '../../audio/services/RecordingStateMachine';
import { audioContextManager } from '../../audio/services/AudioContextManager';
import { performanceMonitor } from '../../utils/performance';
import { AudioRetry, RetryConditions } from '../../utils/retryMechanism';
import '../../styles/hover-animations.css';

export default function FreestyleSessionPage() {
  // State for UI
  const [loadingBeats, setLoadingBeats] = useState(true); // Loading state for beats
  const transportRef = useRef<any>(null);
  const [showFadeIn, setShowFadeIn] = useState(true);

  // Prevent flickering by ensuring all components are ready before showing UI
  useEffect(() => {
    // Force a repaint to ensure all elements are rendered correctly
    const forceRepaint = () => {
      if (typeof document !== 'undefined') {
        const html = document.documentElement;
        html.style.display = 'none';
        // This forces a repaint
        void html.offsetHeight;
        html.style.display = '';
      }
    };

    // Execute after a very short delay to ensure DOM is ready
    const repaintTimer = setTimeout(forceRepaint, 10);

    return () => clearTimeout(repaintTimer);
  }, []);

  // Zustand stores
  const beatsFromStore = useBeatStore(s => s.beats);
  const loadBeats = useBeatStore(s => s.loadBeats);
  const currentBeat = useBeatStore(s => s.currentBeat);
  const selectBeatByObject = useBeatStore(s => s.selectBeatByObject);
  const recordingState = useAudioStore(s => s.recordingState);

  // Preview mode is now handled by the preview page

  // Mic controls state
  const [availableMics, setAvailableMics] = useState<MediaDeviceInfo[]>([]);
  const [selectedMic, setSelectedMic] = useState<MediaDeviceInfo | null>(null);
  const [micGain, setMicGain] = useState<number>(1.0); // Default to 1.0 (100%)
  const [beatVolume, setBeatVolume] = useState<number>(1.0); // Default to 1.0 (100%)

  // Load saved values from localStorage (client-side only)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedMicGain = localStorage.getItem('micGain');
        if (savedMicGain) {
          setMicGain(parseFloat(savedMicGain));
        }

        const savedBeatVolume = localStorage.getItem('beatVolume');
        if (savedBeatVolume) {
          setBeatVolume(parseFloat(savedBeatVolume));
        }
      } catch (e) {
        console.warn("Error loading volume settings from localStorage:", e);
      }
    }
  }, []);
  const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [beatDuration, setBeatDuration] = useState(0);
  // Get beatAudioBuffer from beatStore instead of local state
  const beatAudioBuffer = useBeatStore(state => state.beatAudioBuffer);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [previewAudio, setPreviewAudio] = useState<HTMLAudioElement | null>(null);

  // These states are used for device selection in the CollapsiblePanel
  // We keep them for backward compatibility
  const [_inputOptions, setInputOptions] = useState<{ value: string; label: string }[]>([
    { value: 'default', label: 'Default Mic' }
  ]);

  const [_selectedInput, setSelectedInput] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("selectedInputDevice") || "default";
    }
    return "default";
  });

  // Load available input devices with proper cleanup
  useEffect(() => {
    let mounted = true;
    let currentStream: MediaStream | null = null;

    // Get devices and set input options with proper cleanup
    const getDevices = async () => {
      if (!navigator.mediaDevices?.enumerateDevices || !mounted) return;

      try {
        // First set up the UI with default options
        if (mounted) {
          setInputOptions([{ value: "default", label: "Default Mic" }]);
        }

        // Request permissions and get device list with retry mechanism
        try {
          currentStream = await AudioRetry.getMicrophone();

          if (!mounted) {
            // Component unmounted, cleanup stream
            currentStream.getTracks().forEach(track => track.stop());
            return;
          }

          // Get device list after permission granted
          const devices = await navigator.mediaDevices.enumerateDevices();
          const audioInputs = devices.filter((d) => d.kind === "audioinput");

          if (mounted) {
            // Set available mics for the new panel
            setAvailableMics(audioInputs);

            // If no mic is selected yet, select the first one
            if (audioInputs.length > 0 && !selectedMic) {
              setSelectedMic(audioInputs[0]);
            }

            // For backward compatibility
            const options = [
              { value: "default", label: "Default Mic" },
              ...audioInputs.map((d) => ({
                value: d.deviceId,
                label: d.label || `Mic ${d.deviceId.slice(-4)}`,
              })),
            ];
            setInputOptions(options);
          }

          // Clean up the stream immediately after getting device list
          currentStream.getTracks().forEach(track => track.stop());
          currentStream = null;

        } catch (err) {
          console.error("Error accessing media devices:", err);
          if (currentStream) {
            currentStream.getTracks().forEach(track => track.stop());
            currentStream = null;
          }
        }
      } catch (err) {
        console.error("Error setting up media devices:", err);
      }
    };

    // Device change handler with debouncing
    let deviceChangeTimeout: NodeJS.Timeout;
    const handleDeviceChange = () => {
      clearTimeout(deviceChangeTimeout);
      deviceChangeTimeout = setTimeout(() => {
        if (mounted) {
          getDevices();
        }
      }, 500); // Debounce device changes
    };

    // Start the device detection process
    getDevices();

    // Listen for device changes
    if (navigator.mediaDevices?.addEventListener) {
      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    }

    return () => {
      mounted = false;

      // Cleanup timeout
      clearTimeout(deviceChangeTimeout);

      // Cleanup stream if still active
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
      }

      // Remove event listener
      if (navigator.mediaDevices?.removeEventListener) {
        navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
      }
    };
  }, []); // Remove selectedMic dependency to prevent multiple concurrent calls

  // Handle beat preview
  const handleBeatPreview = (beat: any) => {
    // Stop current preview if playing
    if (previewAudio) {
      previewAudio.pause();
      previewAudio.currentTime = 0;
    }

    // Get the beat URL
    const beatUrl = beat.attributes?.audio_url || beat.audio_url;
    if (!beatUrl) {
      console.error('No audio URL found for beat:', beat);
      return;
    }

    // Create new audio element for preview
    const audio = new Audio(beatUrl);

    // Use the current beatVolume state (0-2 range, where 1.0 is 100%)
    audio.volume = beatVolume;
    console.log(`Setting preview audio volume to ${beatVolume} (${Math.round(beatVolume * 100)}%)`);

    // Set up event listeners
    audio.addEventListener('ended', () => {
      setIsPreviewPlaying(false);
    });

    // Play the preview
    audio.play().then(() => {
      setIsPreviewPlaying(true);
      setPreviewAudio(audio);
    }).catch(error => {
      console.error('Error playing beat preview:', error);
    });
  };

  // Stop beat preview
  const stopBeatPreview = () => {
    if (previewAudio) {
      previewAudio.pause();
      previewAudio.currentTime = 0;
      setIsPreviewPlaying(false);
    }
  };

  // Update currentTime and beatDuration for the waveform
  useEffect(() => {
    if (transportRef.current) {
      const updateTime = () => {
        if (transportRef.current) {
          setCurrentTime(transportRef.current.getCurrentTime() || 0);
          setBeatDuration(transportRef.current.getDuration() || 0);
        }
      };

      const interval = setInterval(updateTime, 100);
      return () => clearInterval(interval);
    }
  }, [transportRef.current]);

  // Handle input device selection changes
  const handleInputChange = (deviceId: string) => {
    setSelectedInput(deviceId);
    if (typeof window !== "undefined") {
      localStorage.setItem("selectedInputDevice", deviceId);
    }
    // We no longer need to force remount the entire system
    // The FreestyleRecorder will handle input changes internally
  };

  // No AI functionality in this version

  useEffect(() => {
    // Load beats into Zustand store - only once
    const loadBeatsData = async () => {
      try {
        setLoadingBeats(true);
        await loadBeats();
      } catch (error) {
        console.error("Error loading beats:", error);
      } finally {
        // Set loading to false after beats are loaded
        setLoadingBeats(false);
      }
    };

    loadBeatsData();
  }, [loadBeats]); // Only depends on loadBeats function

  // Improved fade-in animation logic - no delay to prevent flickering
  useEffect(() => {
    if (!loadingBeats && currentBeat) {
      // Set showFadeIn to false immediately to prevent flickering
      setShowFadeIn(false);
    }
  }, [loadingBeats, currentBeat]);

  // Preview mode is now handled by the preview page

  // Beat selection handler with proper async handling
  const handleBeatSelect = useCallback(async (newBeat: any) => {
    console.log('Beat selection started:', newBeat.title);

    try {
      // Check if we can safely change beats
      if (recordingState === 'recording' || recordingState === 'stopping') {
        console.warn('Cannot change beats while recording is active');
        return;
      }

      // Stop any currently playing beat before switching (with await)
      if (transportRef.current && typeof transportRef.current.stop === "function") {
        await transportRef.current.stop();
      }

      // Wait a moment for any ongoing operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Select the new beat in the store
      selectBeatByObject(newBeat);

      // Only remount if absolutely necessary (avoid nuclear approach)
      // Instead of force remounting, let components handle beat changes gracefully
      console.log('Beat selected successfully:', newBeat.title);

      // Record performance metric
      performanceMonitor.recordMetric({
        name: 'beat_selection_success',
        value: 1,
        timestamp: Date.now(),
        type: 'audio',
        metadata: { beatTitle: newBeat.title }
      });

    } catch (error) {
      console.error('Error selecting beat:', error);

      performanceMonitor.recordMetric({
        name: 'beat_selection_error',
        value: 1,
        timestamp: Date.now(),
        type: 'audio',
        metadata: { error: error instanceof Error ? error.message : String(error) }
      });
    }
  }, [recordingState, selectBeatByObject, transportRef]);

  // Note: Beat playback is now handled directly by the FreestyleRecorder component
  // through the transportRef, so we don't need separate helper functions here

  // Helper to get the current selected beat's audio URL
  const selectedBeatUrl = currentBeat
    ? currentBeat.attributes?.audio_url || currentBeat.audio_url
    : null;

  // CRITICAL FIX: Store the beat URL in window for redundancy
  useEffect(() => {
    if (selectedBeatUrl) {
      console.log("CRITICAL: Storing beat URL in window:", selectedBeatUrl);
      (window as any).__currentBeatUrl = selectedBeatUrl;
      (window as any).__sessionPageBeatUrl = selectedBeatUrl;

      // Also store in localStorage for persistence
      try {
        localStorage.setItem('currentBeatUrl', selectedBeatUrl);
      } catch (e) {
        console.warn("Error storing beat URL in localStorage:", e);
      }
    }
  }, [selectedBeatUrl]);

  // Save mic gain to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('micGain', micGain.toString());
        console.log(`Saved mic gain to localStorage: ${micGain} (${Math.round(micGain * 100)}%)`);

        // Also update the gainPercent in the audio store for recording
        useAudioStore.getState().setGainPercent(micGain * 100);
      } catch (e) {
        console.warn("Error storing mic gain in localStorage:", e);
      }
    }
  }, [micGain]);

  // Save beat volume to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('beatVolume', beatVolume.toString());
        console.log(`Saved beat volume to localStorage: ${beatVolume} (${Math.round(beatVolume * 100)}%)`);

        // If there's a preview audio playing, update its volume
        if (previewAudio) {
          previewAudio.volume = beatVolume;
        }

        // Also update any active beat audio elements
        const beatAudioElements = document.querySelectorAll('audio');
        beatAudioElements.forEach(audio => {
          if (audio.src.includes('/beats/')) {
            audio.volume = beatVolume;
          }
        });
      } catch (e) {
        console.warn("Error storing beat volume in localStorage:", e);
      }
    }
  }, [beatVolume, previewAudio]);

  return (
    <FeedbackProvider>
      <main
        className="min-h-screen w-full text-white p-0 m-0 relative overflow-hidden"
        style={{
          background: "#0F1A30", // Solid deep midnight background
        }}
      >
        {/* Clean minimal background */}
        <StudioBackground className={showFadeIn ? "fade-in-background" : undefined} />

        {/* Collapsible Panel */}
        <CollapsiblePanel
          isCollapsed={isPanelCollapsed}
          toggleCollapse={() => setIsPanelCollapsed(!isPanelCollapsed)}
          // Audio controls props
          currentBeat={currentBeat}
          beatDuration={beatDuration}
          currentTime={currentTime}
          beatVolume={beatVolume}
          setBeatVolume={setBeatVolume}
          micGain={micGain}
          setMicGain={setMicGain}
          availableMics={availableMics}
          selectedMic={selectedMic}
          setSelectedMic={(mic) => {
            setSelectedMic(mic);
            // For backward compatibility
            setSelectedInput(mic.deviceId);
            handleInputChange(mic.deviceId);
          }}
          beatAudioBuffer={beatAudioBuffer}
          // Beat library props
          beats={beatsFromStore}
          onBeatSelect={handleBeatSelect}
          onBeatPreview={handleBeatPreview}
          isPreviewPlaying={isPreviewPlaying}
          stopPreview={stopBeatPreview}
        />

        {/* Main content container - only show when UI is ready */}
        <div
          className="w-full"
          style={{
            width: "100vw",
            minHeight: "100vh",
            padding: 0,
            margin: 0,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            opacity: 1, // Always show immediately
            transition: "none", // No transition to prevent flickering
          }}
        >
          {/* Session content with fade-in animation */}
          {currentBeat && beatsFromStore && beatsFromStore.length > 0 ? (
            <div
              className={showFadeIn ? "fade-in-container" : undefined}
              style={{
                width: "100%",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                marginTop: "5vh", // Further reduced to move component higher up
                marginBottom: "3vh",
              }}
            >
              {/* Main recording interface */}

              {/* Recording system with microphone */}
              <div
                className="hover-transition"
                style={{
                  marginTop: '40px', // Increased margin to move down for instructional text
                  width: '100%', // Ensure full width
                  display: 'flex',
                  justifyContent: 'center' // Center the recording system
                }}
              >
                <ErrorBoundary
                  onError={(error, errorInfo) => {
                    console.error('RecordingSystem error:', error, errorInfo);
                    performanceMonitor.recordMetric({
                      name: 'recording_system_error',
                      value: 1,
                      timestamp: Date.now(),
                      type: 'audio',
                      metadata: { error: error.message }
                    });
                  }}
                >
                  <RecordingSystem
                    onRecordingComplete={(recording: any) => {
                      console.log("Recording complete:", recording);
                      performanceMonitor.recordMetric({
                        name: 'recording_completed',
                        value: 1,
                        timestamp: Date.now(),
                        type: 'audio',
                        metadata: { duration: recording?.duration || 0 }
                      });
                    }}
                  />
                </ErrorBoundary>
              </div>

              {/* Waveform container removed - now part of FreestyleRecorder */}
            </div>
          ) : loadingBeats ? (
            <div
              className="loading-state"
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              <div className="loading-pulse">
                <div style={{
                  width: 40,
                  height: 40,
                  borderRadius: "50%",
                  background: "#5f6fff",
                  animation: "loadingPulse 1.5s infinite ease-in-out",
                  boxShadow: "0 0 20px #5f6fff88"
                }} />
              </div>
            </div>
          ) : (
            <div
              className="error-state"
              style={{
                color: "#ff5f7e",
                fontWeight: 600,
                marginTop: "40vh",
                padding: "16px 24px",
                background: "rgba(255, 95, 126, 0.1)",
                borderRadius: 12,
                boxShadow: "0 4px 12px rgba(0,0,0,0.2)"
              }}
            >
              No beat audio file found for this session.
            </div>
          )}
        </div>

        {/* Global styles moved to globals.css */}
      </main>
    </FeedbackProvider>
  );
}
