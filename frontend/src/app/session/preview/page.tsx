"use client";
/**
 * Preview Page
 *
 * This page is dedicated to playback of recorded freestyles.
 * It provides a clean, focused interface for:
 * - Playing back the recorded freestyle with the beat
 * - Visualizing the audio with a waveform
 * - Downloading the recording
 * - Recording again (returning to session page)
 *
 * The page receives recording data via URL parameters and/or localStorage.
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { PreviewPlayer } from '../../../audio/components/PreviewPlayer';
import { StudioBackground } from '../../../ui/layout/StudioBackground';
import { usePreviewStore } from '../../../stores/previewStore';
import { useAudioStore } from '../../../stores/audioStore';
// import { useBeatStore } from '../../../stores/beatStore'; // Not currently used

export default function PreviewPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get recording data from stores
  const { mixedBlob, /* mixedBuffer, */ recordingBlob, mixedUrl, recordingUrl } = useAudioStore();
  // const { currentBeat } = useBeatStore(); // Not currently used

  // Initialize preview data
  useEffect(() => {
    const initializePreview = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Log all available data sources
        console.log("Initializing preview with store data:", {
          mixedBlob: mixedBlob ? `${mixedBlob.size} bytes` : 'missing',
          recordingBlob: recordingBlob ? `${recordingBlob.size} bytes` : 'missing',
          mixedUrl: mixedUrl || 'missing',
          recordingUrl: recordingUrl || 'missing'
        });

        // Check window for redundant storage
        console.log("Window storage:", {
          hasMixedRecordingBuffer: !!(window as any).__mixedRecordingBuffer,
          hasMixedRecordingUrl: (window as any).__mixedRecordingUrl || 'missing',
          hasCurrentRecordingUrl: (window as any).__currentRecordingUrl || 'missing',
          hasCurrentRecordingData: !!(window as any).__currentRecordingData
        });

        // Check localStorage directly
        const storedData = localStorage.getItem('recordingData');
        console.log("localStorage data available:", !!storedData);

        // Check if we have recording data
        if (!mixedBlob && !recordingBlob && !mixedUrl && !recordingUrl) {
          console.log("No recording data in store, checking window storage first");

          // Try to get recording data from window storage first
          if ((window as any).__mixedRecordingUrl) {
            console.log("Found recording URL in window storage:", (window as any).__mixedRecordingUrl);
            try {
              const response = await fetch((window as any).__mixedRecordingUrl);
              if (!response.ok) {
                throw new Error(`Failed to fetch blob from window storage: ${response.status}`);
              }
              const blob = await response.blob();
              console.log("Successfully fetched blob from window storage:", blob.size, "bytes");

              // Update the store with the blob
              useAudioStore.setState({
                mixedBlob: blob,
                recordingBlob: blob,
                mixedUrl: (window as any).__mixedRecordingUrl,
                recordingUrl: (window as any).__currentRecordingUrl || (window as any).__mixedRecordingUrl
              });

              console.log("Updated store with blob from window storage");
            } catch (windowError) {
              console.error("Error fetching from window storage:", windowError);
              console.log("Falling back to localStorage");
            }
          } else {
            console.log("No recording data in window storage, checking localStorage");
          }

          // If we still don't have data, try localStorage
          if (!useAudioStore.getState().mixedBlob) {
            console.log("Checking localStorage for recording data");
            // Try to get recording data from localStorage
            const storedRecordingData = localStorage.getItem('recordingData');

            if (storedRecordingData) {
              try {
                const parsedData = JSON.parse(storedRecordingData);
                console.log("Found stored recording data:", parsedData);

                // Check if we have blobs stored in window as backup
                if ((window as any).__mixedBlob || (window as any).__recordingBlob) {
                  console.log("Found blob in window storage");
                  const blob = (window as any).__mixedBlob || (window as any).__recordingBlob;

                  // Create a new object URL from the blob
                  const blobUrl = URL.createObjectURL(blob);
                  console.log("Created new blob URL from window storage:", blobUrl);

                  // Update the store with the blob
                  useAudioStore.setState({
                    mixedBlob: blob,
                    recordingBlob: blob,
                    mixedUrl: blobUrl,
                    recordingUrl: blobUrl
                  });

                  console.log("Updated store with blob from window storage");
                } else {
                  // Try to use mixedUrl first, then fall back to recordingUrl if available
                  const urlToFetch = parsedData.mixedUrl || parsedData.recordingUrl ||
                                    (window as any).__mixedRecordingUrl || (window as any).__currentRecordingUrl;

                  if (urlToFetch) {
                    console.log("Fetching blob from URL:", urlToFetch);
                    try {
                      const response = await fetch(urlToFetch);
                      if (!response.ok) {
                        throw new Error(`Failed to fetch blob: ${response.status} ${response.statusText}`);
                      }
                      const blob = await response.blob();
                      console.log("Successfully fetched blob:", blob.size, "bytes");

                      // Create a new object URL from the blob
                      const blobUrl = URL.createObjectURL(blob);
                      console.log("Created new blob URL:", blobUrl);

                      // Update the store with the blob
                      useAudioStore.setState({
                        mixedBlob: blob,
                        recordingBlob: blob,
                        mixedUrl: blobUrl,
                        recordingUrl: blobUrl
                      });

                      console.log("Updated store with new blob and URL");
                    } catch (fetchError) {
                      console.error("Error fetching blob:", fetchError);

                      // Try to use the beat as fallback
                      if (parsedData.beatUrl) {
                        console.log("Falling back to beat URL after fetch error");
                        throw new Error("Falling back to beat");
                      } else {
                        setError("Error loading recording. Please record a freestyle first.");
                        router.push('/session');
                        return;
                      }
                    }
                  }
                }

                if (!useAudioStore.getState().mixedBlob && parsedData.beatUrl) {
                  // If we have a beat URL but no recording URL, we can try to create a dummy recording
                  console.log("No recording URL available, but found beat URL:", parsedData.beatUrl);
                  try {
                    // Fetch the beat
                    const response = await fetch(parsedData.beatUrl);
                    if (!response.ok) {
                      throw new Error(`Failed to fetch beat: ${response.status} ${response.statusText}`);
                    }
                    const blob = await response.blob();
                    console.log("Successfully fetched beat blob:", blob.size, "bytes");

                    // Create a new object URL from the blob
                    const blobUrl = URL.createObjectURL(blob);
                    console.log("Created new blob URL for beat:", blobUrl);

                    // Update the store with the beat as a fallback
                    useAudioStore.setState({
                      mixedBlob: blob,
                      recordingBlob: null,
                      mixedUrl: blobUrl,
                      recordingUrl: null
                    });

                    console.log("Updated store with beat as fallback");
                  } catch (fetchError) {
                    console.error("Error fetching beat:", fetchError);
                    setError("Error loading recording. Please record a freestyle first.");
                    router.push('/session');
                    return;
                  }
                } else {
                  // No recording or beat data available
                  console.error("No mixedUrl or recordingUrl in stored data");
                  setError("No recording found. Please record a freestyle first.");
                  router.push('/session');
                  return;
                }
            } catch (e) {
              console.error("Error parsing stored recording data:", e);
              setError("Error loading recording. Please record a freestyle first.");
              router.push('/session');
              return;
            }
          } else {
            // No recording data available
            console.error("No recording data in localStorage");
            setError("No recording found. Please record a freestyle first.");
            router.push('/session');
            return;
          }
        }

        // Set preview mode in the store
        usePreviewStore.setState({
          isPreviewMode: true,
          autoPlay: false // Don't auto-play initially
        });

        console.log("Preview initialized successfully");

      } catch (e) {
        console.error("Error initializing preview:", e);
        setError("Error initializing preview. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    initializePreview();

    // Clean up on unmount
    return () => {
      // Reset preview mode when leaving the page
      usePreviewStore.setState({ isPreviewMode: false });
    };
  }, [mixedBlob, recordingBlob, router]);

  // Handle recording again
  const handleRecordAgain = () => {
    // Navigate back to session page
    router.push('/session');
  };

  return (
    <main className="min-h-screen w-full text-white p-0 m-0 relative overflow-hidden">
      <StudioBackground />

      <div className="relative z-10 w-full h-full flex flex-col items-center justify-center">
        <h1 className="text-2xl font-bold mb-8 text-center">Freestyle Preview</h1>

        {isLoading ? (
          <div className="loading-pulse">
            <div style={{
              width: 40,
              height: 40,
              borderRadius: "50%",
              background: "#5f6fff",
              animation: "loadingPulse 1.5s infinite ease-in-out",
              boxShadow: "0 0 20px #5f6fff88"
            }} />
          </div>
        ) : error ? (
          <div className="error-message p-4 bg-red-900/30 rounded-lg">
            {error}
          </div>
        ) : (
          <PreviewPlayer
            onRecordAgain={handleRecordAgain}
          />
        )}

        <div className="mt-8 flex gap-4">
          <button
            onClick={handleRecordAgain}
            className="px-6 py-2 bg-transparent border border-white/50 rounded-full hover:bg-white/10 transition-colors"
          >
            Record Again
          </button>
        </div>
      </div>
    </main>
  );
}
