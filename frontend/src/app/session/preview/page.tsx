"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { PreviewPlayer } from "../../../audio/components/PreviewPlayer";
import { StudioBackground } from "../../../ui/layout/StudioBackground";
import { usePreviewStore } from "../../../stores/previewStore";
import { useAudioStore } from "../../../stores/audioStore";

export default function PreviewPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { mixedBlob, recordingBlob, mixedUrl, recordingUrl } = useAudioStore();

  useEffect(() => {
    const initializePreview = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!mixedBlob && !recordingBlob && !mixedUrl && !recordingUrl) {
          const storedRecordingData = localStorage.getItem("recordingData");

          if (storedRecordingData) {
            try {
              const parsedData = JSON.parse(storedRecordingData);
              const urlToFetch = parsedData.mixedUrl || parsedData.recordingUrl;

              if (urlToFetch) {
                const response = await fetch(urlToFetch);
                if (!response.ok) {
                  throw new Error(`Failed to fetch blob: ${response.status}`);
                }
                const blob = await response.blob();
                const blobUrl = URL.createObjectURL(blob);

                useAudioStore.setState({
                  mixedBlob: blob,
                  recordingBlob: blob,
                  mixedUrl: blobUrl,
                  recordingUrl: blobUrl
                });
              } else {
                setError("No recording found. Please record a freestyle first.");
                router.push("/session");
                return;
              }
            } catch (e) {
              setError("Error loading recording. Please record a freestyle first.");
              router.push("/session");
              return;
            }
          } else {
            setError("No recording found. Please record a freestyle first.");
            router.push("/session");
            return;
          }
        }

        usePreviewStore.setState({
          isPreviewMode: true,
          autoPlay: false
        });

      } catch (e) {
        console.error("Error initializing preview:", e);
        setError("Error initializing preview. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    initializePreview();

    return () => {
      usePreviewStore.setState({ isPreviewMode: false });
    };
  }, [mixedBlob, recordingBlob, router]);

  const handleRecordAgain = () => {
    router.push("/session");
  };

  return (
    <main className="min-h-screen w-full text-white p-0 m-0 relative overflow-hidden">
      <StudioBackground />
      <div className="relative z-10 w-full h-full flex flex-col items-center justify-center">
        <h1 className="text-2xl font-bold mb-8 text-center">Freestyle Preview</h1>
        {isLoading ? (
          <div className="loading-pulse">
            <div style={{
              width: 40,
              height: 40,
              borderRadius: "50%",
              background: "#5f6fff",
              animation: "loadingPulse 1.5s infinite ease-in-out",
              boxShadow: "0 0 20px #5f6fff88"
            }} />
          </div>
        ) : error ? (
          <div className="error-message p-4 bg-red-900/30 rounded-lg">
            {error}
          </div>
        ) : (
          <PreviewPlayer onRecordAgain={handleRecordAgain} />
        )}
        <div className="mt-8 flex gap-4">
          <button
            onClick={handleRecordAgain}
            className="px-6 py-2 bg-transparent border border-white/50 rounded-full hover:bg-white/10 transition-colors"
          >
            Record Again
          </button>
        </div>
      </div>
    </main>
  );
}
