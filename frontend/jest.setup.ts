import "@testing-library/jest-dom";
import "whatwg-fetch";
import "jest-canvas-mock";

// Mock the Web Audio API
class AudioContextMock {
  createAnalyser() {
    return {
      connect: jest.fn(),
      frequencyBinCount: 1024,
      getByteFrequencyData: jest.fn(),
      fftSize: 2048,
    };
  }

  createMediaStreamSource() {
    return {
      connect: jest.fn(),
    };
  }

  close() {
    return Promise.resolve();
  }
}

class MediaRecorderMock {
  start() {}
  stop() {}
  ondataavailable() {}
  onstop() {}
  static isTypeSupported() {
    return true;
  }
}

// Mock navigator.mediaDevices
Object.defineProperty(window, "AudioContext", {
  writable: true,
  value: AudioContextMock,
});

Object.defineProperty(window, "MediaRecorder", {
  writable: true,
  value: MediaRecorderMock,
});

Object.defineProperty(navigator, "mediaDevices", {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockImplementation(() =>
      Promise.resolve({
        getTracks: () => [
          {
            stop: jest.fn(),
          },
        ],
      }),
    ),
  },
});

// Mock SpeechGrammarList
const mockSpeechGrammarList: SpeechGrammarList = {
  length: 0,
  item: jest.fn(),
  addFromString: jest.fn(),
  addFromURI: jest.fn(),
};

// Mock Web Speech API
const mockSpeechRecognition: SpeechRecognition = {
  start: jest.fn(),
  stop: jest.fn(),
  abort: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
  continuous: true,
  interimResults: true,
  lang: "en-US",
  maxAlternatives: 1,
  grammars: mockSpeechGrammarList,
  onaudioend: null,
  onaudiostart: null,
  onend: null,
  onerror: null,
  onnomatch: null,
  onresult: null,
  onsoundend: null,
  onsoundstart: null,
  onspeechend: null,
  onspeechstart: null,
  onstart: null,
};

// Properly type the global mock
(
  global as unknown as { webkitSpeechRecognition: unknown }
).webkitSpeechRecognition = jest
  .fn()
  .mockImplementation(() => mockSpeechRecognition);
