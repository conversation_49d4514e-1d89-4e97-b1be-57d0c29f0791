import http from "k6/http";
import { check, sleep } from "k6";
import { Rate } from "k6/metrics";

// Custom metrics
const errorRate = new Rate("errors");
const suggestionLatency = new Rate("suggestion_latency");

// Test configuration
export const options = {
  stages: [
    { duration: "1m", target: 10 }, // Ramp up to 10 users
    { duration: "3m", target: 10 }, // Stay at 10 users
    { duration: "1m", target: 20 }, // Ramp up to 20 users
    { duration: "3m", target: 20 }, // Stay at 20 users
    { duration: "1m", target: 0 }, // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ["p(95)<500"], // 95% of requests should be below 500ms
    errors: ["rate<0.1"], // Error rate should be below 10%
    suggestion_latency: ["p(95)<1000"], // 95% of AI suggestions should be below 1s
  },
};

// Simulate recording session
export default function () {
  // Login
  const loginRes = http.post("http://localhost:3001/api/auth/login", {
    email: "<EMAIL>",
    password: "password",
  });

  check(loginRes, {
    "logged in successfully": (r) => r.status === 200,
  }) || errorRate.add(1);

  const token = loginRes.json("token");

  // Get available beats
  const beatsRes = http.get("http://localhost:3001/api/beats", {
    headers: { Authorization: `Bearer ${token}` },
  });

  check(beatsRes, {
    "got beats successfully": (r) => r.status === 200,
    "has beats": (r) => r.json("data").length > 0,
  }) || errorRate.add(1);

  const beats = beatsRes.json("data");
  const beatId = beats[0].id;

  // Create freestyle session
  const sessionRes = http.post("http://localhost:3001/api/freestyle_sessions", {
    headers: { Authorization: `Bearer ${token}` },
    json: { beat_id: beatId },
  });

  check(sessionRes, {
    "created session successfully": (r) => r.status === 200,
  }) || errorRate.add(1);

  const sessionId = sessionRes.json("data").id;

  // Simulate recording with AI suggestions
  for (let i = 0; i < 5; i++) {
    const start = new Date();

    const suggestionsRes = http.get(
      `http://localhost:3001/api/freestyle_sessions/${sessionId}/ai_suggestions?start_time=${i * 30}&end_time=${(i + 1) * 30}`,
      { headers: { Authorization: `Bearer ${token}` } },
    );

    suggestionLatency.add(new Date() - start);

    check(suggestionsRes, {
      "got suggestions successfully": (r) => r.status === 200,
      "has suggestions": (r) => r.json("data").length > 0,
    }) || errorRate.add(1);

    // Simulate using a suggestion
    if (suggestionsRes.json("data").length > 0) {
      const suggestionId = suggestionsRes.json("data")[0].id;

      const updateRes = http.put(
        `http://localhost:3001/api/freestyle_sessions/${sessionId}/ai_suggestions/${suggestionId}`,
        { json: { used: true } },
        { headers: { Authorization: `Bearer ${token}` } },
      );

      check(updateRes, {
        "updated suggestion successfully": (r) => r.status === 200,
      }) || errorRate.add(1);
    }

    sleep(5); // Wait 5 seconds between chunks
  }

  // End session
  const endRes = http.put(
    `http://localhost:3001/api/freestyle_sessions/${sessionId}`,
    { json: { status: "completed" } },
    { headers: { Authorization: `Bearer ${token}` } },
  );

  check(endRes, {
    "ended session successfully": (r) => r.status === 200,
  }) || errorRate.add(1);
}

// Test audio processing performance
export function handleSummary(data) {
  return {
    "performance.html": generateReport(data),
    stdout: textSummary(data, { indent: " ", enableColors: true }),
  };
}

function generateReport(data) {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Performance Test Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .metric { margin-bottom: 20px; }
          .chart { width: 100%; height: 300px; }
        </style>
      </head>
      <body>
        <h1>Performance Test Results</h1>
        
        <div class="metric">
          <h2>Response Times</h2>
          <div class="chart" id="responseTimesChart"></div>
        </div>

        <div class="metric">
          <h2>Error Rates</h2>
          <div class="chart" id="errorRatesChart"></div>
        </div>

        <div class="metric">
          <h2>AI Suggestion Latency</h2>
          <div class="chart" id="suggestionLatencyChart"></div>
        </div>

        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <script>
          const responseTimesData = ${JSON.stringify(data.metrics.http_req_duration)};
          const errorRatesData = ${JSON.stringify(data.metrics.errors)};
          const suggestionLatencyData = ${JSON.stringify(data.metrics.suggestion_latency)};

          Plotly.newPlot('responseTimesChart', [{
            y: responseTimesData.values,
            type: 'box'
          }]);

          Plotly.newPlot('errorRatesChart', [{
            y: errorRatesData.values,
            type: 'scatter'
          }]);

          Plotly.newPlot('suggestionLatencyChart', [{
            y: suggestionLatencyData.values,
            type: 'violin'
          }]);
        </script>
      </body>
    </html>
  `;
}
