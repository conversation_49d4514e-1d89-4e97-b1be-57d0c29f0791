class DAWRecorderProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this._buffer = [];
    this._recording = false;
    this._rollingBuffer = [];
    this._rollingOffset = 0;
    this._rollingBufferSize = 1024;
    this.port.onmessage = (event) => {
      if (event.data === 'start') {
        this._recording = true;
        this._buffer = [];
        this._rollingBuffer = new Array(this._rollingBufferSize).fill(0);
      } else if (event.data === 'stop') {
        this._recording = false;
      } else if (event.data === 'flush') {
        this.port.postMessage({ type: 'buffer', buffer: new Float32Array(this._buffer) });
        this._buffer = [];
      }
    };
  }

  process(inputs) {
    const input = inputs[0][0];
    if (this._recording && input) {
      this._buffer.push(new Float32Array(input));
      // Rolling buffer for live waveform
      for (let i = 0; i < input.length; i++) {
        this._rollingBuffer[this._rollingOffset] = input[i];
        this._rollingOffset = (this._rollingOffset + 1) % this._rollingBufferSize;
      }
      if (this._buffer.length % 2 === 0) {
        const rollingCopy = this._rollingBuffer.slice(0);
        this.port.postMessage({
          type: 'rolling',
          buffer: rollingCopy,
        });
      }
    }
    return true;
  }
}

registerProcessor('daw-recorder-processor', DAWRecorderProcessor); 