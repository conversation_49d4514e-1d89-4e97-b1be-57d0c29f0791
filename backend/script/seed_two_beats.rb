# Adds two beats with correct audio attachments
hands_up = Beat.create!(
  title: 'Hands up',
  genre: 'hip-hop',
  bpm: 90,
  key: 'C',
  mood: nil,
  description: nil,
  producer_name: 'Test Producer',
  is_free: true,
  status: 'inactive',
  source_type: 'original'
)
hands_up.audio_file.attach(
  io: File.open(File.expand_path('../test_beats/hands_up.wav', __dir__)),
  filename: 'hands_up.wav',
  content_type: 'audio/wav'
)
hands_up.update!(status: 'active')
puts "Added beat: #{hands_up.title} (ID: #{hands_up.id}) with audio file."

supreme = Beat.create!(
  title: 'Supreme',
  genre: 'hip-hop',
  bpm: 88,
  key: 'Cmin',
  mood: nil,
  description: nil,
  producer_name: 'Test Producer',
  is_free: true,
  status: 'inactive',
  source_type: 'original'
)
supreme.audio_file.attach(
  io: File.open(File.expand_path('../test_beats/supreme.wav', __dir__)),
  filename: 'supreme.wav',
  content_type: 'audio/wav'
)
supreme.update!(status: 'active')
puts "Added beat: #{supreme.title} (ID: #{supreme.id}) with audio file." 