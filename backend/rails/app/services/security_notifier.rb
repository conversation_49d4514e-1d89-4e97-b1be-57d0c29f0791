class SecurityNotifier
  class << self
    def notify_security_team(alert)
      # Send email to security team
      SecurityMailer.security_alert(
        recipients: security_team_emails,
        alert: alert
      ).deliver_later

      # Send Slack notification if configured
      notify_slack_security_channel(alert) if slack_enabled?
    end

    def notify_administrators(alert)
      # Send email to administrators
      SecurityMailer.admin_alert(
        recipients: admin_emails,
        alert: alert
      ).deliver_later
    end

    def emergency_notification(alert)
      # Send SMS notifications
      send_sms_notifications(alert)

      # Initiate phone calls if configured
      initiate_phone_calls(alert) if phone_calls_enabled?
    end

    private

    def security_team_emails
      User.security_team.pluck(:email)
    end

    def admin_emails
      User.administrators.pluck(:email)
    end

    def notify_slack_security_channel(alert)
      SlackNotifier.post_to_channel(
        channel: Rails.application.credentials.slack[:security_channel],
        message: format_slack_message(alert),
        color: slack_color_for_severity(alert[:severity])
      )
    end

    def format_slack_message(alert)
      {
        title: "Security Alert: #{alert[:type].to_s.titleize}",
        text: alert[:message],
        fields: [
          {
            title: "Severity",
            value: alert[:severity].to_s.upcase,
            short: true
          },
          {
            title: "Count",
            value: alert[:count],
            short: true
          },
          {
            title: "Details",
            value: format_details(alert[:details])
          }
        ],
        ts: alert[:timestamp].to_i
      }
    end

    def format_details(details)
      return "No details available" if details.blank?

      details.map do |detail|
        detail.map { |k, v| "#{k}: #{v}" }.join(", ")
      end.join("\n")
    end

    def slack_color_for_severity(severity)
      case severity
      when :critical then "danger"
      when :warning then "warning"
      else "good"
      end
    end

    def send_sms_notifications(alert)
      phone_numbers = emergency_contact_numbers

      phone_numbers.each do |number|
        SmsService.send_message(
          to: number,
          message: format_sms_message(alert)
        )
      end
    end

    def format_sms_message(alert)
      "[SECURITY ALERT] #{alert[:type].to_s.upcase}: #{alert[:message]}"
    end

    def initiate_phone_calls(alert)
      phone_numbers = emergency_contact_numbers

      phone_numbers.each do |number|
        PhoneService.initiate_call(
          to: number,
          message: format_phone_message(alert),
          priority: :high
        )
      end
    end

    def format_phone_message(alert)
      "Critical security alert. #{alert[:type].to_s.humanize}. #{alert[:message]}"
    end

    def emergency_contact_numbers
      User.emergency_contacts.pluck(:phone_number)
    end

    def slack_enabled?
      Rails.application.credentials.dig(:slack, :enabled)
    end

    def phone_calls_enabled?
      Rails.application.credentials.dig(:phone_calls, :enabled)
    end
  end
end
