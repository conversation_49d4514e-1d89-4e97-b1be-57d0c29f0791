module Api
  class ExportsController < ApiController
    before_action :authenticate_user!
    before_action :set_freestyle_session
    before_action :check_ownership
    
    # POST /api/freestyle_sessions/:freestyle_session_id/export
    def create
      # Check if user has permission to export (premium feature)
      unless current_user.premium?
        render json: { error: "Export is a premium feature" }, status: :forbidden
        return
      end
      
      # Get the format parameter
      format = params[:format] || 'webm'
      
      # Validate format
      unless ['webm', 'mp3', 'wav'].include?(format)
        render json: { error: "Invalid format. Supported formats: webm, mp3, wav" }, status: :bad_request
        return
      end
      
      # Check if recording exists
      unless @freestyle_session.recording.attached?
        render json: { error: "No recording found for this session" }, status: :not_found
        return
      end
      
      begin
        # Process the recording based on format
        case format
        when 'webm'
          # No conversion needed for webm
          download_url = url_for(@freestyle_session.recording)
        when 'mp3'
          # Convert to mp3 using ffmpeg (would be implemented in a real app)
          # This is a placeholder for the actual conversion logic
          download_url = url_for(@freestyle_session.recording)
        when 'wav'
          # Convert to wav using ffmpeg (would be implemented in a real app)
          # This is a placeholder for the actual conversion logic
          download_url = url_for(@freestyle_session.recording)
        end
        
        # Return the download URL
        render json: { 
          data: { 
            downloadUrl: download_url,
            format: format,
            filename: "freestyle-recording.#{format}"
          },
          message: "Export successful"
        }, status: :ok
      rescue => e
        # Log the error
        Rails.logger.error("Export error: #{e.message}")
        
        # Return error response
        render json: { error: "Export failed: #{e.message}" }, status: :internal_server_error
      end
    end
    
    private
    
    def set_freestyle_session
      @freestyle_session = FreestyleSession.find(params[:freestyle_session_id])
    rescue ActiveRecord::RecordNotFound
      render json: { error: "Freestyle session not found" }, status: :not_found
    end
    
    def check_ownership
      unless @freestyle_session.user_id == current_user.id
        render json: { error: "You don't have permission to access this recording" }, status: :forbidden
      end
    end
  end
end
