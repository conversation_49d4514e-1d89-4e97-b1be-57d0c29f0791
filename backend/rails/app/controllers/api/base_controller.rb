module Api
  class BaseController < ApplicationController
    before_action :set_current_user

    rescue_from ActiveRecord::RecordNotFound, with: :not_found
    rescue_from ActiveRecord::RecordInvalid, with: :unprocessable_entity

    private

    def set_current_user
      if request.headers["Authorization"].present?
        authenticate_user!
      else
        # Allow guest access
        @current_user = nil
      end
    end

    def require_authentication!
      unless current_user
        render json: { error: "Authentication required" }, status: :unauthorized
      end
    end

    def require_premium!
      unless current_user&.premium?
        render json: { error: "Premium subscription required" }, status: :forbidden
      end
    end

    def current_ability
      @current_ability ||= Ability.new(current_user)
    end

    def not_found
      render json: { error: "Resource not found" }, status: :not_found
    end

    def unprocessable_entity(exception)
      render json: { errors: exception.record.errors.full_messages }, status: :unprocessable_entity
    end
  end
end
