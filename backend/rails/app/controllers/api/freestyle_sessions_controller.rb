module Api
  class FreestyleSessionsController < BaseController
    before_action :set_freestyle_session, only: [ :show, :update, :destroy ]
    before_action :require_authentication!, except: [ :create ]
    before_action :require_premium!, only: [ :index, :show ]

    def index
      @sessions = current_user.freestyle_sessions.recent
      render json: { data: FreestyleSessionSerializer.new(@sessions).serializable_hash[:data] }
    end

    def show
      if @freestyle_session.user == current_user
        render json: { data: FreestyleSessionSerializer.new(@freestyle_session).serializable_hash[:data] }
      else
        render json: { error: "Not authorized to view this session" }, status: :forbidden
      end
    end

    def create
      # Only accept beat_id from the root, ignore nested freestyle_session key
      beat_id = params[:beat_id]

      # Create a temporary user if not authenticated
      unless current_user
        temp_user = User.create!(
          email: "temp_#{Time.now.to_i}@example.com",
          password: SecureRandom.hex(10),
          username: "temp_user_#{Time.now.to_i}"
        )
        @freestyle_session = FreestyleSession.new(beat_id: beat_id, user: temp_user)
      else
        @freestyle_session = FreestyleSession.new(beat_id: beat_id, user: current_user)
      end

      @freestyle_session.status = "completed"

      # Attach the recording if provided
      if params[:recording].present?
        @freestyle_session.recording.attach(params[:recording])
      end

      if @freestyle_session.save
        # Return a simplified response without using the serializer
        render json: {
          data: {
            id: @freestyle_session.id,
            beat_id: @freestyle_session.beat_id,
            recording_url: nil, # We don't have a recording yet
            status: @freestyle_session.status,
            created_at: @freestyle_session.created_at
          }
        }, status: :created
      else
        render json: { error: @freestyle_session.errors.full_messages }, status: :unprocessable_entity
      end
    rescue => e
      render json: { error: e.message }, status: :unprocessable_entity
    end

    def update
      if @freestyle_session.update(freestyle_session_params)
        render json: { data: FreestyleSessionSerializer.new(@freestyle_session).serializable_hash[:data] }
      else
        render json: { error: @freestyle_session.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def destroy
      if @freestyle_session.user == current_user
        @freestyle_session.destroy
        head :no_content
      else
        render json: { error: "Not authorized to delete this session" }, status: :forbidden
      end
    end

    private

    def set_freestyle_session
      @freestyle_session = FreestyleSession.find(params[:id])
    end

    def freestyle_session_params
      params.permit(:beat_id, :duration, :recording)
    end
  end
end
