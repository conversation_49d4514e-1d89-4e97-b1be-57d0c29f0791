module Api
  class SessionsController < Devise::SessionsController
    respond_to :json
    skip_before_action :verify_signed_out_user, only: :destroy

    def create
      user = User.find_by(email: sign_in_params[:email])
      if user&.valid_password?(sign_in_params[:password])
        sign_in(user)
        render json: {
          user: UserSerializer.new(user).as_json,
          token: current_token
        }
      else
        render json: { error: "Invalid email or password" }, status: :unauthorized
      end
    end

    def destroy
      sign_out(current_user)
      render json: { message: "Logged out successfully" }
    end

    private

    def current_token
      request.env["warden-jwt_auth.token"]
    end

    def respond_to_on_destroy
      head :no_content
    end
  end
end
