module Api
  class RegistrationsController < Devise::RegistrationsController
    respond_to :json

    def create
      build_resource(sign_up_params)

      if resource.save
        sign_in(resource)
        render json: {
          user: UserSerializer.new(resource).as_json,
          token: current_token
        }
      else
        render json: { errors: resource.errors.full_messages }, status: :unprocessable_entity
      end
    end

    private

    def sign_up_params
      params.require(:user).permit(:email, :password, :password_confirmation, :username)
    end

    def current_token
      request.env["warden-jwt_auth.token"]
    end
  end
end
