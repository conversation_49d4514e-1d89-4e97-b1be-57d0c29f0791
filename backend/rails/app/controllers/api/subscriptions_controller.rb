module Api
  class SubscriptionsController < BaseController
    def show
      render json: current_user.subscription
    end

    def create
      subscription = current_user.build_subscription(subscription_params)

      if subscription.save
        render json: subscription, status: :created
      else
        render json: { errors: subscription.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def update
      subscription = current_user.subscription

      if subscription.update(subscription_params)
        render json: subscription
      else
        render json: { errors: subscription.errors.full_messages }, status: :unprocessable_entity
      end
    end

    private

    def subscription_params
      params.require(:subscription).permit(:plan_type, :status, :starts_at, :ends_at)
    end
  end
end
