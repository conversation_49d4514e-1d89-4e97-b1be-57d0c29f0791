module Api
  class BeatsController < BaseController
    before_action :set_beat, only: [ :show, :update, :destroy ]
    before_action :require_premium!, only: [ :create, :update, :destroy ]

    def index
      @beats = if params[:premium] && current_user&.premium?
        Beat.premium.active
      else
        Beat.free.active
      end

      @beats = @beats.where(genre: params[:genre]) if params[:genre].present?
      @beats = @beats.where(key: params[:key]) if params[:key].present?
      @beats = @beats.where("bpm >= ?", params[:min_bpm]) if params[:min_bpm].present?
      @beats = @beats.where("bpm <= ?", params[:max_bpm]) if params[:max_bpm].present?

      if params[:random].present? && params[:random].to_s == "true"
        limit = params[:limit].to_i > 0 ? params[:limit].to_i : 1
        @beats = @beats.order("RANDOM()").limit(limit)
      end

      render json: { data: BeatSerializer.new(@beats).serializable_hash[:data] }
    end

    def show
      if @beat.is_free || current_user&.premium?
        render json: { data: BeatSerializer.new(@beat).serializable_hash[:data] }
      else
        render json: { error: "Premium subscription required to access this beat" }, status: :forbidden
      end
    end

    def create
      @beat = Beat.new(beat_params)

      if @beat.save
        render json: { data: BeatSerializer.new(@beat).serializable_hash[:data] }, status: :created
      else
        render json: { error: @beat.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def update
      if @beat.update(beat_params)
        render json: { data: BeatSerializer.new(@beat).serializable_hash[:data] }
      else
        render json: { error: @beat.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def destroy
      @beat.destroy
      head :no_content
    end

    private

    def set_beat
      @beat = Beat.find(params[:id])
    end

    def beat_params
      params.require(:beat).permit(
        :title,
        :genre,
        :bpm,
        :key,
        :mood,
        :is_free,
        :description,
        :producer_name,
        :audio_file
      )
    end
  end
end
