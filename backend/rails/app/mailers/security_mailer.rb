class SecurityMailer < ApplicationMailer
  def security_alert(recipients:, alert:)
    @alert = alert
    @timestamp = Time.at(alert[:timestamp])
    @details = format_details(alert[:details])

    mail(
      to: recipients,
      subject: "[SECURITY ALERT] #{alert[:type].to_s.titleize} - #{alert[:severity].to_s.upcase}",
      priority: 1
    )
  end

  def admin_alert(recipients:, alert:)
    @alert = alert
    @timestamp = Time.at(alert[:timestamp])
    @details = format_details(alert[:details])

    mail(
      to: recipients,
      subject: "[ADMIN ALERT] Security Issue: #{alert[:type].to_s.titleize}",
      priority: 1
    )
  end

  private

  def format_details(details)
    return [] if details.blank?

    details.map do |detail|
      detail.map { |k, v| { key: k.to_s.titleize, value: v } }
    end
  end
end
