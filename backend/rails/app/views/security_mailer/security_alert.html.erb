<!DOCTYPE html>
<html>
  <head>
    <meta content='text/html; charset=UTF-8' http-equiv='Content-Type' />
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .alert-header { background-color: #f44336; color: white; padding: 15px; }
      .alert-body { padding: 20px; }
      .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
      .details-table th, .details-table td { 
        border: 1px solid #ddd; 
        padding: 8px; 
        text-align: left; 
      }
      .details-table th { background-color: #f5f5f5; }
      .timestamp { color: #666; font-size: 0.9em; }
    </style>
  </head>
  <body>
    <div class="alert-header">
      <h1>Security Alert: <%= @alert[:type].to_s.titleize %></h1>
      <p>Severity: <%= @alert[:severity].to_s.upcase %></p>
    </div>

    <div class="alert-body">
      <p class="timestamp">Detected at: <%= @timestamp.strftime("%B %d, %Y at %H:%M:%S %Z") %></p>
      
      <h2>Alert Details</h2>
      <% if @details.any? %>
        <table class="details-table">
          <thead>
            <tr>
              <th>Field</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            <% @details.each do |detail| %>
              <% detail.each do |item| %>
                <tr>
                  <td><%= item[:key] %></td>
                  <td><%= item[:value] %></td>
                </tr>
              <% end %>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <p>No additional details available.</p>
      <% end %>

      <p>Please investigate this alert immediately and take appropriate action.</p>
      
      <hr>
      <p>
        <small>
          This is an automated security alert. Please do not reply to this email.
          If you need assistance, please contact the security team through appropriate channels.
        </small>
      </p>
    </div>
  </body>
</html> 