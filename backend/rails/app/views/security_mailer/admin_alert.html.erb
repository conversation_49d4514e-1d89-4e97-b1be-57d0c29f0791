<!DOCTYPE html>
<html>
  <head>
    <meta content='text/html; charset=UTF-8' http-equiv='Content-Type' />
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .alert-header { background-color: #2196F3; color: white; padding: 15px; }
      .alert-body { padding: 20px; }
      .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
      .details-table th, .details-table td { 
        border: 1px solid #ddd; 
        padding: 8px; 
        text-align: left; 
      }
      .details-table th { background-color: #f5f5f5; }
      .timestamp { color: #666; font-size: 0.9em; }
      .severity-high { color: #f44336; font-weight: bold; }
      .severity-medium { color: #ff9800; font-weight: bold; }
      .severity-low { color: #4caf50; font-weight: bold; }
    </style>
  </head>
  <body>
    <div class="alert-header">
      <h1>Administrative Security Alert</h1>
    </div>

    <div class="alert-body">
      <h2><%= @alert[:type].to_s.titleize %></h2>
      <p class="timestamp">Detected at: <%= @timestamp.strftime("%B %d, %Y at %H:%M:%S %Z") %></p>
      
      <p>
        Severity: 
        <span class="severity-<%= @alert[:severity].to_s.downcase %>">
          <%= @alert[:severity].to_s.upcase %>
        </span>
      </p>

      <h3>Alert Details</h3>
      <% if @details.any? %>
        <table class="details-table">
          <thead>
            <tr>
              <th>Field</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            <% @details.each do |detail| %>
              <% detail.each do |item| %>
                <tr>
                  <td><%= item[:key] %></td>
                  <td><%= item[:value] %></td>
                </tr>
              <% end %>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <p>No additional details available.</p>
      <% end %>

      <h3>Required Actions</h3>
      <ol>
        <li>Review the alert details thoroughly</li>
        <li>Assess the potential impact on system security</li>
        <li>Take necessary remediation steps</li>
        <li>Document the incident and response</li>
        <li>Update security protocols if needed</li>
      </ol>

      <hr>
      <p>
        <small>
          This is an automated administrative security alert. For urgent matters, 
          please contact the security team lead directly.
        </small>
      </p>
    </div>
  </body>
</html> 