class Beat < ApplicationRecord
  has_many :freestyle_sessions, dependent: :destroy
  has_many :users, through: :freestyle_sessions
  has_one_attached :audio_file

  validates :title, presence: true
  validates :genre, presence: true, inclusion: { in: %w[hip-hop trap boom-bap lo-fi] }
  validates :bpm, presence: true, numericality: { only_integer: true, greater_than: 0 }
  validates :key, presence: true
  validates :status, presence: true, inclusion: { in: %w[draft active inactive] }
  validates :complexity, numericality: { only_integer: true, greater_than: 0, less_than_or_equal_to: 5 }, allow_nil: true
  validates :source_type, presence: true, inclusion: { in: %w[original uploaded] }
  validates :audio_file, presence: true, if: :active?
  validates :is_free, inclusion: { in: [ true, false ] }

  scope :active, -> { where(status: "active") }
  scope :free, -> { where(is_free: true) }
  scope :premium, -> { where(is_free: false) }

  def duration
    return nil unless audio_file.attached?

    # TODO: Implement audio duration calculation using audio processing library
    # For now, return a placeholder
    180 # 3 minutes
  end

  def audio_url
    return nil unless audio_file.attached?
    Rails.application.routes.url_helpers.rails_blob_url(audio_file, only_path: true)
  end

  def toggle_status
    new_status = status == "active" ? "inactive" : "active"
    update!(status: new_status)
  end

  def active?
    status == "active"
  end
end
