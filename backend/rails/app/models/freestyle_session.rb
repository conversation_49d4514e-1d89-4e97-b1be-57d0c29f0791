class FreestyleSession < ApplicationRecord
  belongs_to :user
  belongs_to :beat
  has_many :ai_suggestions, dependent: :destroy
  has_one_attached :recording

  validates :user, presence: true
  validates :beat, presence: true
  validates :status, presence: true, inclusion: { in: %w[in_progress completed] }
  validates :duration, numericality: { greater_than: 0 }, allow_nil: true

  scope :in_progress, -> { where(status: "in_progress") }
  scope :completed, -> { where(status: "completed") }
  scope :with_recording, -> { joins("INNER JOIN active_storage_attachments ON active_storage_attachments.record_id = freestyle_sessions.id")
                             .where(active_storage_attachments: { record_type: "FreestyleSession", name: "recording" }) }

  def complete!
    update!(status: "completed")
  end

  def duration_in_minutes
    duration ? (duration.to_f / 60) : nil
  end

  def suggestion_count
    ai_suggestions.count
  end

  def used_suggestions_count
    ai_suggestions.used.count
  end

  def recording_url
    return nil unless recording.attached?
    Rails.application.routes.url_helpers.rails_blob_url(recording, only_path: true)
  end
end
