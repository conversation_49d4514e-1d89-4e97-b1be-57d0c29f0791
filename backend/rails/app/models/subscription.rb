class Subscription < ApplicationRecord
  belongs_to :user

  validates :plan_type, presence: true, inclusion: { in: %w[trial premium] }
  validates :status, presence: true, inclusion: { in: %w[active inactive] }
  validates :starts_at, presence: true
  validate :ends_at_after_starts_at, if: -> { ends_at.present? }

  scope :active, -> { where(status: "active") }
  scope :premium, -> { where(plan_type: "premium") }

  def active?
    status == "active" && !expired?
  end

  def expired?
    return false if ends_at.nil?
    ends_at < Time.current
  end

  def days_remaining
    return nil if ends_at.nil?
    return 0 if expired?
    ((ends_at - Time.current) / 1.day).ceil
  end

  private

  def ends_at_after_starts_at
    return if starts_at.nil? || ends_at.nil?

    if ends_at <= starts_at
      errors.add(:ends_at, "must be after the start date")
    end
  end
end
