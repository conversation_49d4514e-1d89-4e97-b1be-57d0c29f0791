class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable

  has_many :freestyle_sessions, dependent: :destroy
  has_many :beats, through: :freestyle_sessions
  has_one :subscription, dependent: :destroy

  validates :username, presence: true, uniqueness: { case_sensitive: false }, length: { in: 3..20 }
  validates :email, presence: true, uniqueness: true
  validates :role, presence: true, inclusion: { in: %w[user admin] }
  before_validation :set_default_role

  after_create :schedule_premium_upsell_email

  def premium?
    subscription&.active? || false
  end

  private

  def set_default_role
    self.role ||= "user"
  end

  def schedule_premium_upsell_email
    PremiumUpsellJob.set(wait: 24.hours).perform_later(self.id)
  end
end
