FactoryBot.define do
  factory :beat do
    title { Faker::Music.album }
    genre { %w[hip-hop trap boom-bap lo-fi].sample }
    bpm { rand(70..160) }
    key { %w[C Cm D Dm E Em F Fm G Gm A Am B Bm].sample }
    mood { %w[chill energetic dark uplifting].sample }
    complexity { rand(1..5) }
    description { Faker::Lorem.paragraph }
    producer_name { Faker::Name.name }
    is_free { [ true, false ].sample }
    status { 'draft' }
    audio_url { Faker::Internet.url(host: 'example.com', path: '/beats/audio.mp3') }
    source_type { 'original' }
    instruments { [ 'drums', 'bass', 'synth', 'piano' ].sample(2) }
    production_style { %w[modern classic experimental].sample }

    trait :active do
      status { 'active' }
      after(:build) do |beat|
        beat.audio_file.attach(
          io: StringIO.new("fake audio content"),
          filename: 'test.mp3',
          content_type: 'audio/mpeg'
        )
      end
    end

    trait :inactive do
      status { 'inactive' }
    end
  end
end
