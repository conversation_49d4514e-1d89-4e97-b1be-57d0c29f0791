FactoryBot.define do
  factory :freestyle_session do
    user
    beat
    status { 'in_progress' }
    duration { rand(60..300) } # Random duration between 1-5 minutes

    trait :completed do
      status { 'completed' }
    end

    trait :with_recording do
      after(:build) do |session|
        session.recording.attach(
          io: StringIO.new("fake recording content"),
          filename: 'test_recording.mp3',
          content_type: 'audio/mpeg'
        )
      end
    end

    trait :with_suggestions do
      after(:create) do |session|
        create_list(:ai_suggestion, 3, freestyle_session: session)
      end
    end
  end
end
