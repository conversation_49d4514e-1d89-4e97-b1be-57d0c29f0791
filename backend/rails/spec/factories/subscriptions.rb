FactoryBot.define do
  factory :subscription do
    user
    plan_type { 'premium' }
    status { 'active' }
    starts_at { 1.month.ago }
    ends_at { 1.month.from_now }

    trait :inactive do
      status { 'inactive' }
    end

    trait :expired do
      starts_at { 2.months.ago }
      ends_at { 1.month.ago }
    end

    trait :trial do
      plan_type { 'trial' }
      starts_at { Time.current }
      ends_at { 14.days.from_now }
    end
  end
end
