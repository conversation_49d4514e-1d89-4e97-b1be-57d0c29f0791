FactoryBot.define do
  factory :user do
    email { Faker::Internet.unique.email }
    username { Faker::Internet.unique.username(specifier: 5..20) }
    password { 'password123' }
    password_confirmation { 'password123' }
    role { 'user' }

    trait :admin do
      role { 'admin' }
    end

    trait :premium do
      after(:create) do |user|
        create(:subscription, user: user, plan_type: 'premium', status: 'active')
      end
    end

    trait :with_sessions do
      after(:create) do |user|
        create_list(:freestyle_session, 3, user: user)
      end
    end
  end
end
