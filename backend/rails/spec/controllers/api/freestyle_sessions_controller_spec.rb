[1] Processing by ActiveStorage::Blobs::RedirectController#show as MP3
[1]   Parameters: {"signed_id"=>"eyJfcmFpbHMiOnsiZGF0YSI6MTYsInB1ciI6ImJsb2JfaWQifX0=--c510c6f5d5bade1f30e88b0333c773ec6a4e02d8", "filename"=>"supreme"}
[1]   ActiveStorage::Blob Load (0.9ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = 16 LIMIT 1 /*action='show',application='Backend',controller='redirect'*/
[1]   Disk Storage (0.6ms) Generated URL for file at key: 0ybyn7ae7xwxuby7x6a39m3a0ik7 (http://localhost:3000/rails/active_storage/disk/eyJfcmFpbHMiOnsiZGF0YSI6eyJrZXkiOiIweWJ5bjdhZTd4d3h1Ynk3eDZhMzltM2EwaWs3IiwiZGlzcG9zaXRpb24iOiJhdHRhY2htZW50OyBmaWxlbmFtZT1cInN1cHJlbWUubXAzXCI7IGZpbGVuYW1lKj1VVEYtOCcnc3VwcmVtZS5tcDMiLCJjb250ZW50X3R5cGUiOiJhdWRpby94LXdhdiIsInNlcnZpY2VfbmFtZSI6ImxvY2FsIn0sImV4cCI6IjIwMjUtMDUtMTNUMDY6MzY6MzAuMDU0WiIsInB1ciI6ImJsb2Jfa2V5In19--852cd2066dcd32dd6e9d52df50531c45e0dac8c1/supreme.mp3)
[1] Redirected to http://localhost:3000/rails/active_storage/disk/eyJfcmFpbHMiOnsiZGF0YSI6eyJrZXkiOiIweWJ5bjdhZTd4d3h1Ynk3eDZhMzltM2EwaWs3IiwiZGlzcG9zaXRpb24iOiJhdHRhY2htZW50OyBmaWxlbmFtZT1cInN1cHJlbWUubXAzXCI7IGZpbGVuYW1lKj1VVEYtOCcnc3VwcmVtZS5tcDMiLCJjb250ZW50X3R5cGUiOiJhdWRpby94LXdhdiIsInNlcnZpY2VfbmFtZSI6ImxvY2FsIn0sImV4cCI6IjIwMjUtMDUtMTNUMDY6MzY6MzAuMDU0WiIsInB1ciI6ImJsb2Jfa2V5In19--852cd2066dcd32dd6e9d52df50531c45e0dac8c1/supreme.mp3
[1] Completed 302 Found in 4ms (ActiveRecord: 0.9ms | Allocations: 1289)
[1] 
[1] 
[1] Started GET "/rails/active_storage/disk/eyJfcmFpbHMiOnsiZGF0YSI6eyJrZXkiOiIweWJ5bjdhZTd4d3h1Ynk3eDZhMzltM2EwaWs3IiwiZGlzcG9zaXRpb24iOiJhdHRhY2htZW50OyBmaWxlbmFtZT1cInN1cHJlbWUubXAzXCI7IGZpbGVuYW1lKj1VVEYtOCcnc3VwcmVtZS5tcDMiLCJjb250ZW50X3R5cGUiOiJhdWRpby94LXdhdiIsInNlcnZpY2VfbmFtZSI6ImxvY2FsIn0sImV4cCI6IjIwMjUtMDUtMTNUMDY6MzY6MzAuMDU0WiIsInB1ciI6ImJsb2Jfa2V5In19--852cd2066dcd32dd6e9d52df50531c45e0dac8c1/supreme.mp3" for ::1 at 2025-05-13 00:31:30 -0600
[1] Processing by ActiveStorage::DiskController#show as MP3
[1]   Parameters: {"encoded_key"=>"[FILTERED]", "filename"=>"supreme"}
[1] Completed 304 Not Modified in 0ms (ActiveRecord: 0.0ms | Allocations: 124)
[1] 
[1] 
[1] Processing by ActiveStorage::DiskController#show as MP3
[1]   Parameters: {"encoded_key"=>"[FILTERED]", "filename"=>"supreme"}
[1] Completed 206 Partial Content in 2ms (ActiveRecord: 0.0ms | Allocations: 157)
[1] 
[1] 
[1] Started GET "/rails/active_storage/disk/eyJfcmFpbHMiOnsiZGF0YSI6eyJrZXkiOiIweWJ5bjdhZTd4d3h1Ynk3eDZhMzltM2EwaWs3IiwiZGlzcG9zaXRpb24iOiJhdHRhY2htZW50OyBmaWxlbmFtZT1cInN1cHJlbWUubXAzXCI7IGZpbGVuYW1lKj1VVEYtOCcnc3VwcmVtZS5tcDMiLCJjb250ZW50X3R5cGUiOiJhdWRpby94LXdhdiIsInNlcnZpY2VfbmFtZSI6ImxvY2FsIn0sImV4cCI6IjIwMjUtMDUtMTNUMDY6MzY6MzAuMDU0WiIsInB1ciI6ImJsb2Jfa2V5In19--852cd2066dcd32dd6e9d52df50531c45e0dac8c1/supreme.mp3" for ::1 at 2025-05-13 00:31:30 -0600
[1] Processing by ActiveStorage::DiskController#show as MP3
[1]   Parameters: {"encoded_key"=>"[FILTERED]", "filename"=>"supreme"}
[1] Completed 206 Partial Content in 0ms (ActiveRecord: 0.0ms | Allocations: 149)
[1] 
[1] 
[1] Started GET "/rails/active_storage/disk/eyJfcmFpbHMiOnsiZGF0YSI6eyJrZXkiOiIweWJ5bjdhZTd4d3h1Ynk3eDZhMzltM2EwaWs3IiwiZGlzcG9zaXRpb24iOiJhdHRhY2htZW50OyBmaWxlbmFtZT1cInN1cHJlbWUubXAzXCI7IGZpbGVuYW1lKj1VVEYtOCcnc3VwcmVtZS5tcDMiLCJjb250ZW50X3R5cGUiOiJhdWRpby94LXdhdiIsInNlcnZpY2VfbmFtZSI6ImxvY2FsIn0sImV4cCI6IjIwMjUtMDUtMTNUMDY6MzY6MzAuMDU0WiIsInB1ciI6ImJsb2Jfa2V5In19--852cd2066dcd32dd6e9d52df50531c45e0dac8c1/supreme.mp3" for ::1 at 2025-05-13 00:31:31 -0600
[1] Processing by ActiveStorage::DiskController#show as MP3
[1]   Parameters: {"encoded_key"=>"[FILTERED]", "filename"=>"supreme"}
[1] Completed 304 Not Modified in 0ms (ActiveRecord: 0.0ms | Allocations: 125)
[1] 
[1] 
[1] Started GET "/rails/active_storage/disk/eyJfcmFpbHMiOnsiZGF0YSI6eyJrZXkiOiIweWJ5bjdhZTd4d3h1Ynk3eDZhMzltM2EwaWs3IiwiZGlzcG9zaXRpb24iOiJhdHRhY2htZW50OyBmaWxlbmFtZT1cInN1cHJlbWUubXAzXCI7IGZpbGVuYW1lKj1VVEYtOCcnc3VwcmVtZS5tcDMiLCJjb250ZW50X3R5cGUiOiJhdWRpby94LXdhdiIsInNlcnZpY2VfbmFtZSI6ImxvY2FsIn0sImV4cCI6IjIwMjUtMDUtMTNUMDY6MzY6MzAuMDU0WiIsInB1ciI6ImJsb2Jfa2V5In19--852cd2066dcd32dd6e9d52df50531c45e0dac8c1/supreme.mp3" for ::1 at 2025-05-13 00:31:31 -0600
[1] Processing by ActiveStorage::DiskController#show as MP3
[1]   Parameters: {"encoded_key"=>"[FILTERED]", "filename"=>"supreme"}
[1] Completed 206 Partial Content in 1ms (ActiveRecord: 0.0ms | Allocations: 148)
[1] 
[1] 
[1] Started POST "/api/freestyle_sessions" for ::1 at 2025-05-13 00:31:33 -0600
[1] Processing by Api::FreestyleSessionsController#create as */*
[1]   Parameters: {"beat_id"=>2, "freestyle_session"=>{"beat_id"=>2}}
[1]   TRANSACTION (0.1ms)  BEGIN /*action='create',application='Backend',controller='freestyle_sessions'*/
[1]   ↳ app/controllers/api/freestyle_sessions_controller.rb:32:in `create'
[1]   Beat Load (0.2ms)  SELECT "beats".* FROM "beats" WHERE "beats"."id" = 2 LIMIT 1 /*action='create',application='Backend',controller='freestyle_sessions'*/
[1]   ↳ app/controllers/api/freestyle_sessions_controller.rb:32:in `create'
[1]   TRANSACTION (0.1ms)  ROLLBACK /*action='create',application='Backend',controller='freestyle_sessions'*/
[1]   ↳ app/controllers/api/freestyle_sessions_controller.rb:32:in `create'
[1] Completed 422 Unprocessable Content in 12ms (Views: 0.1ms | ActiveRecord: 8.2ms | Allocations: 8385)
[1] 
[1] 
[0]  GET /favicon.ico 200 in 18ms
[1] Started POST "/api/freestyle_sessions" for ::1 at 2025-05-13 00:31:40 -0600
[1] Processing by Api::FreestyleSessionsController#create as */*
[1]   Parameters: {"beat_id"=>2, "freestyle_session"=>{"beat_id"=>2}}
[1]   TRANSACTION (0.1ms)  BEGIN /*action='create',application='Backend',controller='freestyle_sessions'*/
[1]   ↳ app/controllers/api/freestyle_sessions_controller.rb:32:in `create'
[1]   Beat Load (0.9ms)  SELECT "beats".* FROM "beats" WHERE "beats"."id" = 2 LIMIT 1 /*action='create',application='Backend',controller='freestyle_sessions'*/
[1]   ↳ app/controllers/api/freestyle_sessions_controller.rb:32:in `create'
[1]   TRANSACTION (0.1ms)  ROLLBACK /*action='create',application='Backend',controller='freestyle_sessions'*/
[1]   ↳ app/controllers/api/freestyle_sessions_controller.rb:32:in `create'
[1] Completed 422 Unprocessable Content in 3ms (Views: 0.1ms | ActiveRecord: 1.1ms | Allocations: 2696)
[1] 
[1] 
