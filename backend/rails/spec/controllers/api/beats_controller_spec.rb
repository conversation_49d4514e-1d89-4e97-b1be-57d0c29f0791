require 'rails_helper'

RSpec.describe Api::BeatsController, type: :controller do
  let(:user) { create(:user) }
  let(:admin) { create(:user, :admin) }
  let(:premium_user) { create(:user, :premium) }

  describe 'GET #index' do
    let!(:free_beat) { create(:beat, :active, is_free: true) }
    let!(:premium_beat) { create(:beat, :active, is_free: false) }
    let!(:inactive_beat) { create(:beat, :inactive) }

    context 'when not authenticated' do
      before { get :index }

      it 'returns only free, active beats' do
        expect(response).to have_http_status(:ok)
        expect(json_response[:beats].length).to eq(1)
        expect(json_response[:beats].first[:id]).to eq(free_beat.id)
      end
    end

    context 'when authenticated as free user' do
      before do
        sign_in user
        get :index
      end

      it 'returns only free, active beats' do
        expect(response).to have_http_status(:ok)
        expect(json_response[:beats].length).to eq(1)
        expect(json_response[:beats].first[:id]).to eq(free_beat.id)
      end
    end

    context 'when authenticated as premium user' do
      before do
        sign_in premium_user
        get :index
      end

      it 'returns all active beats' do
        expect(response).to have_http_status(:ok)
        expect(json_response[:beats].length).to eq(2)
        expect(json_response[:beats].map { |b| b[:id] }).to contain_exactly(free_beat.id, premium_beat.id)
      end
    end

    context 'when authenticated as admin' do
      before do
        sign_in admin
        get :index, params: { include_inactive: true }
      end

      it 'returns all beats including inactive ones' do
        expect(response).to have_http_status(:ok)
        expect(json_response[:beats].length).to eq(3)
        expect(json_response[:beats].map { |b| b[:id] })
          .to contain_exactly(free_beat.id, premium_beat.id, inactive_beat.id)
      end
    end
  end

  describe 'GET #show' do
    let(:beat) { create(:beat, :active) }

    context 'when not authenticated' do
      before { get :show, params: { id: beat.id } }

      it 'returns the beat' do
        expect(response).to have_http_status(:ok)
        expect(json_response[:beat][:id]).to eq(beat.id)
      end
    end

    context 'when beat is premium and user is not premium' do
      let(:premium_beat) { create(:beat, :active, is_free: false) }

      before do
        sign_in user
        get :show, params: { id: premium_beat.id }
      end

      it 'returns unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST #create' do
    let(:valid_attributes) do
      {
        beat: {
          title: 'Test Beat',
          genre: 'hip-hop',
          bpm: 90,
          key: 'Cm',
          is_free: true,
          source_type: 'original'
        }
      }
    end

    context 'when not authenticated' do
      before { post :create, params: valid_attributes }

      it 'returns unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when authenticated as admin' do
      before do
        sign_in admin
        post :create, params: valid_attributes
      end

      it 'creates a new beat' do
        expect(response).to have_http_status(:created)
        expect(Beat.count).to eq(1)
        expect(json_response[:beat][:title]).to eq('Test Beat')
      end
    end

    context 'with invalid attributes' do
      before do
        sign_in admin
        post :create, params: { beat: { title: '' } }
      end

      it 'returns unprocessable entity' do
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response[:errors]).to include("Title can't be blank")
      end
    end
  end

  describe 'PUT #update' do
    let(:beat) { create(:beat, :active) }
    let(:valid_attributes) { { beat: { title: 'Updated Title' } } }

    context 'when not authenticated' do
      before { put :update, params: { id: beat.id }.merge(valid_attributes) }

      it 'returns unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when authenticated as admin' do
      before do
        sign_in admin
        put :update, params: { id: beat.id }.merge(valid_attributes)
      end

      it 'updates the beat' do
        expect(response).to have_http_status(:ok)
        expect(beat.reload.title).to eq('Updated Title')
      end
    end
  end

  describe 'DELETE #destroy' do
    let!(:beat) { create(:beat, :active) }

    context 'when not authenticated' do
      before { delete :destroy, params: { id: beat.id } }

      it 'returns unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when authenticated as admin' do
      before do
        sign_in admin
        delete :destroy, params: { id: beat.id }
      end

      it 'deletes the beat' do
        expect(response).to have_http_status(:no_content)
        expect(Beat.exists?(beat.id)).to be false
      end
    end
  end

  private

  def json_response
    @json_response ||= JSON.parse(response.body, symbolize_names: true)
  end
end
