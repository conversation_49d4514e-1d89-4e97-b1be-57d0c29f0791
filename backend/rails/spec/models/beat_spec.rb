require 'rails_helper'

RSpec.describe Beat, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:genre) }
    it { should validate_presence_of(:bpm) }
    it { should validate_presence_of(:key) }
    it { should validate_presence_of(:status) }

    it { should validate_numericality_of(:bpm).only_integer.is_greater_than(0) }
    it { should validate_numericality_of(:complexity).only_integer.is_greater_than(0).is_less_than_or_equal_to(5).allow_nil }

    it { should validate_inclusion_of(:genre).in_array(%w[hip-hop trap boom-bap lo-fi]) }
    it { should validate_inclusion_of(:status).in_array(%w[draft active inactive]) }

    context 'when status is active' do
      subject { build(:beat, :active) }
      it { should validate_presence_of(:audio_file) }
    end

    context 'when status is not active' do
      subject { build(:beat, :inactive) }
      it { should_not validate_presence_of(:audio_file) }
    end
  end

  describe 'associations' do
    it { should have_many(:freestyle_sessions) }
    it { should have_many(:users).through(:freestyle_sessions) }
  end

  describe 'scopes' do
    let!(:free_beat) { create(:beat, :active, is_free: true) }
    let!(:premium_beat) { create(:beat, :active, is_free: false) }
    let!(:inactive_beat) { create(:beat, :inactive) }

    describe '.free' do
      it 'returns only free beats' do
        expect(Beat.free).to include(free_beat)
        expect(Beat.free).not_to include(premium_beat)
      end
    end

    describe '.premium' do
      it 'returns only premium beats' do
        expect(Beat.premium).to include(premium_beat)
        expect(Beat.premium).not_to include(free_beat)
      end
    end

    describe '.active' do
      it 'returns only active beats' do
        expect(Beat.active).to include(free_beat, premium_beat)
        expect(Beat.active).not_to include(inactive_beat)
      end
    end
  end

  describe 'instance methods' do
    let(:beat) { create(:beat, :active) }

    describe '#duration' do
      it 'returns the actual duration of the audio file' do
        skip 'Implement audio duration calculation using audio processing library'
        # Test will be implemented when we add audio processing
        beat.audio_file.attach(io: File.open('spec/fixtures/test_beat.mp3'), filename: 'test_beat.mp3')
        expect(beat.duration).to eq(120) # Example: 2 minutes
      end
    end

    describe '#toggle_status' do
      it 'toggles between active and inactive status' do
        expect { beat.toggle_status }.to change { beat.status }
          .from('active').to('inactive')

        expect { beat.toggle_status }.to change { beat.status }
          .from('inactive').to('active')
      end
    end
  end
end
