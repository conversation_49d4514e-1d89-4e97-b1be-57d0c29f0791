require 'rails_helper'

RSpec.describe FreestyleSession, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:user) }
    it { should validate_presence_of(:beat) }
    it { should validate_presence_of(:status) }

    it { should validate_inclusion_of(:status).in_array(%w[in_progress completed]) }

    context 'when duration is present' do
      it { should validate_numericality_of(:duration).is_greater_than(0) }
    end
  end

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:beat) }
    it { should have_many(:ai_suggestions).dependent(:destroy) }
    it { should have_one_attached(:recording) }
  end

  describe 'scopes' do
    let!(:in_progress_session) { create(:freestyle_session) }
    let!(:completed_session) { create(:freestyle_session, :completed) }
    let!(:session_with_recording) { create(:freestyle_session, :with_recording) }

    describe '.in_progress' do
      it 'returns only in-progress sessions' do
        expect(FreestyleSession.in_progress).to include(in_progress_session)
        expect(FreestyleSession.in_progress).not_to include(completed_session)
      end
    end

    describe '.completed' do
      it 'returns only completed sessions' do
        expect(FreestyleSession.completed).to include(completed_session)
        expect(FreestyleSession.completed).not_to include(in_progress_session)
      end
    end

    describe '.with_recording' do
      it 'returns only sessions with recordings' do
        expect(FreestyleSession.with_recording).to include(session_with_recording)
        expect(FreestyleSession.with_recording).not_to include(in_progress_session)
      end
    end
  end

  describe 'instance methods' do
    let(:session) { create(:freestyle_session, duration: 150) } # 2.5 minutes

    describe '#complete!' do
      it 'marks the session as completed' do
        expect { session.complete! }.to change { session.status }
          .from('in_progress').to('completed')
      end
    end

    describe '#duration_in_minutes' do
      it 'returns the duration in minutes' do
        expect(session.duration_in_minutes).to eq(2.5)
      end

      context 'when duration is nil' do
        let(:session) { create(:freestyle_session, duration: nil) }

        it 'returns nil' do
          expect(session.duration_in_minutes).to be_nil
        end
      end
    end

    describe '#suggestion_count' do
      let(:session) { create(:freestyle_session, :with_suggestions) }

      it 'returns the total number of suggestions' do
        expect(session.suggestion_count).to eq(3)
      end
    end

    describe '#used_suggestions_count' do
      let(:session) { create(:freestyle_session) }

      before do
        create(:ai_suggestion, :used, freestyle_session: session)
        create(:ai_suggestion, freestyle_session: session)
        create(:ai_suggestion, :used, freestyle_session: session)
      end

      it 'returns the number of used suggestions' do
        expect(session.used_suggestions_count).to eq(2)
      end
    end

    describe '#recording_url' do
      context 'when recording is attached' do
        let(:session) { create(:freestyle_session, :with_recording) }

        it 'returns the recording URL' do
          expect(session.recording_url).to be_present
          expect(session.recording_url).to include('/rails/active_storage/blobs/')
        end
      end

      context 'when recording is not attached' do
        let(:session) { create(:freestyle_session) }

        it 'returns nil' do
          expect(session.recording_url).to be_nil
        end
      end
    end
  end
end
