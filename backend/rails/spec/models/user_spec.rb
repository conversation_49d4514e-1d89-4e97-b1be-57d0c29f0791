require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'validations' do
    subject { build(:user) }

    it { should validate_presence_of(:username) }
    it { should validate_presence_of(:email) }

    it { should validate_uniqueness_of(:username).case_insensitive }
    it { should validate_uniqueness_of(:email).case_insensitive }

    it { should validate_length_of(:username).is_at_least(3).is_at_most(20) }

    it { should validate_inclusion_of(:role).in_array(%w[user admin]) }

    it 'validates role presence before setting default' do
      user = build(:user)
      user.role = nil
      expect(user.role).to be_nil
      user.valid?
      expect(user.role).to eq('user')
    end
  end

  describe 'associations' do
    it { should have_many(:freestyle_sessions).dependent(:destroy) }
    it { should have_many(:beats).through(:freestyle_sessions) }
    it { should have_one(:subscription).dependent(:destroy) }
  end

  describe 'instance methods' do
    describe '#premium?' do
      context 'when user has an active subscription' do
        let(:user) { create(:user, :premium) }

        it 'returns true' do
          expect(user.premium?).to be true
        end
      end

      context 'when user has no subscription' do
        let(:user) { create(:user) }

        it 'returns false' do
          expect(user.premium?).to be false
        end
      end

      context 'when user has an inactive subscription' do
        let(:user) { create(:user) }

        before do
          create(:subscription, user: user, status: 'inactive')
        end

        it 'returns false' do
          expect(user.premium?).to be false
        end
      end
    end
  end

  describe 'devise configuration' do
    it { should validate_presence_of(:password) }
    it { should validate_confirmation_of(:password) }

    context 'when creating a new user' do
      let(:user) { create(:user, password: 'password123') }

      it 'stores an encrypted password' do
        expect(user.encrypted_password).to be_present
        expect(user.encrypted_password).not_to eq('password123')
        expect(user.valid_password?('password123')).to be true
      end
    end
  end

  describe 'callbacks' do
    describe 'before_validation' do
      it 'sets default role to user' do
        user = build(:user, role: nil)
        user.valid?
        expect(user.role).to eq('user')
      end
    end
  end
end
