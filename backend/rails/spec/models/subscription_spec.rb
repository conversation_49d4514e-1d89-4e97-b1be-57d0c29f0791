require 'rails_helper'

RSpec.describe Subscription, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:plan_type) }
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:starts_at) }

    it { should validate_inclusion_of(:plan_type).in_array(%w[trial premium]) }
    it { should validate_inclusion_of(:status).in_array(%w[active inactive]) }
  end

  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'instance methods' do
    describe '#active?' do
      context 'when subscription is active and not expired' do
        let(:subscription) { create(:subscription) }

        it 'returns true' do
          expect(subscription.active?).to be true
        end
      end

      context 'when subscription is inactive' do
        let(:subscription) { create(:subscription, :inactive) }

        it 'returns false' do
          expect(subscription.active?).to be false
        end
      end

      context 'when subscription is expired' do
        let(:subscription) { create(:subscription, :expired) }

        it 'returns false' do
          expect(subscription.active?).to be false
        end
      end
    end

    describe '#expired?' do
      context 'when end date is in the past' do
        let(:subscription) { create(:subscription, :expired) }

        it 'returns true' do
          expect(subscription.expired?).to be true
        end
      end

      context 'when end date is in the future' do
        let(:subscription) { create(:subscription) }

        it 'returns false' do
          expect(subscription.expired?).to be false
        end
      end

      context 'when end date is not set' do
        let(:subscription) { create(:subscription, ends_at: nil) }

        it 'returns false' do
          expect(subscription.expired?).to be false
        end
      end
    end

    describe '#days_remaining' do
      context 'when subscription is active' do
        let(:subscription) { create(:subscription, ends_at: 15.days.from_now) }

        it 'returns the number of days until expiration' do
          expect(subscription.days_remaining).to be_within(1).of(15)
        end
      end

      context 'when subscription is expired' do
        let(:subscription) { create(:subscription, :expired) }

        it 'returns 0' do
          expect(subscription.days_remaining).to eq(0)
        end
      end

      context 'when end date is not set' do
        let(:subscription) { create(:subscription, ends_at: nil) }

        it 'returns nil' do
          expect(subscription.days_remaining).to be_nil
        end
      end
    end
  end
end
