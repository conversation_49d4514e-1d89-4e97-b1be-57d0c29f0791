namespace :beats do
  desc "Import test beats from backend/test_beats/"
  task import_test_beats: :environment do
    beats_dir = Rails.root.join("test_beats")
    Dir.glob(beats_dir.join("*.wav")).each do |file_path|
      filename = File.basename(file_path)
      # Skip if a Beat with this audio file already exists
      existing = Beat.joins(audio_file_attachment: :blob).where(active_storage_blobs: { filename: filename }).first
      if existing
        puts "Skipping #{filename}: already imported."
        next
      end

      beat = Beat.new(
        title: File.basename(filename, ".wav").humanize,
        genre: "hip-hop",
        key: "C",
        bpm: 90,
        producer_name: "Test Producer",
        is_free: true,
        status: "active",
        source_type: "uploaded"
      )
      beat.audio_file.attach(io: File.open(file_path), filename: filename, content_type: "audio/wav")
      if beat.save
        puts "Imported #{filename} as Beat ##{beat.id}"
      else
        puts "Failed to import #{filename}: #{beat.errors.full_messages.join(', ')}"
      end
    end
    puts "Import complete."
  end
end
