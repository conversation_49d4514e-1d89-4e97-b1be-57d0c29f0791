GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.5.1)
      actionpack (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.5.1)
      actionpack (= 7.1.5.1)
      actionview (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.5.1)
      actionview (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.5.1)
      actionpack (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.5.1)
      activesupport (= 7.1.5.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.1.5.1)
      activesupport (= 7.1.5.1)
      globalid (>= 0.3.6)
    activemodel (7.1.5.1)
      activesupport (= 7.1.5.1)
    activerecord (7.1.5.1)
      activemodel (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      timeout (>= 0.4.0)
    activestorage (7.1.5.1)
      actionpack (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      marcel (~> 1.0)
    activesupport (7.1.5.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    ast (2.4.3)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    bcrypt_pbkdf (1.1.1-arm64-darwin)
    bcrypt_pbkdf (1.1.1-x86_64-darwin)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.1)
    crass (1.0.6)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.1)
    docile (1.4.1)
    dotenv (3.1.8)
    drb (2.2.1)
    ed25519 (1.3.0)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.10.2)
    jsonapi-serializer (2.2.0)
      activesupport (>= 4.2)
    kamal (2.5.3)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.2)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    language_server-protocol (3.17.0.4)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    mutex_m (0.3.0)
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.3)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.13)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (7.1.5.1)
      actioncable (= 7.1.5.1)
      actionmailbox (= 7.1.5.1)
      actionmailer (= 7.1.5.1)
      actionpack (= 7.1.5.1)
      actiontext (= 7.1.5.1)
      actionview (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activemodel (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      bundler (>= 1.15.0)
      railties (= 7.1.5.1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.13.1)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.5)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    rubocop (1.75.3)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.31.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    solid_cable (1.0.3)
      rails (< 9)
    solid_cache (0.7.0)
      activejob (>= 7)
      activerecord (>= 7)
      railties (>= 7)
    solid_queue (1.1.5)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stringio (3.1.7)
    thor (1.3.2)
    thruster (0.1.13)
    thruster (0.1.13-aarch64-linux)
    thruster (0.1.13-arm64-darwin)
    thruster (0.1.13-x86_64-darwin)
    thruster (0.1.13-x86_64-linux)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.2)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-darwin
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  bootsnap
  brakeman
  database_cleaner-active_record
  debug
  devise (~> 4.9)
  factory_bot_rails
  faker
  jsonapi-serializer
  kamal
  pg (~> 1.1)
  puma (>= 5.0)
  rack-cors
  rails (~> 7.1.0)
  rspec-rails (~> 6.1.0)
  rubocop (~> 1.75)
  rubocop-rails-omakase
  shoulda-matchers
  simplecov
  solid_cable
  solid_cache
  solid_queue
  thruster
  tzinfo-data

BUNDLED WITH
   2.6.8
