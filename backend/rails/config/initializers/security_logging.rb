require "logger"
require "json"

# Configure security event logging
Rails.application.configure do
  # Create a dedicated logger for security events
  security_log_path = Rails.root.join("log", "security_#{Rails.env}.log")
  security_logger = Logger.new(security_log_path, "daily")

  # Configure log format
  security_logger.formatter = proc do |severity, datetime, progname, msg|
    log_entry = {
      timestamp: datetime.iso8601,
      severity: severity,
      environment: Rails.env,
      process_id: Process.pid,
      thread_id: Thread.current.object_id,
      message: msg
    }

    if msg.is_a?(Hash)
      log_entry.merge!(msg)
    end

    "#{JSON.dump(log_entry)}\n"
  end

  # Set log level based on environment
  security_logger.level = Rails.env.production? ? Logger::INFO : Logger::DEBUG

  # Add security logger to Rails configuration
  config.security_logger = security_logger

  # Define security event types
  config.security_events = {
    authentication: {
      login_success: "AUTH001",
      login_failure: "AUTH002",
      logout: "AUTH003",
      password_change: "AUTH004",
      mfa_enabled: "AUTH005",
      mfa_disabled: "AUTH006"
    },
    access_control: {
      unauthorized_access: "ACL001",
      permission_change: "ACL002",
      role_change: "ACL003",
      elevation_attempt: "ACL004"
    },
    data_access: {
      sensitive_read: "DAT001",
      sensitive_write: "DAT002",
      bulk_export: "DAT003",
      unusual_access: "DAT004"
    },
    system: {
      config_change: "SYS001",
      startup: "SYS002",
      shutdown: "SYS003",
      error: "SYS004",
      performance: "SYS005"
    },
    security: {
      attack_detected: "SEC001",
      vulnerability_found: "SEC002",
      policy_violation: "SEC003",
      anomaly_detected: "SEC004"
    }
  }

  # Configure Lograge (if enabled)
  if defined?(Lograge)
    config.lograge.custom_options = lambda do |event|
      {
        request_id: event.payload[:request_id],
        user_id: event.payload[:user_id],
        remote_ip: event.payload[:remote_ip],
        user_agent: event.payload[:user_agent]
      }
    end
  end
end

# Define security logging methods
module SecurityLogging
  def self.log_security_event(event_type, details = {})
    logger = Rails.application.config.security_logger
    event_code = find_event_code(event_type)

    log_entry = {
      event_type: event_type,
      event_code: event_code,
      details: details,
      request_id: Thread.current[:request_id],
      user_id: Thread.current[:user_id],
      ip_address: Thread.current[:remote_ip]
    }

    logger.info(log_entry)

    # Send critical events to real-time monitoring
    if details[:severity] == "critical"
      SecurityMonitoring.alert(log_entry)
    end
  end

  def self.find_event_code(event_type)
    events = Rails.application.config.security_events
    events.each do |category, codes|
      codes.each do |type, code|
        return code if type == event_type
      end
    end
    "UNKNOWN"
  end
end

# Add security logging to ApplicationController
module SecurityLoggingConcern
  extend ActiveSupport::Concern

  included do
    before_action :set_security_context
    after_action :clear_security_context
  end

  private

  def set_security_context
    Thread.current[:request_id] = request.request_id
    Thread.current[:user_id] = current_user&.id
    Thread.current[:remote_ip] = request.remote_ip
  end

  def clear_security_context
    Thread.current[:request_id] = nil
    Thread.current[:user_id] = nil
    Thread.current[:remote_ip] = nil
  end

  def log_security_event(event_type, details = {})
    SecurityLogging.log_security_event(event_type, details)
  end
end

# Example usage in controllers:
#
# class ApplicationController < ActionController::Base
#   include SecurityLoggingConcern
# end
#
# class UsersController < ApplicationController
#   def login
#     if user.authenticate(params[:password])
#       log_security_event(:login_success, user_id: user.id)
#     else
#       log_security_event(:login_failure,
#         user_id: params[:email],
#         reason: 'Invalid credentials'
#       )
#     end
#   end
# end
