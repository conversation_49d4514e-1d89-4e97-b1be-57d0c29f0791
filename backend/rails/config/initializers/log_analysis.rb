require "json"

# Configure log analysis system
module LogAnalysis
  class Configuration
    class << self
      attr_accessor :patterns, :thresholds, :rules, :actions

      def configure
        yield self
      end
    end
  end

  # Define pattern matchers for security events
  Configuration.patterns = {
    # Authentication patterns
    auth_failure: ->(log) {
      log[:event_code]&.start_with?("AUTH") &&
      log[:event_type] == :login_failure
    },
    repeated_auth_failure: ->(logs) {
      logs.count { |log|
        log[:event_code] == "AUTH002" &&
        Time.parse(log[:timestamp]) > 5.minutes.ago
      } >= 5
    },

    # Access control patterns
    unauthorized_access: ->(log) {
      log[:event_code]&.start_with?("ACL") &&
      log[:event_type] == :unauthorized_access
    },
    privilege_escalation: ->(log) {
      log[:event_code] == "ACL004"
    },

    # Data access patterns
    sensitive_data_access: ->(log) {
      log[:event_code]&.start_with?("DAT") &&
      [ :sensitive_read, :sensitive_write ].include?(log[:event_type])
    },
    bulk_data_access: ->(log) {
      log[:event_code] == "DAT003"
    },

    # Security incident patterns
    attack_pattern: ->(log) {
      log[:event_code] == "SEC001"
    },
    vulnerability_detected: ->(log) {
      log[:event_code] == "SEC002"
    }
  }

  # Define thresholds for alerts
  Configuration.thresholds = {
    auth_failures: {
      warning: 5,   # 5 failures in 5 minutes
      critical: 10  # 10 failures in 5 minutes
    },
    unauthorized_access: {
      warning: 3,   # 3 attempts in 5 minutes
      critical: 5   # 5 attempts in 5 minutes
    },
    sensitive_data: {
      warning: 10,  # 10 sensitive data accesses in 1 minute
      critical: 20  # 20 sensitive data accesses in 1 minute
    }
  }

  # Define analysis rules
  Configuration.rules = {
    # Authentication rules
    auth_failure_detection: {
      pattern: :auth_failure,
      window: 5.minutes,
      threshold: 5,
      action: :alert_auth_failures
    },

    # Access control rules
    unauthorized_access_detection: {
      pattern: :unauthorized_access,
      window: 5.minutes,
      threshold: 3,
      action: :alert_unauthorized_access
    },

    # Data access rules
    sensitive_data_monitoring: {
      pattern: :sensitive_data_access,
      window: 1.minute,
      threshold: 10,
      action: :alert_sensitive_data_access
    },

    # Attack detection rules
    attack_detection: {
      pattern: :attack_pattern,
      window: 1.minute,
      threshold: 1,
      action: :alert_attack_detected
    }
  }

  # Define actions to take when rules are triggered
  Configuration.actions = {
    alert_auth_failures: ->(events) {
      SecurityMonitoring.alert({
        type: :authentication,
        severity: events.size >= 10 ? :critical : :warning,
        message: "Multiple authentication failures detected",
        count: events.size,
        details: events.map { |e| {
          ip: e[:ip_address],
          timestamp: e[:timestamp]
        }}
      })
    },

    alert_unauthorized_access: ->(events) {
      SecurityMonitoring.alert({
        type: :access_control,
        severity: events.size >= 5 ? :critical : :warning,
        message: "Multiple unauthorized access attempts detected",
        count: events.size,
        details: events.map { |e| {
          resource: e[:details][:resource],
          user: e[:user_id],
          ip: e[:ip_address]
        }}
      })
    },

    alert_sensitive_data_access: ->(events) {
      SecurityMonitoring.alert({
        type: :data_access,
        severity: events.size >= 20 ? :critical : :warning,
        message: "High volume of sensitive data access detected",
        count: events.size,
        details: events.map { |e| {
          data_type: e[:details][:data_type],
          user: e[:user_id],
          operation: e[:details][:operation]
        }}
      })
    },

    alert_attack_detected: ->(events) {
      SecurityMonitoring.alert({
        type: :security,
        severity: :critical,
        message: "Potential security attack detected",
        count: events.size,
        details: events.map { |e| {
          attack_type: e[:details][:attack_type],
          source: e[:ip_address],
          target: e[:details][:target]
        }}
      })
    }
  }

  class Analyzer
    def self.analyze_logs(logs)
      logs.each do |log|
        analyze_single_log(log)
        analyze_patterns(logs)
      end
    end

    def self.analyze_single_log(log)
      Configuration.rules.each do |rule_name, rule|
        if Configuration.patterns[rule[:pattern]].call(log)
          trigger_action(rule[:action], [ log ])
        end
      end
    end

    def self.analyze_patterns(logs)
      Configuration.rules.each do |rule_name, rule|
        matching_logs = logs.select { |log|
          Time.parse(log[:timestamp]) > rule[:window].ago &&
          Configuration.patterns[rule[:pattern]].call(log)
        }

        if matching_logs.size >= rule[:threshold]
          trigger_action(rule[:action], matching_logs)
        end
      end
    end

    def self.trigger_action(action_name, events)
      Configuration.actions[action_name].call(events)
    end
  end

  # Initialize log analysis system
  def self.start
    # Set up log file monitoring
    log_path = Rails.root.join("log", "security_#{Rails.env}.log")

    Thread.new do
      File.open(log_path) do |log|
        log.extend(File::Tail)
        log.interval = 5
        log.backward(1)
        log.tail do |line|
          begin
            entry = JSON.parse(line, symbolize_names: true)
            Analyzer.analyze_logs([ entry ])
          rescue JSON::ParserError => e
            Rails.logger.error "Failed to parse security log entry: #{e.message}"
          end
        end
      end
    end
  end
end

# Start log analysis when Rails boots
Rails.application.config.after_initialize do
  LogAnalysis.start if Rails.env.production?
end
