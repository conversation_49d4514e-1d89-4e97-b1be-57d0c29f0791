Rails.application.routes.draw do
  devise_for :users,
             controllers: {
               sessions: "api/sessions",
               registrations: "api/registrations"
             },
             defaults: { format: :json }

  namespace :api do
    # Guest accessible routes
    resources :beats, only: [ :index, :show ]
    resources :freestyle_sessions, only: [ :create ]

    # Authenticated user routes
    authenticate :user do
      resources :beats, only: [ :create, :update, :destroy ]
      resources :freestyle_sessions, only: [ :index, :show, :update, :destroy ] do
        # Export endpoint
        post 'export', to: 'exports#create'
      end
      resource :subscription, only: [ :show, :create, :destroy ]
    end

    get "profile", to: "users#profile"
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"

  # Serve test beats as static files
  get "/api/test-beats/*filename", to: proc { |env|
    filename = env["action_dispatch.request.path_parameters"][:filename]
    path = Rails.root.join("test_beats", filename)
    if File.exist?(path)
      [ 200, { "Content-Type" => "audio/wav" }, [ File.binread(path) ] ]
    else
      [ 404, { "Content-Type" => "text/plain" }, [ "Not found" ] ]
    end
  }
end
