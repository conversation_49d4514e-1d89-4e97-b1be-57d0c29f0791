<!DOCTYPE html>
<html xmlns='http://www.w3.org/1999/xhtml'>
  <head>
    <title>Code coverage for Backend</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <script src='./assets/0.13.1/application.js' type='text/javascript'></script>
    <link href='./assets/0.13.1/application.css' media='screen, projection, print' rel='stylesheet' type='text/css' />
    <link rel="icon" type="image/png" href="./assets/0.13.1/favicon_red.png" />
  </head>

  <body>
    <div id="loading">
      <img src="./assets/0.13.1/loading.gif" alt="loading"/>
    </div>
    <div id="wrapper" class="hide">
      <div class="timestamp">Generated <abbr class="timeago" title="2025-04-22T16:23:48-06:00">2025-04-22T16:23:48-06:00</abbr></div>
      <ul class="group_tabs"></ul>

      <div id="content">
        <div class="file_list_container" id="AllFiles">
  <h2>
    <span class="group_name">All Files</span>
    (<span class="covered_percent">
      <span class="red">
  15.96%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.18
       </span>
    </span> hits/line
    )
  </h2>

  <a name="AllFiles"></a>

  <div>
    <b>21</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>451</b> relevant lines,
    <span class="green"><b>72</b> lines covered</span> and
    <span class="red"><b>379</b> lines missed. </span>
    (<span class="red">
  15.96%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#badcef6bbdfc777ba3d396613d2ab13c46e4f75d" class="src_link" title="app/controllers/api/ai_suggestions_controller.rb">app/controllers/api/ai_suggestions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">40.74 %</td>
            <td class="cell--number">50</td>
            <td class="cell--number">27</td>
            <td class="cell--number">11</td>
            <td class="cell--number">16</td>
            <td class="cell--number">0.41</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#5f5d81613499c5de85307b5773aab75a52c3ad06" class="src_link" title="app/controllers/api/base_controller.rb">app/controllers/api/base_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">54.55 %</td>
            <td class="cell--number">43</td>
            <td class="cell--number">22</td>
            <td class="cell--number">12</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0.55</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f62480d778febcca72c7daa04262a86d9898884d" class="src_link" title="app/controllers/api/beats_controller.rb">app/controllers/api/beats_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">72</td>
            <td class="cell--number">61</td>
            <td class="cell--number">0</td>
            <td class="cell--number">61</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#97c1ab319d139e4b7a054ef95ae410f0f959ce47" class="src_link" title="app/controllers/api/freestyle_sessions_controller.rb">app/controllers/api/freestyle_sessions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">64</td>
            <td class="cell--number">53</td>
            <td class="cell--number">0</td>
            <td class="cell--number">53</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#dd35d41544a2dbf2ac4623cf6f59954eafa5d0e2" class="src_link" title="app/controllers/api/registrations_controller.rb">app/controllers/api/registrations_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">29</td>
            <td class="cell--number">24</td>
            <td class="cell--number">0</td>
            <td class="cell--number">24</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#467d5d489af57901313a63855eaee9fcdc71cbc3" class="src_link" title="app/controllers/api/sessions_controller.rb">app/controllers/api/sessions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">34</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#212b141cffc324417b53306876f901745f797db3" class="src_link" title="app/controllers/api/subscriptions_controller.rb">app/controllers/api/subscriptions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">33</td>
            <td class="cell--number">27</td>
            <td class="cell--number">0</td>
            <td class="cell--number">27</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#db997d515d31e5490d5617fa44e29a461109b049" class="src_link" title="app/controllers/api/suggestions_controller.rb">app/controllers/api/suggestions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">51</td>
            <td class="cell--number">41</td>
            <td class="cell--number">0</td>
            <td class="cell--number">41</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#8b86c773c54d3ab379c20560a9f93cc2f818ab67" class="src_link" title="app/controllers/application_controller.rb">app/controllers/application_controller.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">2</td>
            <td class="cell--number">1</td>
            <td class="cell--number">1</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#27cb6e30bf20d1cf75585ca912044564f07ee1bd" class="src_link" title="app/jobs/application_job.rb">app/jobs/application_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#9239cfa47ba7889c7f5c8b0283fb4feeee69b14c" class="src_link" title="app/mailers/application_mailer.rb">app/mailers/application_mailer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#aa995e6463f2fd65d40f691a2e5ae470a036451c" class="src_link" title="app/models/ai_suggestion.rb">app/models/ai_suggestion.rb</a></td>
            <td class="red strong cell--number t-file__coverage">71.43 %</td>
            <td class="cell--number">21</td>
            <td class="cell--number">14</td>
            <td class="cell--number">10</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.71</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#2edac47bf85e787a574fe7a981f262960512c345" class="src_link" title="app/models/application_record.rb">app/models/application_record.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">3</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a9187ae84b64ea510f9a8bde8b25f0eb3caa6d89" class="src_link" title="app/models/beat.rb">app/models/beat.rb</a></td>
            <td class="red strong cell--number t-file__coverage">77.78 %</td>
            <td class="cell--number">41</td>
            <td class="cell--number">27</td>
            <td class="cell--number">21</td>
            <td class="cell--number">6</td>
            <td class="cell--number">1.15</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#420072d2679112059bb407cfa5f87969f98a49d9" class="src_link" title="app/models/freestyle_session.rb">app/models/freestyle_session.rb</a></td>
            <td class="red strong cell--number t-file__coverage">73.91 %</td>
            <td class="cell--number">37</td>
            <td class="cell--number">23</td>
            <td class="cell--number">17</td>
            <td class="cell--number">6</td>
            <td class="cell--number">0.74</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#93d7265f9f59ddcddefa3f4eb5cd0b9348b56674" class="src_link" title="app/models/subscription.rb">app/models/subscription.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">36</td>
            <td class="cell--number">28</td>
            <td class="cell--number">0</td>
            <td class="cell--number">28</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#4aaf51882c6537b346cae3e4ff25626a1e590e32" class="src_link" title="app/models/user.rb">app/models/user.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">25</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#3051cc71f79c94eb4f602b4f7c35d15311570700" class="src_link" title="app/serializers/ai_suggestion_serializer.rb">app/serializers/ai_suggestion_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">9</td>
            <td class="cell--number">7</td>
            <td class="cell--number">0</td>
            <td class="cell--number">7</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a81343af2696d63b3f98d833eb9608b365a91ae2" class="src_link" title="app/serializers/beat_serializer.rb">app/serializers/beat_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">21</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#4ce59640c70d1efa8d5c848ffeb56a7c8ec3a261" class="src_link" title="app/serializers/freestyle_session_serializer.rb">app/serializers/freestyle_session_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">15</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#0f33319a0a42e61fec82ad463e48d11c7713b182" class="src_link" title="app/serializers/user_serializer.rb">app/serializers/user_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">13</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>


        
          <div class="file_list_container" id="Controllers">
  <h2>
    <span class="group_name">Controllers</span>
    (<span class="covered_percent">
      <span class="red">
  8.42%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.08
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Controllers"></a>

  <div>
    <b>9</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>285</b> relevant lines,
    <span class="green"><b>24</b> lines covered</span> and
    <span class="red"><b>261</b> lines missed. </span>
    (<span class="red">
  8.42%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#badcef6bbdfc777ba3d396613d2ab13c46e4f75d" class="src_link" title="app/controllers/api/ai_suggestions_controller.rb">app/controllers/api/ai_suggestions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">40.74 %</td>
            <td class="cell--number">50</td>
            <td class="cell--number">27</td>
            <td class="cell--number">11</td>
            <td class="cell--number">16</td>
            <td class="cell--number">0.41</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#5f5d81613499c5de85307b5773aab75a52c3ad06" class="src_link" title="app/controllers/api/base_controller.rb">app/controllers/api/base_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">54.55 %</td>
            <td class="cell--number">43</td>
            <td class="cell--number">22</td>
            <td class="cell--number">12</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0.55</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f62480d778febcca72c7daa04262a86d9898884d" class="src_link" title="app/controllers/api/beats_controller.rb">app/controllers/api/beats_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">72</td>
            <td class="cell--number">61</td>
            <td class="cell--number">0</td>
            <td class="cell--number">61</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#97c1ab319d139e4b7a054ef95ae410f0f959ce47" class="src_link" title="app/controllers/api/freestyle_sessions_controller.rb">app/controllers/api/freestyle_sessions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">64</td>
            <td class="cell--number">53</td>
            <td class="cell--number">0</td>
            <td class="cell--number">53</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#dd35d41544a2dbf2ac4623cf6f59954eafa5d0e2" class="src_link" title="app/controllers/api/registrations_controller.rb">app/controllers/api/registrations_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">29</td>
            <td class="cell--number">24</td>
            <td class="cell--number">0</td>
            <td class="cell--number">24</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#467d5d489af57901313a63855eaee9fcdc71cbc3" class="src_link" title="app/controllers/api/sessions_controller.rb">app/controllers/api/sessions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">34</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#212b141cffc324417b53306876f901745f797db3" class="src_link" title="app/controllers/api/subscriptions_controller.rb">app/controllers/api/subscriptions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">33</td>
            <td class="cell--number">27</td>
            <td class="cell--number">0</td>
            <td class="cell--number">27</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#db997d515d31e5490d5617fa44e29a461109b049" class="src_link" title="app/controllers/api/suggestions_controller.rb">app/controllers/api/suggestions_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">51</td>
            <td class="cell--number">41</td>
            <td class="cell--number">0</td>
            <td class="cell--number">41</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#8b86c773c54d3ab379c20560a9f93cc2f818ab67" class="src_link" title="app/controllers/application_controller.rb">app/controllers/application_controller.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">2</td>
            <td class="cell--number">1</td>
            <td class="cell--number">1</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Channels">
  <h2>
    <span class="group_name">Channels</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Channels"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Models">
  <h2>
    <span class="group_name">Models</span>
    (<span class="covered_percent">
      <span class="red">
  42.48%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.51
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Models"></a>

  <div>
    <b>6</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>113</b> relevant lines,
    <span class="green"><b>48</b> lines covered</span> and
    <span class="red"><b>65</b> lines missed. </span>
    (<span class="red">
  42.48%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#aa995e6463f2fd65d40f691a2e5ae470a036451c" class="src_link" title="app/models/ai_suggestion.rb">app/models/ai_suggestion.rb</a></td>
            <td class="red strong cell--number t-file__coverage">71.43 %</td>
            <td class="cell--number">21</td>
            <td class="cell--number">14</td>
            <td class="cell--number">10</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.71</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#2edac47bf85e787a574fe7a981f262960512c345" class="src_link" title="app/models/application_record.rb">app/models/application_record.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">3</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a9187ae84b64ea510f9a8bde8b25f0eb3caa6d89" class="src_link" title="app/models/beat.rb">app/models/beat.rb</a></td>
            <td class="red strong cell--number t-file__coverage">77.78 %</td>
            <td class="cell--number">41</td>
            <td class="cell--number">27</td>
            <td class="cell--number">21</td>
            <td class="cell--number">6</td>
            <td class="cell--number">1.15</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#420072d2679112059bb407cfa5f87969f98a49d9" class="src_link" title="app/models/freestyle_session.rb">app/models/freestyle_session.rb</a></td>
            <td class="red strong cell--number t-file__coverage">73.91 %</td>
            <td class="cell--number">37</td>
            <td class="cell--number">23</td>
            <td class="cell--number">17</td>
            <td class="cell--number">6</td>
            <td class="cell--number">0.74</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#93d7265f9f59ddcddefa3f4eb5cd0b9348b56674" class="src_link" title="app/models/subscription.rb">app/models/subscription.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">36</td>
            <td class="cell--number">28</td>
            <td class="cell--number">0</td>
            <td class="cell--number">28</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#4aaf51882c6537b346cae3e4ff25626a1e590e32" class="src_link" title="app/models/user.rb">app/models/user.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">25</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Mailers">
  <h2>
    <span class="group_name">Mailers</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Mailers"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>4</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>4</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#9239cfa47ba7889c7f5c8b0283fb4feeee69b14c" class="src_link" title="app/mailers/application_mailer.rb">app/mailers/application_mailer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Helpers">
  <h2>
    <span class="group_name">Helpers</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Helpers"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Jobs">
  <h2>
    <span class="group_name">Jobs</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Jobs"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>2</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>2</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#27cb6e30bf20d1cf75585ca912044564f07ee1bd" class="src_link" title="app/jobs/application_job.rb">app/jobs/application_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Libraries">
  <h2>
    <span class="group_name">Libraries</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Libraries"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Services">
  <h2>
    <span class="group_name">Services</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Services"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Ungrouped">
  <h2>
    <span class="group_name">Ungrouped</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Ungrouped"></a>

  <div>
    <b>4</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>47</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>47</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#3051cc71f79c94eb4f602b4f7c35d15311570700" class="src_link" title="app/serializers/ai_suggestion_serializer.rb">app/serializers/ai_suggestion_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">9</td>
            <td class="cell--number">7</td>
            <td class="cell--number">0</td>
            <td class="cell--number">7</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a81343af2696d63b3f98d833eb9608b365a91ae2" class="src_link" title="app/serializers/beat_serializer.rb">app/serializers/beat_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">21</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#4ce59640c70d1efa8d5c848ffeb56a7c8ec3a261" class="src_link" title="app/serializers/freestyle_session_serializer.rb">app/serializers/freestyle_session_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">15</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#0f33319a0a42e61fec82ad463e48d11c7713b182" class="src_link" title="app/serializers/user_serializer.rb">app/serializers/user_serializer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">13</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
      </div>

      <div id="footer">
        Generated by <a href="https://github.com/simplecov-ruby/simplecov">simplecov</a> v0.22.0
        and simplecov-html v0.13.1<br/>
        using RSpec
      </div>

      <div class="source_files">
      
        <div class="source_table" id="badcef6bbdfc777ba3d396613d2ab13c46e4f75d">
  <div class="header">
    <h3>app/controllers/api/ai_suggestions_controller.rb</h3>
    <h4>
      <span class="red">
  40.74%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>27</b> relevant lines.
      <span class="green"><b>11</b> lines covered</span> and
      <span class="red"><b>16</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  class AiSuggestionsController &lt; BaseController</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="3">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    before_action :set_freestyle_session</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="4">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    before_action :check_suggestion_limits, only: [:create]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="6">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">      suggestions = @freestyle_session.ai_suggestions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      render json: suggestions</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="11">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      suggestion = @freestyle_session.ai_suggestions.new(ai_suggestion_params)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">      </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      if suggestion.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">        render json: suggestion, status: :created</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">        render json: { errors: suggestion.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="21">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      suggestion = @freestyle_session.ai_suggestions.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">      if suggestion.update(ai_suggestion_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">        render json: suggestion</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">        render json: { errors: suggestion.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="30">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="32">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def set_freestyle_session</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">      @freestyle_session = current_user.freestyle_sessions.find(params[:freestyle_session_id])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="36">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def ai_suggestion_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">      params.require(:ai_suggestion).permit(:content, :timestamp, :used)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="40">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def check_suggestion_limits</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">      return if current_user.premium?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">      daily_count = @freestyle_session.ai_suggestions.where(&#39;created_at &gt;= ?&#39;, Time.current.beginning_of_day).count</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby">      </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">      if daily_count &gt;= 5</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">        render json: { error: &#39;Daily suggestion limit reached&#39; }, status: :forbidden</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="50">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="5f5d81613499c5de85307b5773aab75a52c3ad06">
  <div class="header">
    <h3>app/controllers/api/base_controller.rb</h3>
    <h4>
      <span class="red">
  54.55%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>22</b> relevant lines.
      <span class="green"><b>12</b> lines covered</span> and
      <span class="red"><b>10</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  class BaseController &lt; ApplicationController</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="3">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    before_action :set_current_user</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="5">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    rescue_from ActiveRecord::RecordNotFound, with: :not_found</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="6">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    rescue_from ActiveRecord::RecordInvalid, with: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="8">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="10">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def set_current_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">      if request.headers[&#39;Authorization&#39;].present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">        authenticate_user!</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">        # Allow guest access</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">        @current_user = nil</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="19">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def require_authentication!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      unless current_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">        render json: { error: &#39;Authentication required&#39; }, status: :unauthorized</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="25">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def require_premium!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      unless current_user&amp;.premium?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">        render json: { error: &#39;Premium subscription required&#39; }, status: :forbidden</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="31">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def current_ability</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">      @current_ability ||= Ability.new(current_user)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="35">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def not_found</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">      render json: { error: &#39;Resource not found&#39; }, status: :not_found</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="39">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def unprocessable_entity(exception)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">      render json: { errors: exception.record.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="43">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="f62480d778febcca72c7daa04262a86d9898884d">
  <div class="header">
    <h3>app/controllers/api/beats_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>61</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>61</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class BeatsController &lt; BaseController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    before_action :set_beat, only: [:show, :update, :destroy]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">    before_action :require_premium!, only: [:create, :update, :destroy]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">      @beats = if params[:premium] &amp;&amp; current_user&amp;.premium?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">        Beat.premium.active</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        Beat.free.active</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">      @beats = @beats.where(genre: params[:genre]) if params[:genre].present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      @beats = @beats.where(key: params[:key]) if params[:key].present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      @beats = @beats.where(&#39;bpm &gt;= ?&#39;, params[:min_bpm]) if params[:min_bpm].present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      @beats = @beats.where(&#39;bpm &lt;= ?&#39;, params[:max_bpm]) if params[:max_bpm].present?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      render json: BeatSerializer.new(@beats).serializable_hash</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    def show</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      if @beat.is_free || current_user&amp;.premium?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">        render json: BeatSerializer.new(@beat).serializable_hash</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">        render json: { error: &#39;Premium subscription required to access this beat&#39; }, status: :forbidden</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">      @beat = Beat.new(beat_params)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">      if @beat.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">        render json: BeatSerializer.new(@beat).serializable_hash, status: :created</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">        render json: { errors: @beat.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">    def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">      if @beat.update(beat_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">        render json: BeatSerializer.new(@beat).serializable_hash</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">        render json: { errors: @beat.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="46">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">    def destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">      @beat.destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">      head :no_content</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">    def set_beat</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="55">
            

            

            <code class="ruby">      @beat = Beat.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="57">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">    def beat_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="59">
            

            

            <code class="ruby">      params.require(:beat).permit(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="60">
            

            

            <code class="ruby">        :title,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">        :genre,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">        :bpm,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="63">
            

            

            <code class="ruby">        :key,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="64">
            

            

            <code class="ruby">        :mood,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="65">
            

            

            <code class="ruby">        :is_free,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="66">
            

            

            <code class="ruby">        :description,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="67">
            

            

            <code class="ruby">        :producer_name,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="68">
            

            

            <code class="ruby">        :audio_file</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="69">
            

            

            <code class="ruby">      )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="70">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="71">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="72">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="97c1ab319d139e4b7a054ef95ae410f0f959ce47">
  <div class="header">
    <h3>app/controllers/api/freestyle_sessions_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>53</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>53</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class FreestyleSessionsController &lt; BaseController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    before_action :set_freestyle_session, only: [:show, :update, :destroy]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">    before_action :require_authentication!, except: [:create]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    before_action :require_premium!, only: [:index, :show]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      @sessions = current_user.freestyle_sessions.recent</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">      render json: FreestyleSessionSerializer.new(@sessions).serializable_hash</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    def show</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">      if @freestyle_session.user == current_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">        render json: FreestyleSessionSerializer.new(@freestyle_session).serializable_hash</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">        render json: { error: &#39;Not authorized to view this session&#39; }, status: :forbidden</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">      @freestyle_session = FreestyleSession.new(freestyle_session_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      @freestyle_session.user = current_user if current_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">      @freestyle_session.status = &#39;completed&#39;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">      # Attach the recording if provided</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      if params[:recording].present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">        @freestyle_session.recording.attach(params[:recording])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">      if @freestyle_session.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">        render json: FreestyleSessionSerializer.new(@freestyle_session).serializable_hash, status: :created</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">        render json: { errors: @freestyle_session.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">    def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">      if @freestyle_session.update(freestyle_session_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">        render json: @freestyle_session</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">        render json: { errors: @freestyle_session.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">    def destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">      if @freestyle_session.user == current_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">        @freestyle_session.destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">        head :no_content</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">        render json: { error: &#39;Not authorized to delete this session&#39; }, status: :forbidden</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">    def set_freestyle_session</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">      @freestyle_session = FreestyleSession.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="60">
            

            

            <code class="ruby">    def freestyle_session_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">      params.permit(:beat_id, :duration, :recording)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="63">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="64">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="dd35d41544a2dbf2ac4623cf6f59954eafa5d0e2">
  <div class="header">
    <h3>app/controllers/api/registrations_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>24</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>24</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class RegistrationsController &lt; Devise::RegistrationsController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    respond_to :json</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      build_resource(sign_up_params)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      if resource.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        sign_in(resource)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        render json: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">          user: UserSerializer.new(resource).as_json,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">          token: current_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">        render json: { errors: resource.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    def sign_up_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      params.require(:user).permit(:email, :password, :password_confirmation, :username)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    def current_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      request.env[&#39;warden-jwt_auth.token&#39;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="467d5d489af57901313a63855eaee9fcdc71cbc3">
  <div class="header">
    <h3>app/controllers/api/sessions_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>29</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>29</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class SessionsController &lt; Devise::SessionsController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    respond_to :json</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">    skip_before_action :verify_signed_out_user, only: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">      user = User.find_by(email: sign_in_params[:email])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      if user&amp;.valid_password?(sign_in_params[:password])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        sign_in(user)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        render json: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">          user: UserSerializer.new(user).as_json,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">          token: current_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">        render json: { error: &#39;Invalid email or password&#39; }, status: :unauthorized</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    def destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      sign_out(current_user)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">      render json: { message: &#39;Logged out successfully&#39; }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">    def current_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">      request.env[&#39;warden-jwt_auth.token&#39;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    def respond_to_on_destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">      head :no_content</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="212b141cffc324417b53306876f901745f797db3">
  <div class="header">
    <h3>app/controllers/api/subscriptions_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>27</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>27</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class SubscriptionsController &lt; BaseController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    def show</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      render json: current_user.subscription</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      subscription = current_user.build_subscription(subscription_params)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">      </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      if subscription.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">        render json: subscription, status: :created</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        render json: { errors: subscription.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      subscription = current_user.subscription</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">      </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      if subscription.update(subscription_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">        render json: subscription</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">        render json: { errors: subscription.errors.full_messages }, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    def subscription_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">      params.require(:subscription).permit(:plan_type, :status, :starts_at, :ends_at)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="db997d515d31e5490d5617fa44e29a461109b049">
  <div class="header">
    <h3>app/controllers/api/suggestions_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>41</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>41</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class SuggestionsController &lt; BaseController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    before_action :check_suggestion_limit, only: [:create]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    def create</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">      # TODO: Integrate with actual AI service</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">      # For now, return placeholder suggestions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      suggestion = generate_placeholder_suggestion</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      if current_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">        ai_suggestion = current_user.ai_suggestions.create!(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">          content: suggestion,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">          freestyle_session_id: params[:freestyle_session_id]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">        )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">        render json: AISuggestionSerializer.new(ai_suggestion).serializable_hash, status: :created</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">        render json: { suggestion: suggestion }, status: :created</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    def check_suggestion_limit</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      return if current_user&amp;.premium?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">      return if !current_user # Allow guest users some suggestions</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">      suggestions_today = current_user.ai_suggestions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">        .where(&#39;created_at &gt;= ?&#39;, Time.current.beginning_of_day)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">        .count</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">      if suggestions_today &gt;= 5</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">        render json: { error: &#39;Daily suggestion limit reached&#39; }, status: :forbidden</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">    def generate_placeholder_suggestion</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">      suggestions = [</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">        &quot;Keep the flow going, you&#39;re doing great!&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">        &quot;Talk about your journey and experiences&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">        &quot;Paint a picture with your words&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">        &quot;Share your dreams and aspirations&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">        &quot;Describe the world around you&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">        &quot;Tell a story about overcoming challenges&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">        &quot;Express your feelings about life&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">        &quot;Freestyle about your passion for music&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">      ]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">      suggestions.sample</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="8b86c773c54d3ab379c20560a9f93cc2f818ab67">
  <div class="header">
    <h3>app/controllers/application_controller.rb</h3>
    <h4>
      <span class="green">
  100.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>1</b> relevant lines.
      <span class="green"><b>1</b> lines covered</span> and
      <span class="red"><b>0</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class ApplicationController &lt; ActionController::API</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="27cb6e30bf20d1cf75585ca912044564f07ee1bd">
  <div class="header">
    <h3>app/jobs/application_job.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>2</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>2</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationJob &lt; ActiveJob::Base</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Automatically retry jobs that encountered a deadlock</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  # retry_on ActiveRecord::Deadlocked</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">  # Most jobs are safe to ignore if the underlying records are no longer available</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">  # discard_on ActiveJob::DeserializationError</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="9239cfa47ba7889c7f5c8b0283fb4feeee69b14c">
  <div class="header">
    <h3>app/mailers/application_mailer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationMailer &lt; ActionMailer::Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  default from: &quot;<EMAIL>&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  layout &quot;mailer&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="aa995e6463f2fd65d40f691a2e5ae470a036451c">
  <div class="header">
    <h3>app/models/ai_suggestion.rb</h3>
    <h4>
      <span class="red">
  71.43%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>14</b> relevant lines.
      <span class="green"><b>10</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class AiSuggestion &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :freestyle_session</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="4">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :freestyle_session, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="5">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :content, presence: true, length: { in: 2..200 }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="6">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :timestamp, presence: true, numericality: { greater_than_or_equal_to: 0 }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="8">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :used, -&gt; { where(used: true) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="9">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :unused, -&gt; { where(used: false) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="10">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :ordered_by_timestamp, -&gt; { order(:timestamp) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="12">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def mark_as_used!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">    update!(used: true)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="16">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def timestamp_in_minutes</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    minutes = timestamp / 60</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    seconds = timestamp % 60</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    format(&#39;%d:%02d&#39;, minutes, seconds)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="2edac47bf85e787a574fe7a981f262960512c345">
  <div class="header">
    <h3>app/models/application_record.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>3</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>3</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationRecord &lt; ActiveRecord::Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  primary_abstract_class</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="a9187ae84b64ea510f9a8bde8b25f0eb3caa6d89">
  <div class="header">
    <h3>app/models/beat.rb</h3>
    <h4>
      <span class="red">
  77.78%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>27</b> relevant lines.
      <span class="green"><b>21</b> lines covered</span> and
      <span class="red"><b>6</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class Beat &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_many :freestyle_sessions, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="3">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_many :users, through: :freestyle_sessions</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="4">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_one_attached :audio_file</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="6">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :title, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="7">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :genre, presence: true, inclusion: { in: %w[hip-hop trap boom-bap lo-fi] }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="8">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :bpm, presence: true, numericality: { only_integer: true, greater_than: 0 }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="9">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :key, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="10">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :status, presence: true, inclusion: { in: %w[draft active inactive] }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="11">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :complexity, numericality: { only_integer: true, greater_than: 0, less_than_or_equal_to: 5 }, allow_nil: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="12">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :source_type, presence: true, inclusion: { in: %w[original uploaded] }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="13">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :audio_file, presence: true, if: :active?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="14">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :is_free, inclusion: { in: [true, false] }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="16">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :active, -&gt; { where(status: &#39;active&#39;) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="17">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :free, -&gt; { where(is_free: true) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="18">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :premium, -&gt; { where(is_free: false) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="20">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def duration</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    return nil unless audio_file.attached?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">    # TODO: Implement audio duration calculation using audio processing library</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby">    # For now, return a placeholder</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    180 # 3 minutes</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="28">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def audio_url</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    return nil unless audio_file.attached?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    Rails.application.routes.url_helpers.rails_blob_url(audio_file, only_path: true)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="33">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def toggle_status</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">    new_status = status == &#39;active&#39; ? &#39;inactive&#39; : &#39;active&#39;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    update!(status: new_status)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="38">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def active?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="11" data-linenumber="39">
            
              <span class="hits">11</span>
            

            

            <code class="ruby">    status == &#39;active&#39;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="420072d2679112059bb407cfa5f87969f98a49d9">
  <div class="header">
    <h3>app/models/freestyle_session.rb</h3>
    <h4>
      <span class="red">
  73.91%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>23</b> relevant lines.
      <span class="green"><b>17</b> lines covered</span> and
      <span class="red"><b>6</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class FreestyleSession &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :user</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="3">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :beat</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="4">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_many :ai_suggestions, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="5">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_one_attached :recording</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="7">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :user, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="8">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :beat, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="9">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :status, presence: true, inclusion: { in: %w[in_progress completed] }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="10">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :duration, numericality: { greater_than: 0 }, allow_nil: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="12">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :in_progress, -&gt; { where(status: &#39;in_progress&#39;) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="13">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :completed, -&gt; { where(status: &#39;completed&#39;) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="14">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :with_recording, -&gt; { joins(&#39;INNER JOIN active_storage_attachments ON active_storage_attachments.record_id = freestyle_sessions.id&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">                             .where(active_storage_attachments: { record_type: &#39;FreestyleSession&#39;, name: &#39;recording&#39; }) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="17">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def complete!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    update!(status: &#39;completed&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="21">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def duration_in_minutes</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    duration ? (duration.to_f / 60) : nil</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="25">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def suggestion_count</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">    ai_suggestions.count</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="29">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def used_suggestions_count</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    ai_suggestions.used.count</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="33">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def recording_url</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">    return nil unless recording.attached?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    Rails.application.routes.url_helpers.rails_blob_url(recording, only_path: true)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="93d7265f9f59ddcddefa3f4eb5cd0b9348b56674">
  <div class="header">
    <h3>app/models/subscription.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>28</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>28</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Subscription &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  belongs_to :user</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  validates :plan_type, presence: true, inclusion: { in: %w[trial premium] }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  validates :status, presence: true, inclusion: { in: %w[active inactive] }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  validates :starts_at, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  validate :ends_at_after_starts_at, if: -&gt; { ends_at.present? }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  scope :active, -&gt; { where(status: &#39;active&#39;) }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  scope :premium, -&gt; { where(plan_type: &#39;premium&#39;) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  def active?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">    status == &#39;active&#39; &amp;&amp; !expired?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  def expired?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    return false if ends_at.nil?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    ends_at &lt; Time.current</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">  def days_remaining</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    return nil if ends_at.nil?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    return 0 if expired?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    ((ends_at - Time.current) / 1.day).ceil</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">  def ends_at_after_starts_at</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    return if starts_at.nil? || ends_at.nil?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">    if ends_at &lt;= starts_at</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">      errors.add(:ends_at, &#39;must be after the start date&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="4aaf51882c6537b346cae3e4ff25626a1e590e32">
  <div class="header">
    <h3>app/models/user.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>18</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>18</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class User &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Include default devise modules. Others available are:</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  devise :database_authenticatable, :registerable,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">         :recoverable, :rememberable, :validatable</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">         </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  has_many :freestyle_sessions, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  has_many :beats, through: :freestyle_sessions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  has_one :subscription, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  validates :username, presence: true, uniqueness: { case_sensitive: false }, length: { in: 3..20 }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  validates :email, presence: true, uniqueness: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  validates :role, presence: true, inclusion: { in: %w[user admin] }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  before_validation :set_default_role</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  def premium?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    subscription&amp;.active? || false</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">  def set_default_role</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    self.role ||= &#39;user&#39;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="3051cc71f79c94eb4f602b4f7c35d15311570700">
  <div class="header">
    <h3>app/serializers/ai_suggestion_serializer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>7</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>7</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class AISuggestionSerializer</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  include JSONAPI::Serializer</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  attributes :content,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">             :created_at</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  belongs_to :freestyle_session</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  belongs_to :user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="a81343af2696d63b3f98d833eb9608b365a91ae2">
  <div class="header">
    <h3>app/serializers/beat_serializer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>18</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>18</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class BeatSerializer</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  include JSONAPI::Serializer</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  attributes :title,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">             :genre,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">             :bpm,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">             :key,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">             :mood,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">             :description,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">             :producer_name,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">             :is_free,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">             :created_at</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  attribute :audio_url do |beat|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    beat.audio_url</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">  attribute :duration do |beat|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    beat.duration</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="4ce59640c70d1efa8d5c848ffeb56a7c8ec3a261">
  <div class="header">
    <h3>app/serializers/freestyle_session_serializer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>12</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>12</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class FreestyleSessionSerializer</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  include JSONAPI::Serializer</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  attributes :duration,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">             :status,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">             :created_at</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  attribute :recording_url do |session|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    session.recording_url</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  belongs_to :beat</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  belongs_to :user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  has_many :ai_suggestions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="0f33319a0a42e61fec82ad463e48d11c7713b182">
  <div class="header">
    <h3>app/serializers/user_serializer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>10</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>10</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class UserSerializer</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  include JSONAPI::Serializer</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  attributes :id, :email, :username, :created_at</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  attribute :premium do |user|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    user.premium?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  attribute :subscription do |user|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">    user.subscription.as_json if user.subscription</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">end </code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
      </div>
    </div>
  </body>
</html>
