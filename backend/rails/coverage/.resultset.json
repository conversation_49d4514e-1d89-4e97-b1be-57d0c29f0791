{"RSpec": {"coverage": {"/Users/<USER>/freestyle-app/backend/app/controllers/api/ai_suggestions_controller.rb": {"lines": [1, 1, 1, 1, null, 1, 0, 0, null, null, 1, 0, null, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, null, 0, null, null, null, 1, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, 0, null, 0, 0, null, null, null, null]}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/base_controller.rb": {"lines": [1, 1, 1, null, 1, 1, null, 1, null, 1, 0, 0, null, null, 0, null, null, null, 1, 0, 0, null, null, null, 1, 0, 0, null, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, null]}, "/Users/<USER>/freestyle-app/backend/app/controllers/application_controller.rb": {"lines": [1, null]}, "/Users/<USER>/freestyle-app/backend/app/models/freestyle_session.rb": {"lines": [1, 1, 1, 1, 1, null, 1, 1, 1, 1, null, 1, 1, 1, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, 0, null, null]}, "/Users/<USER>/freestyle-app/backend/app/models/beat.rb": {"lines": [1, 1, 1, 1, null, 1, 1, 1, 1, 1, 1, 1, 1, 1, null, 1, 1, 1, null, 1, 0, null, null, null, 0, null, null, 1, 0, 0, null, null, 1, 0, 0, null, null, 1, 11, null, null]}, "/Users/<USER>/freestyle-app/backend/app/models/ai_suggestion.rb": {"lines": [1, 1, null, 1, 1, 1, null, 1, 1, 1, null, 1, 0, null, null, 1, 0, 0, 0, null, null]}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/beats_controller.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/freestyle_sessions_controller.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/registrations_controller.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/sessions_controller.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/subscriptions_controller.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/controllers/api/suggestions_controller.rb": {"lines": [0, 0, 0, null, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/jobs/application_job.rb": {"lines": [0, null, null, null, null, null, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/mailers/application_mailer.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/models/application_record.rb": {"lines": [0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/models/subscription.rb": {"lines": [0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/models/user.rb": {"lines": [0, null, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/serializers/ai_suggestion_serializer.rb": {"lines": [0, 0, null, 0, 0, null, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/serializers/beat_serializer.rb": {"lines": [0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/serializers/freestyle_session_serializer.rb": {"lines": [0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/freestyle-app/backend/app/serializers/user_serializer.rb": {"lines": [0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}}, "timestamp": 1745360628}}