# Freestyle App

A premium Hip-Hop Golden Era themed freestyle rap creation application with professional audio recording, beat synchronization, and waveform visualization capabilities.

## 🎤 Overview

This application enables users to create freestyle rap recordings over professional beats with studio-quality audio processing. Built with modern web technologies and optimized for performance, it provides a seamless recording experience with real-time audio visualization and precise beat synchronization.

## ✨ Key Features

- **🎵 Professional Audio Recording**: High-quality microphone recording with Web Audio API
- **🎛️ Beat Synchronization**: Precise timing synchronization between vocals and beats
- **📊 Real-time Visualization**: Dynamic waveform visualization and audio level monitoring
- **🎨 Premium UI Design**: Hip-Hop Golden Era themed interface with vinyl record aesthetics
- **⚡ Performance Optimized**: Web Workers and AudioWorklets for smooth performance
- **📱 Responsive Design**: Works seamlessly across desktop and mobile devices
- **🔧 Advanced Controls**: Microphone gain, beat volume, and device selection
- **💾 Export Capabilities**: Download mixed recordings with professional quality

## 🏗️ Architecture

### Frontend Structure
```
frontend/
├── public/                  # Static assets and audio worklets
│   ├── beats/              # Professional beat library
│   ├── syncRecorder.worklet.js    # Audio recording worklet
│   └── audioProcessor.worker.js   # Audio processing worker
├── src/
│   ├── app/                # Next.js 15 app router pages
│   │   ├── session/        # Main recording interface
│   │   └── session/preview/ # Recording preview and playback
│   ├── audio/              # Audio processing system
│   │   ├── components/     # Recording and playback components
│   │   ├── hooks/          # Audio-related React hooks
│   │   ├── services/       # Core audio processing services
│   │   ├── utils/          # Audio utility functions
│   │   ├── workers/        # Web Worker implementations
│   │   └── worklets/       # AudioWorklet processors
│   ├── ui/                 # User interface components
│   │   ├── visualizers/    # Waveform and vinyl record components
│   │   ├── controls/       # Audio controls and sliders
│   │   ├── layout/         # Layout and background components
│   │   └── common/         # Reusable UI components
│   ├── stores/             # Zustand state management
│   │   ├── audioStore.ts   # Recording and playback state
│   │   ├── beatStore.ts    # Beat selection and metadata
│   │   └── previewStore.ts # Preview mode state
│   ├── types/              # TypeScript type definitions
│   └── utils/              # General utility functions
└── context/                # Comprehensive documentation
    ├── components/         # Component documentation
    ├── services/           # Service documentation
    └── diagrams/           # Architecture diagrams
```

### Backend Structure
```
backend/
└── rails/                  # Ruby on Rails API server
    ├── app/
    │   ├── controllers/    # API endpoints for beats and metadata
    │   ├── models/         # Data models for beats and sessions
    │   └── serializers/    # JSON API serializers
    ├── db/                 # Database migrations and seeds
    └── config/             # Rails configuration
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** v18+ (v20+ recommended for optimal performance)
- **Ruby** v3.0+ with Rails v7.0+
- **npm** or **yarn** package manager
- **Modern browser** with Web Audio API support (Chrome, Firefox, Safari, Edge)

### Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd freestyle-app
   npm run install:all
   ```

2. **Start Development Servers**
   ```bash
   npm run dev
   ```
   This starts both frontend (port 3000) and backend (port 3001) servers.

3. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Session Page: http://localhost:3000/session

### Manual Setup

If you prefer to install dependencies separately:

```bash
# Frontend dependencies
cd frontend && npm install

# Backend dependencies
cd backend/rails && bundle install
```

### Environment Configuration

**Frontend** (`.env.local` in `frontend/`):
```env
# Optional: Custom API endpoints
NEXT_PUBLIC_API_URL=http://localhost:3001

# Optional: Analytics configuration
NEXT_PUBLIC_ANALYTICS_ENABLED=false
```

**Backend** (`.env` in `backend/rails/`):
```env
# Database configuration
DATABASE_URL=sqlite3:db/development.sqlite3

# CORS configuration
FRONTEND_URL=http://localhost:3000
```

### Development Commands

```bash
# Start all services
npm run dev

# Start only frontend
npm run dev:frontend

# Start only backend
npm run dev:backend

# Run tests
npm test

# Build for production
cd frontend && npm run build
```

## 🎯 Core Features

### Audio Recording & Processing
- **🎤 High-Quality Recording**: Professional-grade microphone recording with Web Audio API
- **⚡ Real-time Processing**: Web Workers and AudioWorklets for smooth performance
- **🎛️ Precise Synchronization**: Sample-accurate timing between vocals and beats
- **📊 Live Monitoring**: Real-time audio level visualization and feedback
- **🔧 Advanced Controls**: Microphone gain, beat volume, and device selection

### User Interface & Experience
- **🎨 Premium Design**: Hip-Hop Golden Era themed interface with vinyl aesthetics
- **📱 Responsive Layout**: Optimized for desktop and mobile devices
- **🎵 Interactive Components**: Animated vinyl record and microphone visualizations
- **⚙️ Collapsible Controls**: Technical controls panel with audio settings
- **🌊 Waveform Visualization**: Dynamic waveform display for recordings

### Beat Library & Management
- **🎼 Professional Beats**: Curated library of high-quality instrumental tracks
- **🔍 Beat Selection**: Easy browsing and preview of available beats
- **💾 Persistent Settings**: Beat selection and audio settings saved across sessions
- **🎧 Preview Mode**: Listen to beats before recording

### Export & Sharing
- **💾 High-Quality Export**: Download mixed recordings in professional formats
- **🎚️ Audio Processing**: Automatic mixing, compression, and mastering
- **📁 File Management**: Organized export with metadata and timestamps

## 🧪 Testing & Quality Assurance

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test:audio
npm run test:ui
npm run test:integration

# Run tests in watch mode
npm run test:watch
```

### Test Coverage
- **Unit Tests**: Individual component and service testing
- **Integration Tests**: End-to-end workflow testing
- **Performance Tests**: Audio processing and memory usage testing
- **Accessibility Tests**: Screen reader and keyboard navigation testing

## Architecture and Design

### Component Organization

Components are organized by domain and purpose to make it easier to understand and maintain the codebase:

- **Audio Components** (`/audio/components`): Components related to audio recording and playback
- **UI Components** (`/ui`): Visual components organized by purpose:
  - **Visualizers** (`/ui/visualizers`): Visual components like MicVinylIntegrated and Waveform
  - **Controls** (`/ui/controls`): UI controls like GainControl and MicrophoneSelector
  - **Layout** (`/ui/layout`): Layout components like CollapsiblePanel and StudioBackground
  - **Common** (`/ui/common`): Reusable UI components like buttons, forms, etc.

This organization makes it easier to find and work with related components.

### State Management

Zustand is used for state management because:
- It's lightweight and simple
- It works well with TypeScript
- It supports middleware for persistence
- It allows for easy access to state from anywhere in the application

The main stores are:
- **audioStore**: Manages recording state and audio data
- **beatStore**: Manages beat selection and playback
- **previewStore**: Manages preview mode state

### Service Layer

A service layer is used to abstract away complex logic from components:
- **audioProcessor**: Handles audio processing and mixing
- **audioRecorder**: Handles microphone recording
- **AudioEngine**: Manages audio playback and processing

This makes components simpler and more focused on rendering UI.

### Hooks

Custom hooks are used to encapsulate reusable logic:
- **useAudioPlayback**: Manages audio playback
- **useKeyboardShortcuts**: Handles keyboard shortcuts
- **useFocusManagement**: Manages focus between components

## Troubleshooting

### Microphone Issues

Microphone recording requires:
- A secure context (HTTPS or localhost)
- Microphone permissions granted
- A browser that supports the Web Audio API (Chrome, Edge, Safari)

## Recent Refactoring

The codebase has been completely reorganized to improve:

1. **Code Organization**:
   - Created a new directory structure with clear separation of concerns
   - Moved audio components to a dedicated audio directory
   - Organized UI components by purpose (visualizers, controls, layout, common)
   - Simplified component structure and improved naming
   - Removed unused components and legacy code

2. **Audio Processing**:
   - Created dedicated audio services for processing and recording
   - Improved audio mixing for better quality
   - Enhanced waveform visualization
   - Added support for beat selection and persistence

3. **State Management**:
   - Focused on core functionality (audio recording, beat selection, preview)
   - Improved state access patterns
   - Added better type safety
   - Removed subscription and authentication-related state

4. **Visual Design**:
   - Enhanced the vinyl record and microphone visualization
   - Improved waveform appearance and responsiveness
   - Added smooth animations for recording and playback
   - Created a cohesive Hip-Hop Golden Era themed interface

5. **Backend Integration**:
   - Simplified API integration
   - Focused on core beat library functionality
   - Removed unnecessary authentication and subscription features