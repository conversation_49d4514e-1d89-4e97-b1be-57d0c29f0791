# Freestyle App

A premium Hip-Hop Golden Era themed freestyle rap creation application with a visually pleasing interface and professional sounding beats.

## Project Structure

```
freestyle-app/
├── frontend/                # Next.js frontend application
│   ├── public/              # Static assets
│   │   └── beats/           # Beat audio files
│   └── src/                 # Source code
│       ├── app/             # Next.js app router pages
│       ├── audio/           # Audio-related functionality
│       │   ├── components/  # Audio UI components
│       │   ├── hooks/       # Audio-related hooks
│       │   ├── services/    # Audio processing services
│       │   └── utils/       # Audio utility functions
│       ├── ui/              # UI components
│       │   ├── common/      # Common UI components
│       │   ├── controls/    # UI controls (sliders, buttons, etc.)
│       │   ├── layout/      # Layout components
│       │   └── visualizers/ # Visual components (waveforms, etc.)
│       ├── stores/          # Zustand stores
│       ├── types/           # TypeScript type definitions
│       └── utils/           # General utility functions
└── backend/                 # Ruby on Rails API
    └── rails/               # Rails application
        ├── app/             # Rails app
        │   ├── controllers/ # API controllers
        │   ├── models/      # Data models
        │   └── serializers/ # JSON serializers
        └── db/              # Database configuration
```

## Getting Started

### Prerequisites

- Node.js (v18+ recommended)
- <PERSON> (v3.0+) and Rails (v7.0+)
- Yarn or npm

### 1. Install Dependencies

Install all dependencies at once:
```
npm run install:all
```

Or install them separately:
```
# Frontend
cd frontend
npm install

# Backend
cd backend/rails
bundle install

# WebSocket Server
cd backend/websocket
npm install
```

### 2. Environment Variables

**Frontend**: Create a `.env.local` in `frontend/`:
```
# Add any environment variables needed for the frontend
```

### 3. Running the Services

You need to run two services:

1. **Frontend (port 3000)**:
   ```
   cd frontend
   npm run dev
   ```

2. **Backend Rails API (port 3001)**:
   ```
   cd backend/rails
   bundle install
   rails db:setup
   rails s -p 3001
   ```

Alternatively, you can run all services at once using:
```
npm run dev:all
```

## Features

- **Audio Recording**: Record freestyle raps over professional beats
- **Visually Appealing Interface**: Premium Hip-Hop Golden Era themed interface with vinyl record and microphone visualization
- **Waveform Visualization**: Visual representation of audio for playback
- **Beat Selection**: Choose from a library of professional beats
- **Audio Mixing**: Automatically mix your vocals with the selected beat
- **Export**: Export your recordings with the beat mixed in

## Running Tests
```
cd frontend
npm test
```

## Architecture and Design

### Component Organization

Components are organized by domain and purpose to make it easier to understand and maintain the codebase:

- **Audio Components** (`/audio/components`): Components related to audio recording and playback
- **UI Components** (`/ui`): Visual components organized by purpose:
  - **Visualizers** (`/ui/visualizers`): Visual components like MicVinylIntegrated and Waveform
  - **Controls** (`/ui/controls`): UI controls like GainControl and MicrophoneSelector
  - **Layout** (`/ui/layout`): Layout components like CollapsiblePanel and StudioBackground
  - **Common** (`/ui/common`): Reusable UI components like buttons, forms, etc.

This organization makes it easier to find and work with related components.

### State Management

Zustand is used for state management because:
- It's lightweight and simple
- It works well with TypeScript
- It supports middleware for persistence
- It allows for easy access to state from anywhere in the application

The main stores are:
- **audioStore**: Manages recording state and audio data
- **beatStore**: Manages beat selection and playback
- **previewStore**: Manages preview mode state

### Service Layer

A service layer is used to abstract away complex logic from components:
- **audioProcessor**: Handles audio processing and mixing
- **audioRecorder**: Handles microphone recording
- **AudioEngine**: Manages audio playback and processing

This makes components simpler and more focused on rendering UI.

### Hooks

Custom hooks are used to encapsulate reusable logic:
- **useAudioPlayback**: Manages audio playback
- **useKeyboardShortcuts**: Handles keyboard shortcuts
- **useFocusManagement**: Manages focus between components

## Troubleshooting

### Microphone Issues

Microphone recording requires:
- A secure context (HTTPS or localhost)
- Microphone permissions granted
- A browser that supports the Web Audio API (Chrome, Edge, Safari)

## Recent Refactoring

The codebase has been completely reorganized to improve:

1. **Code Organization**:
   - Created a new directory structure with clear separation of concerns
   - Moved audio components to a dedicated audio directory
   - Organized UI components by purpose (visualizers, controls, layout, common)
   - Simplified component structure and improved naming
   - Removed unused components and legacy code

2. **Audio Processing**:
   - Created dedicated audio services for processing and recording
   - Improved audio mixing for better quality
   - Enhanced waveform visualization
   - Added support for beat selection and persistence

3. **State Management**:
   - Focused on core functionality (audio recording, beat selection, preview)
   - Improved state access patterns
   - Added better type safety
   - Removed subscription and authentication-related state

4. **Visual Design**:
   - Enhanced the vinyl record and microphone visualization
   - Improved waveform appearance and responsiveness
   - Added smooth animations for recording and playback
   - Created a cohesive Hip-Hop Golden Era themed interface

5. **Backend Integration**:
   - Simplified API integration
   - Focused on core beat library functionality
   - Removed unnecessary authentication and subscription features